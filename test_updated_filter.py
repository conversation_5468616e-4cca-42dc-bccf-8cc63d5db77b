#!/usr/bin/env python3
"""
Test the updated player filter for ALL seasons 2015-2025
"""

import sqlite3
from wnba_data_collector import WN<PERSON><PERSON><PERSON>prehensiveCollector

def test_updated_filter():
    """Test the updated filter logic"""
    print("TESTING UPDATED FILTER FOR ALL SEASONS 2015-2025")
    print("=" * 60)
    
    collector = WNBAComprehensiveCollector()
    
    try:
        # Test the exact query from the updated method
        cursor = collector.conn.execute("""
            SELECT DISTINCT p.player_id, p.player_name, p.season
            FROM players p
            LEFT JOIN player_game_logs pgl ON p.player_id = pgl.player_id AND p.season = pgl.season
            WHERE pgl.player_id IS NULL
            AND p.season BETWEEN '2015' AND '2025'
            AND (
                -- Players who appear on team rosters (most reliable indicator)
                EXISTS (
                    SELECT 1 FROM team_rosters tr 
                    WHERE tr.player_id = p.player_id AND tr.season = p.season
                )
                OR
                -- Players who have game logs in ANY season (career players)
                EXISTS (
                    SELECT 1 FROM player_game_logs pgl2 
                    WHERE pgl2.player_id = p.player_id
                )
                OR
                -- For recent seasons, include all players as they may become active
                p.season IN ('2024', '2025')
            )
            ORDER BY p.season DESC, p.player_name
        """)
        filtered_players = cursor.fetchall()
        
        # Get total players for comparison
        total_players = collector.conn.execute("""
            SELECT COUNT(*) FROM players 
            WHERE season BETWEEN '2015' AND '2025'
        """).fetchone()[0]
        
        # Break down by season
        cursor = collector.conn.execute("""
            SELECT p.season, 
                   COUNT(*) as total_players,
                   COUNT(CASE WHEN (
                       EXISTS (SELECT 1 FROM team_rosters tr WHERE tr.player_id = p.player_id AND tr.season = p.season)
                       OR EXISTS (SELECT 1 FROM player_game_logs pgl2 WHERE pgl2.player_id = p.player_id)
                       OR p.season IN ('2024', '2025')
                   ) THEN 1 END) as filtered_players
            FROM players p
            WHERE p.season BETWEEN '2015' AND '2025'
            GROUP BY p.season
            ORDER BY p.season
        """)
        
        print(f"FILTER RESULTS BY SEASON:")
        total_filtered = 0
        for season, total, filtered in cursor.fetchall():
            total_filtered += filtered
            percentage = filtered / total * 100 if total > 0 else 0
            print(f"  {season}: {filtered:>4}/{total:>4} players ({percentage:>5.1f}%)")
        
        print(f"\nOVERALL SUMMARY:")
        print(f"  Total players (2015-2025): {total_players:,}")
        print(f"  Filtered players: {len(filtered_players):,}")
        print(f"  Reduction: {(total_players - len(filtered_players))/total_players*100:.1f}%")
        print(f"  Time estimate: ~{len(filtered_players)*6/3600:.1f} hours")
        
        # Check team roster coverage
        roster_coverage = collector.conn.execute("""
            SELECT COUNT(DISTINCT season) as seasons_with_rosters
            FROM team_rosters
            WHERE season BETWEEN '2015' AND '2025'
        """).fetchone()[0]
        
        print(f"\nROSTER DATA COVERAGE:")
        print(f"  Seasons with roster data: {roster_coverage}/11")
        
        if roster_coverage < 11:
            print(f"  ⚠️  Missing roster data for some seasons")
            print(f"     This may affect filtering accuracy")
        
        # Sample of players that will be collected
        print(f"\nSAMPLE PLAYERS TO COLLECT (first 10):")
        for i, (player_id, name, season) in enumerate(filtered_players[:10]):
            print(f"  {season}: {name}")
        
        return len(filtered_players)
        
    finally:
        collector.close()

def main():
    """Main execution"""
    filtered_count = test_updated_filter()
    
    print(f"\n✅ UPDATED FILTER READY")
    print(f"   Will collect data for {filtered_count:,} players across ALL seasons 2015-2025")
    print(f"   Focuses on players who actually played games")
    print(f"   Ready to start comprehensive collection!")

if __name__ == "__main__":
    main()
