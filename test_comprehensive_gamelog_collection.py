#!/usr/bin/env python3
"""
Test Comprehensive WNBA Game Log Collection (Players + Teams)
"""

from wnba_data_collector import WNBAComprehensiveCollector
import pandas as pd
import os

def test_comprehensive_game_log_collection():
    """Test both player and team game log collection functionality"""
    print("🏀 Testing COMPREHENSIVE WNBA Game Log Collection 🏀")
    print("=" * 70)
    
    # Create collector instance
    collector = WNBAComprehensiveCollector()
    
    try:
        # Limit to 2024 for testing
        collector.seasons = ['2024']
        
        print("1. Collecting basic data (teams and players)...")
        collector.collect_teams_data()
        collector.collect_players_data()
        
        # Check what we have
        team_count = collector.conn.execute("SELECT COUNT(*) FROM teams WHERE season = '2024'").fetchone()[0]
        player_count = collector.conn.execute("SELECT COUNT(*) FROM players WHERE season = '2024'").fetchone()[0]
        
        print(f"   Found {team_count} teams and {player_count} players for 2024 season")
        
        if team_count == 0 or player_count == 0:
            print("❌ Insufficient data. Cannot test game log collection.")
            return
        
        # Get sample teams and players
        cursor = collector.conn.execute("""
            SELECT team_id, team_name 
            FROM teams 
            WHERE season = '2024' 
            LIMIT 3
        """)
        sample_teams = cursor.fetchall()
        
        cursor = collector.conn.execute("""
            SELECT player_id, player_name 
            FROM players 
            WHERE season = '2024' 
            LIMIT 3
        """)
        sample_players = cursor.fetchall()
        
        print(f"\n2. Sample teams for testing:")
        for team_id, team_name in sample_teams:
            print(f"   • {team_name} (ID: {team_id})")
            
        print(f"\n3. Sample players for testing:")
        for player_id, player_name in sample_players:
            print(f"   • {player_name} (ID: {player_id})")
        
        # Test player game log collection
        print(f"\n4. Testing PLAYER game log collection...")
        collector.collect_player_game_logs()
        
        player_game_logs = collector.conn.execute("SELECT COUNT(*) FROM player_game_logs").fetchone()[0]
        print(f"   Player game logs collected: {player_game_logs}")
        
        # Test team game log collection
        print(f"\n5. Testing TEAM game log collection...")
        collector.collect_team_game_logs()
        
        team_game_logs = collector.conn.execute("SELECT COUNT(*) FROM team_game_logs").fetchone()[0]
        print(f"   Team game logs collected: {team_game_logs}")
        
        # Analyze the results
        print(f"\n6. ANALYSIS OF COLLECTED DATA:")
        print(f"   {'='*50}")
        
        if player_game_logs > 0:
            print(f"\n   PLAYER GAME LOGS ({player_game_logs} total):")
            
            # Player stats
            cursor = collector.conn.execute("""
                SELECT 
                    COUNT(DISTINCT player_id) as unique_players,
                    COUNT(DISTINCT game_id) as unique_games,
                    AVG(pts) as avg_points,
                    MAX(pts) as max_points,
                    AVG(reb) as avg_rebounds,
                    AVG(ast) as avg_assists
                FROM player_game_logs
            """)
            stats = cursor.fetchone()
            if stats:
                unique_players, unique_games, avg_pts, max_pts, avg_reb, avg_ast = stats
                print(f"     • {unique_players} unique players")
                print(f"     • {unique_games} unique games")
                print(f"     • {avg_pts:.1f} average points per game")
                print(f"     • {max_pts} highest individual score")
                print(f"     • {avg_reb:.1f} average rebounds per game")
                print(f"     • {avg_ast:.1f} average assists per game")
            
            # Top performances
            cursor = collector.conn.execute("""
                SELECT player_name, game_date, matchup, pts, reb, ast
                FROM player_game_logs 
                ORDER BY pts DESC 
                LIMIT 3
            """)
            top_games = cursor.fetchall()
            if top_games:
                print(f"\n     TOP INDIVIDUAL PERFORMANCES:")
                for player_name, game_date, matchup, pts, reb, ast in top_games:
                    print(f"       • {player_name}: {pts}pts, {reb}reb, {ast}ast vs {matchup} ({game_date})")
        
        if team_game_logs > 0:
            print(f"\n   TEAM GAME LOGS ({team_game_logs} total):")
            
            # Team stats
            cursor = collector.conn.execute("""
                SELECT 
                    COUNT(DISTINCT team_id) as unique_teams,
                    COUNT(DISTINCT game_id) as unique_games,
                    AVG(pts) as avg_points,
                    MAX(pts) as max_points,
                    AVG(CAST(w AS FLOAT) / (w + l)) as avg_win_pct
                FROM team_game_logs
                WHERE w IS NOT NULL AND l IS NOT NULL
            """)
            stats = cursor.fetchone()
            if stats:
                unique_teams, unique_games, avg_pts, max_pts, avg_win_pct = stats
                print(f"     • {unique_teams} unique teams")
                print(f"     • {unique_games} unique games")
                print(f"     • {avg_pts:.1f} average points per game")
                print(f"     • {max_pts} highest team score")
                if avg_win_pct:
                    print(f"     • {avg_win_pct:.1%} average win percentage")
            
            # High scoring games
            cursor = collector.conn.execute("""
                SELECT team_name, game_date, matchup, pts, wl
                FROM team_game_logs 
                ORDER BY pts DESC 
                LIMIT 3
            """)
            top_games = cursor.fetchall()
            if top_games:
                print(f"\n     TOP TEAM PERFORMANCES:")
                for team_name, game_date, matchup, pts, wl in top_games:
                    print(f"       • {team_name}: {pts} points vs {matchup} ({wl}) - {game_date}")
        
        # Cross-reference analysis
        if player_game_logs > 0 and team_game_logs > 0:
            print(f"\n   CROSS-REFERENCE ANALYSIS:")
            
            # Games that appear in both player and team logs
            cursor = collector.conn.execute("""
                SELECT COUNT(DISTINCT pgl.game_id) as common_games
                FROM player_game_logs pgl
                INNER JOIN team_game_logs tgl ON pgl.game_id = tgl.game_id
            """)
            common_games = cursor.fetchone()[0]
            print(f"     • {common_games} games appear in both player and team logs")
            
            # Sample game with both player and team data
            cursor = collector.conn.execute("""
                SELECT 
                    pgl.game_id, 
                    pgl.game_date, 
                    pgl.matchup,
                    COUNT(pgl.player_id) as player_count,
                    tgl.team_name,
                    tgl.pts as team_pts,
                    tgl.wl
                FROM player_game_logs pgl
                INNER JOIN team_game_logs tgl ON pgl.game_id = tgl.game_id
                GROUP BY pgl.game_id, pgl.game_date, pgl.matchup, tgl.team_name, tgl.pts, tgl.wl
                LIMIT 1
            """)
            sample_game = cursor.fetchone()
            if sample_game:
                game_id, game_date, matchup, player_count, team_name, team_pts, wl = sample_game
                print(f"     • Sample game: {team_name} vs {matchup} ({game_date})")
                print(f"       - Team scored {team_pts} points ({wl})")
                print(f"       - {player_count} individual player performances recorded")
        
        # Test export functionality
        print(f"\n7. Testing CSV export...")
        collector.export_to_csv()
        
        # Check if CSVs were created
        csv_files = ['player_game_logs.csv', 'team_game_logs.csv']
        for csv_file in csv_files:
            csv_path = os.path.join(collector.data_dir, csv_file)
            if os.path.exists(csv_path):
                df = pd.read_csv(csv_path)
                print(f"   ✅ {csv_file}: {len(df)} rows exported")
            else:
                print(f"   ⚠️  {csv_file}: Not found (may be empty)")
        
        # Final assessment
        print(f"\n8. FINAL ASSESSMENT:")
        if player_game_logs > 0 and team_game_logs > 0:
            print(f"   🎉 COMPREHENSIVE SUCCESS!")
            print(f"   • Both player and team game logs collected")
            print(f"   • Data is properly structured and cross-referenced")
            print(f"   • Ready for advanced analytics and reporting")
        elif player_game_logs > 0 or team_game_logs > 0:
            print(f"   ✅ PARTIAL SUCCESS!")
            print(f"   • Some game log data collected")
            print(f"   • May need to investigate missing data sources")
        else:
            print(f"   ⚠️  LIMITED SUCCESS")
            print(f"   • No game log data found")
            print(f"   • This could be due to:")
            print(f"     - Season hasn't started yet")
            print(f"     - API access limitations")
            print(f"     - Data availability issues")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
    finally:
        collector.close()

def test_direct_endpoints():
    """Test the game log endpoints directly"""
    print("\n" + "=" * 70)
    print("🔍 DIRECT ENDPOINT TESTING")
    print("=" * 70)
    
    from nba_api.stats.endpoints import playergamelog, teamgamelog, commonallplayers, scoreboardv2
    import time
    from datetime import datetime
    
    try:
        # Get sample WNBA player and team
        print("Getting sample WNBA data...")
        
        # Get sample player
        players = commonallplayers.CommonAllPlayers(
            league_id='10',  # WNBA
            season='2024',
            is_only_current_season=1
        )
        players_df = players.get_data_frames()[0]
        
        # Get sample team from scoreboard
        test_date = datetime(2024, 5, 15)
        scoreboard = scoreboardv2.ScoreboardV2(
            league_id='10',
            game_date=test_date.strftime('%m/%d/%Y')
        )
        data_frames = scoreboard.get_data_frames()
        
        if len(players_df) > 0 and len(data_frames) >= 5:
            # Test player game log
            sample_player = players_df.iloc[0]
            player_id = sample_player['PERSON_ID']
            player_name = sample_player['DISPLAY_FIRST_LAST']
            
            print(f"Testing PlayerGameLog with: {player_name} (ID: {player_id})")
            time.sleep(1)
            
            player_logs = playergamelog.PlayerGameLog(
                player_id=player_id,
                season='2024',
                season_type_all_star='Regular Season'
            )
            player_df = player_logs.get_data_frames()[0]
            print(f"✅ PlayerGameLog: {len(player_df)} games")
            
            # Test team game log
            standings_df = data_frames[4]
            if len(standings_df) > 0:
                sample_team = standings_df.iloc[0]
                team_id = sample_team['TEAM_ID']
                team_name = sample_team['TEAM']
                
                print(f"Testing TeamGameLog with: {team_name} (ID: {team_id})")
                time.sleep(1)
                
                team_logs = teamgamelog.TeamGameLog(
                    team_id=team_id,
                    season='2024',
                    season_type_all_star='Regular Season',
                    league_id_nullable='10'
                )
                team_df = team_logs.get_data_frames()[0]
                print(f"✅ TeamGameLog: {len(team_df)} games")
                
                print(f"\n🎯 Both endpoints working correctly!")
            else:
                print("❌ No team data found")
        else:
            print("❌ Insufficient sample data")
            
    except Exception as e:
        print(f"❌ Direct endpoint test failed: {e}")

if __name__ == "__main__":
    test_comprehensive_game_log_collection()
    test_direct_endpoints()
