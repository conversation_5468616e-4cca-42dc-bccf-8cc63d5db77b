#!/usr/bin/env python3
"""
Run Complete WNBA Collection: 2015-2025
Robust execution with error handling and resumption capabilities
"""

import os
import sys
import time
import subprocess
from datetime import datetime
import sqlite3

def check_prerequisites():
    """Check if all prerequisites are met"""
    print("🔍 CHECKING PREREQUISITES...")
    
    # Check if main collector exists
    if not os.path.exists('wnba_data_collector.py'):
        print("❌ wnba_data_collector.py not found!")
        return False
    
    # Check if collection script exists
    if not os.path.exists('collect_complete_wnba_history.py'):
        print("❌ collect_complete_wnba_history.py not found!")
        return False
    
    # Check Python packages
    try:
        import pandas
        import sqlite3
        print("✅ Required packages available")
    except ImportError as e:
        print(f"❌ Missing required package: {e}")
        return False
    
    print("✅ All prerequisites met")
    return True

def get_collection_status():
    """Get current collection status"""
    db_path = 'wnba_comprehensive.db'
    
    if not os.path.exists(db_path):
        return "not_started", {}
    
    try:
        conn = sqlite3.connect(db_path)
        
        # Check key tables
        key_tables = ['teams', 'players', 'player_game_logs', 'team_game_logs']
        status = {}
        
        for table in key_tables:
            try:
                count = conn.execute(f"SELECT COUNT(*) FROM {table}").fetchone()[0]
                status[table] = count
            except sqlite3.OperationalError:
                status[table] = 0
        
        conn.close()
        
        # Determine overall status
        if all(count == 0 for count in status.values()):
            return "not_started", status
        elif status.get('teams', 0) > 0 and status.get('players', 0) > 0:
            if status.get('player_game_logs', 0) > 10000:
                return "advanced", status
            elif status.get('player_game_logs', 0) > 0:
                return "in_progress", status
            else:
                return "foundation_complete", status
        else:
            return "foundation_started", status
    
    except Exception as e:
        print(f"⚠️ Error checking status: {e}")
        return "unknown", {}

def run_collection_with_monitoring():
    """Run the collection with monitoring"""
    print("🚀 STARTING COMPLETE WNBA COLLECTION")
    print("=" * 80)
    
    start_time = datetime.now()
    
    # Start monitoring in background (if possible)
    monitor_process = None
    try:
        if os.name != 'nt':  # Unix-like systems
            monitor_process = subprocess.Popen([
                sys.executable, 'monitor_wnba_collection.py'
            ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            print("📊 Background monitoring started")
    except Exception:
        print("⚠️ Could not start background monitoring")
    
    try:
        # Run the main collection
        print("🏀 Executing comprehensive WNBA data collection...")
        print("   This may take several hours depending on API rate limits")
        print("   You can monitor progress in another terminal with:")
        print("   python monitor_wnba_collection.py")
        print()
        
        # Execute the collection script
        result = subprocess.run([
            sys.executable, 'collect_complete_wnba_history.py'
        ], capture_output=False, text=True)
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        if result.returncode == 0:
            print(f"\n🎉 COLLECTION COMPLETED SUCCESSFULLY!")
            print(f"⏱️ Total Duration: {duration}")
            
            # Show final summary
            print("\n📊 FINAL SUMMARY:")
            subprocess.run([sys.executable, 'monitor_wnba_collection.py', 'summary'])
            
        else:
            print(f"\n⚠️ Collection completed with errors (exit code: {result.returncode})")
            print(f"⏱️ Duration: {duration}")
    
    except KeyboardInterrupt:
        print(f"\n🛑 Collection interrupted by user")
    except Exception as e:
        print(f"\n❌ Collection failed: {e}")
    finally:
        # Clean up monitoring process
        if monitor_process:
            try:
                monitor_process.terminate()
                monitor_process.wait(timeout=5)
            except:
                pass

def resume_collection():
    """Resume collection from where it left off"""
    print("🔄 RESUMING WNBA COLLECTION")
    print("=" * 80)
    
    status, data = get_collection_status()
    
    print(f"📊 Current Status: {status}")
    for table, count in data.items():
        print(f"   • {table}: {count:,} records")
    
    if status == "not_started":
        print("🚀 Starting fresh collection...")
        run_collection_with_monitoring()
    elif status in ["foundation_started", "foundation_complete", "in_progress"]:
        print("🔄 Resuming collection...")
        run_collection_with_monitoring()
    elif status == "advanced":
        print("✅ Collection appears to be largely complete")
        print("🔄 Running to ensure all data is collected...")
        run_collection_with_monitoring()
    else:
        print("❓ Unknown status - attempting to resume...")
        run_collection_with_monitoring()

def main():
    """Main execution function"""
    print("🏀 WNBA COMPREHENSIVE DATA COLLECTION SYSTEM")
    print("=" * 80)
    print("📅 Target: Complete WNBA history 2015-2025")
    print("🎯 Goal: Every player, every team, every game, every statistic")
    print("=" * 80)
    
    # Check prerequisites
    if not check_prerequisites():
        print("\n❌ Prerequisites not met. Please install required packages and ensure all files are present.")
        return
    
    # Check current status
    status, data = get_collection_status()
    
    print(f"\n📊 CURRENT COLLECTION STATUS: {status.upper()}")
    
    if data:
        print("📋 Current Data:")
        for table, count in data.items():
            print(f"   • {table}: {count:,} records")
    
    # Ask user what to do
    print(f"\n🤔 What would you like to do?")
    print("1. Start/Resume collection")
    print("2. Monitor existing collection")
    print("3. Show current summary")
    print("4. Exit")
    
    try:
        choice = input("\nEnter your choice (1-4): ").strip()
        
        if choice == "1":
            resume_collection()
        elif choice == "2":
            print("📊 Starting monitoring...")
            subprocess.run([sys.executable, 'monitor_wnba_collection.py'])
        elif choice == "3":
            subprocess.run([sys.executable, 'monitor_wnba_collection.py', 'summary'])
        elif choice == "4":
            print("👋 Goodbye!")
        else:
            print("❌ Invalid choice")
    
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"\n❌ Error: {e}")

if __name__ == "__main__":
    main()
