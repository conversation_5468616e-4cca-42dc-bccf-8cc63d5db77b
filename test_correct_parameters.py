#!/usr/bin/env python3
"""
Test script to find correct parameters for WNBA endpoints
"""

from nba_api.stats.endpoints import *
import pandas as pd
import time
from datetime import datetime

def test_endpoint_with_variations(endpoint_func, endpoint_name, base_params, param_variations):
    """Test an endpoint with different parameter combinations"""
    print(f"\n{'='*50}")
    print(f"Testing {endpoint_name}")
    print(f"{'='*50}")
    
    for variation_name, params in param_variations.items():
        print(f"\nTrying {variation_name}:")
        test_params = {**base_params, **params}
        print(f"Parameters: {test_params}")
        
        try:
            time.sleep(0.6)
            result = endpoint_func(**test_params)
            
            if result:
                data_frames = result.get_data_frames()
                total_rows = sum(len(df) for df in data_frames)
                print(f"✅ SUCCESS! {len(data_frames)} dataframes, {total_rows} total rows")
                
                if total_rows > 0:
                    print(f"Sample columns: {list(data_frames[0].columns)[:5]}...")
                    return test_params  # Return working parameters
            else:
                print("❌ No result returned")
                
        except Exception as e:
            print(f"❌ ERROR: {e}")
    
    return None

def main():
    wnba_league_id = '10'
    season = '2024'
    
    print("TESTING WNBA ENDPOINT PARAMETERS")
    print("="*60)
    
    # Test LeagueGameFinder - this should have games
    print("\n" + "="*60)
    print("TESTING LEAGUE GAME FINDER")
    print("="*60)
    
    game_finder_variations = {
        "basic": {},
        "with_league_id": {"league_id_nullable": wnba_league_id},
        "with_season": {"season_nullable": season},
        "with_both": {"league_id_nullable": wnba_league_id, "season_nullable": season},
        "with_season_type": {"season_nullable": season, "season_type_nullable": "Regular Season"},
        "full_params": {
            "league_id_nullable": wnba_league_id, 
            "season_nullable": season,
            "season_type_nullable": "Regular Season"
        }
    }
    
    working_params = test_endpoint_with_variations(
        leaguegamefinder.LeagueGameFinder,
        "LeagueGameFinder",
        {},
        game_finder_variations
    )
    
    if working_params:
        print(f"\n🎉 WORKING PARAMETERS FOR LeagueGameFinder: {working_params}")
    
    # Test LeagueGameLog
    print("\n" + "="*60)
    print("TESTING LEAGUE GAME LOG")
    print("="*60)
    
    game_log_variations = {
        "basic": {},
        "with_league_id": {"league_id": wnba_league_id},
        "with_season": {"season": season},
        "with_both": {"league_id": wnba_league_id, "season": season},
        "with_season_type": {"season": season, "season_type": "Regular Season"},
        "full_params": {
            "league_id": wnba_league_id, 
            "season": season,
            "season_type": "Regular Season"
        }
    }
    
    working_params = test_endpoint_with_variations(
        leaguegamelog.LeagueGameLog,
        "LeagueGameLog",
        {},
        game_log_variations
    )
    
    if working_params:
        print(f"\n🎉 WORKING PARAMETERS FOR LeagueGameLog: {working_params}")
    
    # Test LeagueLeaders with correct parameters
    print("\n" + "="*60)
    print("TESTING LEAGUE LEADERS")
    print("="*60)
    
    leaders_variations = {
        "basic": {"season": season},
        "with_league_id": {"league_id": wnba_league_id, "season": season},
        "with_season_type_all_star": {"season": season, "season_type_all_star": "Regular Season"},
        "full_params": {
            "league_id": wnba_league_id,
            "season": season,
            "season_type_all_star": "Regular Season"
        }
    }
    
    working_params = test_endpoint_with_variations(
        leagueleaders.LeagueLeaders,
        "LeagueLeaders",
        {},
        leaders_variations
    )
    
    if working_params:
        print(f"\n🎉 WORKING PARAMETERS FOR LeagueLeaders: {working_params}")
    
    # Test PlayerGameLog with a sample player
    print("\n" + "="*60)
    print("TESTING PLAYER GAME LOG")
    print("="*60)
    
    try:
        # Get a sample player ID
        players = commonallplayers.CommonAllPlayers(
            league_id=wnba_league_id,
            season=season,
            is_only_current_season=1
        )
        players_df = players.get_data_frames()[0]
        if len(players_df) > 0:
            sample_player_id = players_df.iloc[0]['PERSON_ID']
            print(f"Using sample player ID: {sample_player_id}")
            
            player_log_variations = {
                "basic": {"player_id": sample_player_id},
                "with_season": {"player_id": sample_player_id, "season": season},
                "with_season_type": {
                    "player_id": sample_player_id, 
                    "season": season, 
                    "season_type_all_star": "Regular Season"
                }
            }
            
            working_params = test_endpoint_with_variations(
                playergamelog.PlayerGameLog,
                "PlayerGameLog",
                {},
                player_log_variations
            )
            
            if working_params:
                print(f"\n🎉 WORKING PARAMETERS FOR PlayerGameLog: {working_params}")
    except Exception as e:
        print(f"Could not test PlayerGameLog: {e}")
    
    # Test TeamGameLog with a sample team
    print("\n" + "="*60)
    print("TESTING TEAM GAME LOG")
    print("="*60)
    
    try:
        # Get a sample team ID from scoreboard
        test_date = datetime(2024, 5, 15)
        scoreboard = scoreboardv2.ScoreboardV2(
            league_id=wnba_league_id,
            game_date=test_date.strftime('%m/%d/%Y')
        )
        data_frames = scoreboard.get_data_frames()
        if len(data_frames) >= 5:
            standings_df = data_frames[4]
            if len(standings_df) > 0:
                sample_team_id = standings_df.iloc[0]['TEAM_ID']
                print(f"Using sample team ID: {sample_team_id}")
                
                team_log_variations = {
                    "basic": {"team_id": sample_team_id},
                    "with_season": {"team_id": sample_team_id, "season": season},
                    "with_season_type": {
                        "team_id": sample_team_id, 
                        "season": season, 
                        "season_type_all_star": "Regular Season"
                    }
                }
                
                working_params = test_endpoint_with_variations(
                    teamgamelog.TeamGameLog,
                    "TeamGameLog",
                    {},
                    team_log_variations
                )
                
                if working_params:
                    print(f"\n🎉 WORKING PARAMETERS FOR TeamGameLog: {working_params}")
    except Exception as e:
        print(f"Could not test TeamGameLog: {e}")
    
    print("\n" + "="*60)
    print("PARAMETER TESTING COMPLETE")
    print("="*60)

if __name__ == "__main__":
    main()
