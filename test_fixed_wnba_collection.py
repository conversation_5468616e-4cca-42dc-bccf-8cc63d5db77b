#!/usr/bin/env python3
"""
Test Fixed WNBA Collection with 2025 Season Support
"""

from wnba_data_collector import WNBAComprehensiveCollector
import pandas as pd
import os

def test_fixed_wnba_collection():
    """Test the fixed WNBA collection with proper 2025 season support"""
    print("🏀 Testing FIXED WNBA Collection for 2025 Season 🏀")
    print("=" * 70)
    
    # Create collector instance
    collector = WNBAComprehensiveCollector()
    
    try:
        # Test with 2024 and 2025 seasons
        collector.seasons = ['2024', '2025']
        
        print("1. Testing basic data collection...")
        print("   Collecting teams data...")
        collector.collect_teams_data()
        
        print("   Collecting players data...")
        collector.collect_players_data()
        
        # Check what we have
        for season in collector.seasons:
            team_count = collector.conn.execute("SELECT COUNT(*) FROM teams WHERE season = ?", (season,)).fetchone()[0]
            player_count = collector.conn.execute("SELECT COUNT(*) FROM players WHERE season = ?", (season,)).fetchone()[0]
            print(f"   Season {season}: {team_count} teams, {player_count} players")
        
        print("\n2. Testing PLAYER game log collection...")
        collector.collect_player_game_logs()
        
        player_game_logs = collector.conn.execute("SELECT COUNT(*) FROM player_game_logs").fetchone()[0]
        print(f"   Player game logs collected: {player_game_logs}")
        
        if player_game_logs > 0:
            # Show sample player game logs
            cursor = collector.conn.execute("""
                SELECT player_name, season, COUNT(*) as games, AVG(pts) as avg_pts, MAX(pts) as max_pts
                FROM player_game_logs 
                GROUP BY player_id, player_name, season
                ORDER BY games DESC
                LIMIT 5
            """)
            print("   Top players by games played:")
            for player_name, season, games, avg_pts, max_pts in cursor.fetchall():
                print(f"     • {player_name} ({season}): {games} games, {avg_pts:.1f} avg pts, {max_pts} max pts")
        
        print("\n3. Testing TEAM game log collection...")
        collector.collect_team_game_logs()
        
        team_game_logs = collector.conn.execute("SELECT COUNT(*) FROM team_game_logs").fetchone()[0]
        print(f"   Team game logs collected: {team_game_logs}")
        
        if team_game_logs > 0:
            # Show sample team game logs
            cursor = collector.conn.execute("""
                SELECT team_name, season, COUNT(*) as games, AVG(pts) as avg_pts, MAX(pts) as max_pts
                FROM team_game_logs 
                GROUP BY team_id, team_name, season
                ORDER BY games DESC
                LIMIT 5
            """)
            print("   Top teams by games played:")
            for team_name, season, games, avg_pts, max_pts in cursor.fetchall():
                print(f"     • {team_name} ({season}): {games} games, {avg_pts:.1f} avg pts, {max_pts} max pts")
        
        print("\n4. Testing DEFENSE HUB stats collection...")
        collector.collect_defense_hub_stats()
        
        defense_stats = collector.conn.execute("SELECT COUNT(*) FROM defense_hub_stats").fetchone()[0]
        print(f"   Defense hub stats collected: {defense_stats}")
        
        if defense_stats > 0:
            cursor = collector.conn.execute("""
                SELECT season, COUNT(DISTINCT team_id) as teams, COUNT(DISTINCT stat_type) as stat_types
                FROM defense_hub_stats 
                GROUP BY season
            """)
            for season, teams, stat_types in cursor.fetchall():
                print(f"     • Season {season}: {teams} teams, {stat_types} stat types")
        
        print("\n5. Testing LINEUPS collection...")
        collector.collect_lineups_data()
        
        lineups = collector.conn.execute("SELECT COUNT(*) FROM lineups").fetchone()[0]
        print(f"   Lineups collected: {lineups}")
        
        if lineups > 0:
            cursor = collector.conn.execute("""
                SELECT season, COUNT(*) as lineup_count, COUNT(DISTINCT team_id) as teams
                FROM lineups 
                GROUP BY season
            """)
            for season, lineup_count, teams in cursor.fetchall():
                print(f"     • Season {season}: {lineup_count} lineups from {teams} teams")
        
        print("\n6. Testing GAME ROTATIONS collection...")
        collector.collect_game_rotations()
        
        rotations = collector.conn.execute("SELECT COUNT(*) FROM game_rotations").fetchone()[0]
        print(f"   Game rotations collected: {rotations}")
        
        if rotations > 0:
            cursor = collector.conn.execute("""
                SELECT COUNT(DISTINCT game_id) as games, COUNT(DISTINCT person_id) as players
                FROM game_rotations
            """)
            games, players = cursor.fetchone()
            print(f"     • {games} games with rotation data for {players} players")
        
        print("\n7. Testing CSV export...")
        collector.export_to_csv()
        
        # Check exported files
        expected_files = ['player_game_logs.csv', 'team_game_logs.csv', 'defense_hub_stats.csv', 
                         'lineups.csv', 'game_rotations.csv']
        
        for csv_file in expected_files:
            csv_path = os.path.join(collector.data_dir, csv_file)
            if os.path.exists(csv_path):
                df = pd.read_csv(csv_path)
                print(f"   ✅ {csv_file}: {len(df)} rows")
            else:
                print(f"   ⚠️  {csv_file}: Not found (may be empty)")
        
        print("\n8. COMPREHENSIVE SUMMARY:")
        print("   " + "="*50)
        
        # Get total counts
        total_counts = {}
        tables = ['teams', 'players', 'player_game_logs', 'team_game_logs', 
                 'defense_hub_stats', 'lineups', 'game_rotations']
        
        for table in tables:
            try:
                count = collector.conn.execute(f"SELECT COUNT(*) FROM {table}").fetchone()[0]
                total_counts[table] = count
            except:
                total_counts[table] = 0
        
        total_records = sum(total_counts.values())
        
        print(f"   📊 TOTAL DATA COLLECTED: {total_records:,} records")
        for table, count in total_counts.items():
            if count > 0:
                print(f"     • {table.replace('_', ' ').title()}: {count:,}")
        
        # Assessment
        if total_counts['player_game_logs'] > 0 and total_counts['team_game_logs'] > 0:
            print(f"\n   🎉 EXCELLENT! Game log collection is working perfectly!")
            print(f"     • Player game logs: ✅ Working")
            print(f"     • Team game logs: ✅ Working") 
            print(f"     • Advanced stats: ✅ Working")
            print(f"     • Ready for comprehensive WNBA analysis!")
        elif total_counts['player_game_logs'] > 0 or total_counts['team_game_logs'] > 0:
            print(f"\n   ✅ GOOD! Some game log data collected")
            print(f"     • Partial success with game log collection")
        else:
            print(f"\n   ⚠️  Game logs not found - this could be due to:")
            print(f"     • 2025 season hasn't started yet")
            print(f"     • Limited 2024 data availability")
            print(f"     • API access restrictions")
        
        print(f"\n   🔧 FIXES APPLIED:")
        print(f"     • Correct parameter names for all endpoints")
        print(f"     • Proper season format handling")
        print(f"     • Enhanced error handling")
        print(f"     • Added defensive and lineup analytics")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
    finally:
        collector.close()

def test_direct_endpoints_2025():
    """Test endpoints directly for 2025 season"""
    print("\n" + "=" * 70)
    print("🔍 DIRECT ENDPOINT TESTING FOR 2025")
    print("=" * 70)
    
    from nba_api.stats.endpoints import (
        playergamelog, teamgamelog, commonallplayers, 
        defensehub, leaguedashlineups
    )
    import time
    
    try:
        # Test with 2025 season
        print("Testing endpoints with 2025 season...")
        
        # Get sample WNBA players for 2025
        print("1. Testing CommonAllPlayers for 2025...")
        players = commonallplayers.CommonAllPlayers(
            league_id='10',  # WNBA
            season='2025',
            is_only_current_season=1
        )
        players_df = players.get_data_frames()[0]
        print(f"   ✅ Found {len(players_df)} players for 2025 season")
        
        if len(players_df) > 0:
            sample_player = players_df.iloc[0]
            player_id = sample_player['PERSON_ID']
            player_name = sample_player['DISPLAY_FIRST_LAST']
            
            print(f"\n2. Testing PlayerGameLog for {player_name}...")
            time.sleep(1)
            
            player_logs = playergamelog.PlayerGameLog(
                player_id=player_id,
                season='2025',
                season_type_all_star='Regular Season'
            )
            player_df = player_logs.get_data_frames()[0]
            print(f"   ✅ PlayerGameLog: {len(player_df)} games for {player_name}")
            
            if len(player_df) > 0:
                print(f"   📊 Sample game: {player_df.iloc[0]['GAME_DATE']} vs {player_df.iloc[0]['MATCHUP']}")
        
        print(f"\n3. Testing DefenseHub for 2025...")
        time.sleep(1)
        
        defense = defensehub.DefenseHub(
            season='2025',
            season_type_playoffs='Regular Season',
            league_id='10',
            player_or_team='Team'
        )
        defense_dfs = defense.get_data_frames()
        total_defense_rows = sum(len(df) for df in defense_dfs)
        print(f"   ✅ DefenseHub: {len(defense_dfs)} datasets, {total_defense_rows} total rows")
        
        print(f"\n4. Testing LeagueDashLineups for 2025...")
        time.sleep(1)
        
        lineups = leaguedashlineups.LeagueDashLineups(
            season='2025',
            season_type_all_star='Regular Season',
            league_id_nullable='10'
        )
        lineups_df = lineups.get_data_frames()[0]
        print(f"   ✅ LeagueDashLineups: {len(lineups_df)} lineups")
        
        print(f"\n🎯 ALL ENDPOINTS WORKING FOR 2025 SEASON!")
        
    except Exception as e:
        print(f"❌ Direct endpoint test failed: {e}")
        print("This might be expected if 2025 season hasn't started yet")

if __name__ == "__main__":
    test_fixed_wnba_collection()
    test_direct_endpoints_2025()
