#!/usr/bin/env python3
"""
Test script for the Ultimate WNBA Data Collector
"""

from wnba_ultimate_collector import WNBAUltimateCollector
import os

def test_ultimate_collector():
    """Test the ultimate WNBA collector with a limited scope"""
    print("Testing Ultimate WNBA Data Collector...")
    
    # Create collector instance
    collector = WNBAUltimateCollector()
    
    try:
        # Show configuration
        print(f"Seasons to collect: {collector.seasons}")
        print(f"WNBA League ID: {collector.wnba_league_id}")
        print(f"Base directory: {collector.base_dir}")
        print(f"Data directory: {collector.data_dir}")
        print(f"Working endpoints: {len(collector.working_endpoints)}")
        
        # Show initial progress
        print("\nInitial Database State:")
        collector.print_ultimate_summary()
        
        # Test collecting just a few key datasets for verification
        print("\nTesting key data collection methods...")
        
        # Limit to just 2024 for testing
        collector.seasons = ['2024']
        
        # Test players data collection
        print("\n1. Testing players data collection...")
        collector.collect_players_data()
        
        # Test games data collection
        print("\n2. Testing games data collection...")
        collector.collect_comprehensive_games_data()
        
        # Test league leaders
        print("\n3. Testing league leaders collection...")
        collector.collect_league_leaders()
        
        # Show progress after test collections
        print("\nProgress after test collections:")
        collector.print_ultimate_summary()
        
        # Test database queries
        print("\nTesting database queries...")
        
        # Check players
        players_count = collector.conn.execute("SELECT COUNT(*) FROM players").fetchone()[0]
        print(f"Players in database: {players_count}")
        
        # Check games finder
        games_finder_count = collector.conn.execute("SELECT COUNT(*) FROM games_finder").fetchone()[0]
        print(f"Games finder records: {games_finder_count}")
        
        # Check games log
        games_log_count = collector.conn.execute("SELECT COUNT(*) FROM games_log").fetchone()[0]
        print(f"Games log records: {games_log_count}")
        
        # Check league leaders
        leaders_count = collector.conn.execute("SELECT COUNT(*) FROM league_leaders").fetchone()[0]
        print(f"League leaders: {leaders_count}")
        
        if players_count > 0:
            # Show sample player data
            sample_players = collector.conn.execute("SELECT player_name, team_id, position FROM players LIMIT 5").fetchall()
            print("\nSample player data:")
            for player in sample_players:
                print(f"  {player[0]} ({player[1]}) - {player[2]}")
        
        if games_finder_count > 0:
            # Show sample game data
            sample_games = collector.conn.execute("SELECT team_name, game_date, matchup, pts FROM games_finder LIMIT 5").fetchall()
            print("\nSample game data:")
            for game in sample_games:
                print(f"  {game[0]} vs {game[2]} on {game[1]}: {game[3]} pts")
        
        if leaders_count > 0:
            # Show sample leaders data
            sample_leaders = collector.conn.execute("SELECT player_name, team_name, pts FROM league_leaders ORDER BY pts DESC LIMIT 5").fetchall()
            print("\nSample league leaders:")
            for leader in sample_leaders:
                print(f"  {leader[0]} ({leader[1]}): {leader[2]} pts")
        
        print("\n✅ Test completed successfully!")
        print("\nThe Ultimate WNBA Data Collector is working correctly!")
        print("You can now run the full collection to get comprehensive WNBA data from 2015-2025.")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
    finally:
        collector.close()

if __name__ == "__main__":
    test_ultimate_collector()
