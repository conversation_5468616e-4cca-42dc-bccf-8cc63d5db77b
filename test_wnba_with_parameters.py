#!/usr/bin/env python3
"""
Test WNBA endpoints with specific parameter values from documentation
"""

from nba_api.stats.endpoints import *
import pandas as pd
import time
from datetime import datetime

def safe_test_endpoint(endpoint_func, endpoint_name, **kwargs):
    """Safely test an endpoint and return results"""
    print(f"\nTesting {endpoint_name}...")
    print(f"Parameters: {kwargs}")
    
    try:
        time.sleep(0.6)
        result = endpoint_func(**kwargs)
        
        if result:
            data_frames = result.get_data_frames()
            total_rows = sum(len(df) for df in data_frames)
            print(f"✅ SUCCESS! {len(data_frames)} dataframes, {total_rows} total rows")
            
            if total_rows > 0:
                print(f"Sample columns: {list(data_frames[0].columns)[:5]}...")
                return True, total_rows, list(data_frames[0].columns)
            else:
                print("⚠️  SUCCESS but 0 rows")
                return True, 0, []
        else:
            print("❌ No result returned")
            return False, 0, []
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False, 0, []

def test_clutch_endpoints():
    """Test clutch-related endpoints with specific clutch time parameters"""
    print("\n" + "="*60)
    print("TESTING CLUTCH ENDPOINTS WITH SPECIFIC PARAMETERS")
    print("="*60)
    
    season = '2024'
    wnba_league_id = '10'
    
    # Test different clutch time parameters
    clutch_times = [
        "Last 5 Minutes",  # default
        "Last 2 Minutes",
        "Last 1 Minute",
        "Last 30 Seconds"
    ]
    
    working_clutch = []
    
    for clutch_time in clutch_times:
        print(f"\n--- Testing with clutch_time: {clutch_time} ---")
        
        # Test player clutch stats with specific clutch time
        success, rows, cols = safe_test_endpoint(
            leaguedashplayerclutch.LeagueDashPlayerClutch,
            f"LeagueDashPlayerClutch_{clutch_time.replace(' ', '_')}",
            season=season,
            season_type_all_star='Regular Season',
            clutch_time_nullable=clutch_time
        )
        if success and rows > 0:
            working_clutch.append((f"PlayerClutch_{clutch_time}", rows))
        
        # Test team clutch stats with specific clutch time
        success, rows, cols = safe_test_endpoint(
            leaguedashteamclutch.LeagueDashTeamClutch,
            f"LeagueDashTeamClutch_{clutch_time.replace(' ', '_')}",
            season=season,
            season_type_all_star='Regular Season',
            clutch_time_nullable=clutch_time
        )
        if success and rows > 0:
            working_clutch.append((f"TeamClutch_{clutch_time}", rows))
    
    print(f"\n🎉 WORKING CLUTCH ENDPOINTS: {len(working_clutch)}")
    for name, rows in working_clutch:
        print(f"  • {name}: {rows} rows")
    
    return working_clutch

def test_context_measure_endpoints():
    """Test endpoints with different context measures"""
    print("\n" + "="*60)
    print("TESTING CONTEXT MEASURE ENDPOINTS")
    print("="*60)
    
    season = '2024'
    
    # Test different context measures
    context_measures = [
        "PTS",      # default
        "FGM",
        "FGA", 
        "FG_PCT",
        "AST",
        "REB",
        "STL",
        "BLK"
    ]
    
    working_context = []
    
    for measure in context_measures:
        print(f"\n--- Testing with context_measure: {measure} ---")
        
        # Test league dash with context measure (if endpoint supports it)
        # Note: Need to find endpoints that actually use context_measure parameter
        
        # Test player dashboard endpoints that might use context measures
        try:
            # Get a sample player ID first
            players = commonallplayers.CommonAllPlayers(
                league_id='10',
                season='2024',
                is_only_current_season=1
            )
            players_df = players.get_data_frames()[0]
            
            if len(players_df) > 0:
                sample_player_id = players_df.iloc[0]['PERSON_ID']
                
                # Test player dashboard by general splits with context measure
                success, rows, cols = safe_test_endpoint(
                    playerdashboardbygeneralsplits.PlayerDashboardByGeneralSplits,
                    f"PlayerDashboardByGeneralSplits_{measure}",
                    player_id=sample_player_id,
                    measure_type_detailed_defense=measure if measure in ["PTS", "FGM", "FGA", "FG_PCT"] else "PTS"
                )
                if success and rows > 0:
                    working_context.append((f"PlayerDashboard_{measure}", rows))
        
        except Exception as e:
            print(f"Error testing context measure {measure}: {e}")
    
    print(f"\n🎉 WORKING CONTEXT MEASURE ENDPOINTS: {len(working_context)}")
    for name, rows in working_context:
        print(f"  • {name}: {rows} rows")
    
    return working_context

def test_conference_endpoints():
    """Test endpoints with conference parameters"""
    print("\n" + "="*60)
    print("TESTING CONFERENCE ENDPOINTS")
    print("="*60)
    
    season = '2024'
    conferences = ["East", "West", ""]  # Empty string for all
    
    working_conference = []
    
    for conference in conferences:
        conf_label = conference if conference else "All"
        print(f"\n--- Testing with conference: {conf_label} ---")
        
        # Test team stats by conference
        success, rows, cols = safe_test_endpoint(
            leaguedashteamstats.LeagueDashTeamStats,
            f"LeagueDashTeamStats_{conf_label}",
            season=season,
            season_type_all_star='Regular Season',
            conference_nullable=conference
        )
        if success and rows > 0:
            working_conference.append((f"TeamStats_{conf_label}", rows))
        
        # Test player stats by conference
        success, rows, cols = safe_test_endpoint(
            leaguedashplayerstats.LeagueDashPlayerStats,
            f"LeagueDashPlayerStats_{conf_label}",
            season=season,
            season_type_all_star='Regular Season',
            conference_nullable=conference
        )
        if success and rows > 0:
            working_conference.append((f"PlayerStats_{conf_label}", rows))
    
    print(f"\n🎉 WORKING CONFERENCE ENDPOINTS: {len(working_conference)}")
    for name, rows in working_conference:
        print(f"  • {name}: {rows} rows")
    
    return working_conference

def test_additional_wnba_endpoints():
    """Test additional endpoints that might work for WNBA"""
    print("\n" + "="*60)
    print("TESTING ADDITIONAL WNBA ENDPOINTS")
    print("="*60)
    
    season = '2024'
    wnba_league_id = '10'
    
    # Test various endpoints with WNBA-specific parameters
    additional_endpoints = [
        # Historical/All-time endpoints
        (alltimeleadersgrids.AllTimeLeadersGrids, "AllTimeLeadersGrids", {"league_id": wnba_league_id}),
        (franchisehistory.FranchiseHistory, "FranchiseHistory", {"league_id": wnba_league_id}),
        (franchiseleaders.FranchiseLeaders, "FranchiseLeaders", {"league_id": wnba_league_id}),
        
        # Team year info
        (commonteamyears.CommonTeamYears, "CommonTeamYears", {"league_id": wnba_league_id}),
        
        # Assist tracking
        (assistleaders.AssistLeaders, "AssistLeaders", {"league_id": wnba_league_id, "season": season}),
        (assisttracker.AssistTracker, "AssistTracker", {"league_id": wnba_league_id, "season": season}),
        
        # Homepage/Leaders
        (homepageleaders.HomePageLeaders, "HomePageLeaders", {"league_id": wnba_league_id, "season": season}),
        (homepagev2.HomePageV2, "HomePageV2", {"league_id": wnba_league_id}),
        
        # Leader tiles
        (leaderstiles.LeadersTiles, "LeadersTiles", {"league_id": wnba_league_id, "season": season}),
        
        # IST Standings (might work for WNBA tournaments)
        (iststandings.ISTStandings, "ISTStandings", {"league_id": wnba_league_id, "season": season}),
        
        # Lineups
        (leaguedashlineups.LeagueDashLineups, "LeagueDashLineups", {"season": season, "season_type_all_star": "Regular Season"}),
        
        # Shot locations
        (leaguedashplayershotlocations.LeagueDashPlayerShotLocations, "LeagueDashPlayerShotLocations", {"season": season, "season_type_all_star": "Regular Season"}),
        (leaguedashteamshotlocations.LeagueDashTeamShotLocations, "LeagueDashTeamShotLocations", {"season": season, "season_type_all_star": "Regular Season"}),
        
        # Bio stats
        (leaguedashplayerbiostats.LeagueDashPlayerBioStats, "LeagueDashPlayerBioStats", {"season": season, "season_type_all_star": "Regular Season"}),
        
        # Player on/off details
        (leagueplayerondetails.LeaguePlayerOnDetails, "LeaguePlayerOnDetails", {"season": season}),
        
        # Season matchups
        (leagueseasonmatchups.LeagueSeasonMatchups, "LeagueSeasonMatchups", {"season": season}),
        
        # Matchups rollup
        (matchupsrollup.MatchupsRollup, "MatchupsRollup", {"league_id": wnba_league_id, "season": season}),
    ]
    
    working_additional = []
    
    for endpoint_func, name, params in additional_endpoints:
        success, rows, cols = safe_test_endpoint(endpoint_func, name, **params)
        if success and rows > 0:
            working_additional.append((name, rows, cols))
    
    print(f"\n🎉 WORKING ADDITIONAL ENDPOINTS: {len(working_additional)}")
    for name, rows, cols in working_additional:
        print(f"  • {name}: {rows} rows, {len(cols)} columns")
    
    return working_additional

def main():
    """Run comprehensive WNBA endpoint testing with specific parameters"""
    print("🏀 WNBA ENDPOINT TESTING WITH SPECIFIC PARAMETERS 🏀")
    print("="*60)
    
    all_working = []
    
    # Test different categories with specific parameters
    all_working.extend(test_clutch_endpoints())
    all_working.extend(test_context_measure_endpoints())
    all_working.extend(test_conference_endpoints())
    all_working.extend(test_additional_wnba_endpoints())
    
    # Final summary
    print("\n" + "="*60)
    print("🎉 FINAL SUMMARY - ALL WORKING ENDPOINTS 🎉")
    print("="*60)
    print(f"Total working endpoint variations: {len(all_working)}")
    
    for item in all_working:
        if len(item) == 2:  # (name, rows)
            name, rows = item
            print(f"✅ {name}: {rows:,} rows")
        elif len(item) == 3:  # (name, rows, cols)
            name, rows, cols = item
            print(f"✅ {name}: {rows:,} rows, {len(cols)} columns")
    
    print("\n" + "="*60)
    print("PARAMETER TESTING COMPLETE!")
    print("="*60)

if __name__ == "__main__":
    main()
