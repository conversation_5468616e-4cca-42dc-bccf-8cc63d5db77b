#!/usr/bin/env python3
"""
Final WNBA Data Collector - Using verified working endpoints with correct parameters
Based on comprehensive testing results
"""

from nba_api.stats.endpoints import *
import pandas as pd
import sqlite3
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import json
import os
import traceback

class WNBAFinalCollector:
    def __init__(self):
        self.setup_logging()
        self.setup_database()
        self.wnba_league_id = '10'
        # Focus on seasons where we know data is available
        current_date = datetime.now()
        end_year = 2025 if current_date <= datetime(2025, 7, 17) else current_date.year
        self.seasons = self.generate_seasons(2015, end_year)
        self.base_dir = os.getcwd()
        self.data_dir = os.path.join(self.base_dir, 'data')
        
    def setup_logging(self):
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('wnba_final.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def setup_database(self):
        self.conn = sqlite3.connect('wnba_final.db', check_same_thread=False)
        self.create_tables()
        
    def create_tables(self):
        """Create tables for verified working endpoints"""
        tables = {
            'players': '''CREATE TABLE IF NOT EXISTS players (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                player_id TEXT,
                season TEXT,
                player_name TEXT,
                team_id TEXT,
                from_year TEXT,
                to_year TEXT,
                position TEXT,
                height TEXT,
                weight TEXT,
                college TEXT,
                country TEXT,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(player_id, season)
            )''',
            
            'teams': '''CREATE TABLE IF NOT EXISTS teams (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                team_id TEXT,
                season TEXT,
                team_name TEXT,
                conference TEXT,
                wins INTEGER,
                losses INTEGER,
                win_pct REAL,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(team_id, season)
            )''',
            
            'games_finder': '''CREATE TABLE IF NOT EXISTS games_finder (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                season_id TEXT,
                team_id TEXT,
                team_abbreviation TEXT,
                team_name TEXT,
                game_id TEXT,
                game_date TEXT,
                matchup TEXT,
                wl TEXT,
                min INTEGER,
                pts INTEGER,
                fgm INTEGER,
                fga INTEGER,
                fg_pct REAL,
                fg3m INTEGER,
                fg3a INTEGER,
                fg3_pct REAL,
                ftm INTEGER,
                fta INTEGER,
                ft_pct REAL,
                oreb INTEGER,
                dreb INTEGER,
                reb INTEGER,
                ast INTEGER,
                stl INTEGER,
                blk INTEGER,
                tov INTEGER,
                pf INTEGER,
                plus_minus INTEGER,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(game_id, team_id)
            )''',
            
            'games_log': '''CREATE TABLE IF NOT EXISTS games_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                season_id TEXT,
                team_id TEXT,
                team_abbreviation TEXT,
                team_name TEXT,
                game_id TEXT,
                game_date TEXT,
                matchup TEXT,
                wl TEXT,
                min INTEGER,
                pts INTEGER,
                fgm INTEGER,
                fga INTEGER,
                fg_pct REAL,
                fg3m INTEGER,
                fg3a INTEGER,
                fg3_pct REAL,
                ftm INTEGER,
                fta INTEGER,
                ft_pct REAL,
                oreb INTEGER,
                dreb INTEGER,
                reb INTEGER,
                ast INTEGER,
                stl INTEGER,
                blk INTEGER,
                tov INTEGER,
                pf INTEGER,
                plus_minus INTEGER,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(game_id, team_id)
            )''',
            
            'league_leaders': '''CREATE TABLE IF NOT EXISTS league_leaders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                season TEXT,
                player_id TEXT,
                rank_num INTEGER,
                player_name TEXT,
                team_id TEXT,
                team_name TEXT,
                gp INTEGER,
                min REAL,
                pts REAL,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(player_id, season)
            )''',
            
            'team_stats': '''CREATE TABLE IF NOT EXISTS team_stats (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                team_id TEXT,
                season TEXT,
                team_name TEXT,
                gp INTEGER,
                w INTEGER,
                l INTEGER,
                w_pct REAL,
                min REAL,
                pts REAL,
                fgm REAL,
                fga REAL,
                fg_pct REAL,
                fg3m REAL,
                fg3a REAL,
                fg3_pct REAL,
                ftm REAL,
                fta REAL,
                ft_pct REAL,
                oreb REAL,
                dreb REAL,
                reb REAL,
                ast REAL,
                tov REAL,
                stl REAL,
                blk REAL,
                pf REAL,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(team_id, season)
            )''',
            
            'player_stats': '''CREATE TABLE IF NOT EXISTS player_stats (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                player_id TEXT,
                season TEXT,
                player_name TEXT,
                team_id TEXT,
                gp INTEGER,
                min REAL,
                pts REAL,
                fgm REAL,
                fga REAL,
                fg_pct REAL,
                fg3m REAL,
                fg3a REAL,
                fg3_pct REAL,
                ftm REAL,
                fta REAL,
                ft_pct REAL,
                oreb REAL,
                dreb REAL,
                reb REAL,
                ast REAL,
                tov REAL,
                stl REAL,
                blk REAL,
                pf REAL,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(player_id, season)
            )''',
            
            'scoreboard_games': '''CREATE TABLE IF NOT EXISTS scoreboard_games (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                game_id TEXT UNIQUE,
                game_date TEXT,
                season TEXT,
                home_team_id TEXT,
                away_team_id TEXT,
                home_score INTEGER,
                away_score INTEGER,
                game_status TEXT,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )''',
            
            'scoreboard_teams': '''CREATE TABLE IF NOT EXISTS scoreboard_teams (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                team_id TEXT,
                season TEXT,
                team_name TEXT,
                conference TEXT,
                wins INTEGER,
                losses INTEGER,
                win_pct REAL,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(team_id, season)
            )'''
        }
        
        for table_name, query in tables.items():
            self.conn.execute(query)
        self.conn.commit()

    def generate_seasons(self, start_year: int, end_year: int) -> List[str]:
        """Generate season strings from start to end year for WNBA"""
        seasons = []
        current_year = datetime.now().year
        for year in range(start_year, min(end_year + 1, current_year + 1)):
            seasons.append(str(year))
        return seasons
        
    def safe_api_call(self, endpoint_func, **kwargs):
        """Safely call API endpoint with error handling and retries"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                time.sleep(0.6)  # Rate limiting
                result = endpoint_func(**kwargs)
                return result
            except Exception as e:
                self.logger.warning(f"API call failed (attempt {attempt + 1}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)
                else:
                    self.logger.error(f"API call failed after {max_retries} attempts: {e}")
                    return None

    def check_existing_data(self, table_name: str, **conditions) -> bool:
        """Check if data already exists in database"""
        try:
            where_clause = " AND ".join([f"{key} = ?" for key in conditions.keys()])
            query = f"SELECT COUNT(*) FROM {table_name} WHERE {where_clause}"
            cursor = self.conn.execute(query, list(conditions.values()))
            count = cursor.fetchone()[0]
            return count > 0
        except Exception:
            return False

    def collect_players_data(self):
        """Collect WNBA players data - VERIFIED WORKING"""
        self.logger.info("Collecting players data...")

        for season in self.seasons:
            try:
                existing_count = self.conn.execute("SELECT COUNT(*) FROM players WHERE season = ?", (season,)).fetchone()[0]
                if existing_count > 0:
                    self.logger.info(f"Players data for season {season} already exists ({existing_count} records), skipping")
                    continue

                players = self.safe_api_call(
                    commonallplayers.CommonAllPlayers,
                    league_id=self.wnba_league_id,
                    season=season,
                    is_only_current_season=0
                )

                if players:
                    df = players.get_data_frames()[0]
                    new_records = 0
                    for _, row in df.iterrows():
                        if not self.check_existing_data('players', player_id=row.get('PERSON_ID'), season=season):
                            self.conn.execute('''
                                INSERT INTO players
                                (player_id, season, player_name, team_id, from_year, to_year, position, height, weight, college, country)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                            ''', (
                                row.get('PERSON_ID'), season, row.get('DISPLAY_FIRST_LAST'),
                                row.get('TEAM_ID'), row.get('FROM_YEAR'), row.get('TO_YEAR'),
                                row.get('POSITION'), row.get('HEIGHT'), row.get('WEIGHT'),
                                row.get('SCHOOL'), row.get('COUNTRY')
                            ))
                            new_records += 1

                    self.conn.commit()
                    self.logger.info(f"Added {new_records} new player records for season {season}")

            except Exception as e:
                self.logger.error(f"Error collecting players for season {season}: {e}")

    def collect_games_from_finder(self):
        """Collect games using LeagueGameFinder - VERIFIED WORKING (30,000 rows!)"""
        self.logger.info("Collecting games from LeagueGameFinder...")

        try:
            existing_count = self.conn.execute("SELECT COUNT(*) FROM games_finder").fetchone()[0]
            if existing_count > 0:
                self.logger.info(f"Games finder data already exists ({existing_count} records), skipping")
                return

            # Use NO parameters - this works and returns 30,000 rows!
            games_finder = self.safe_api_call(leaguegamefinder.LeagueGameFinder)

            if games_finder:
                df = games_finder.get_data_frames()[0]
                new_records = 0

                self.logger.info(f"Processing {len(df)} game records from LeagueGameFinder...")

                for _, row in df.iterrows():
                    game_id = row.get('GAME_ID')
                    team_id = row.get('TEAM_ID')

                    if game_id and team_id and not self.check_existing_data('games_finder', game_id=game_id, team_id=team_id):
                        self.conn.execute('''
                            INSERT INTO games_finder
                            (season_id, team_id, team_abbreviation, team_name, game_id, game_date, matchup, wl,
                             min, pts, fgm, fga, fg_pct, fg3m, fg3a, fg3_pct, ftm, fta, ft_pct,
                             oreb, dreb, reb, ast, stl, blk, tov, pf, plus_minus)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (
                            row.get('SEASON_ID'), row.get('TEAM_ID'), row.get('TEAM_ABBREVIATION'),
                            row.get('TEAM_NAME'), row.get('GAME_ID'), row.get('GAME_DATE'),
                            row.get('MATCHUP'), row.get('WL'), row.get('MIN'),
                            row.get('PTS'), row.get('FGM'), row.get('FGA'), row.get('FG_PCT'),
                            row.get('FG3M'), row.get('FG3A'), row.get('FG3_PCT'),
                            row.get('FTM'), row.get('FTA'), row.get('FT_PCT'),
                            row.get('OREB'), row.get('DREB'), row.get('REB'),
                            row.get('AST'), row.get('STL'), row.get('BLK'),
                            row.get('TOV'), row.get('PF'), row.get('PLUS_MINUS')
                        ))
                        new_records += 1

                        if new_records % 1000 == 0:
                            self.conn.commit()
                            self.logger.info(f"Processed {new_records} records...")

                self.conn.commit()
                self.logger.info(f"Added {new_records} new game finder records")

        except Exception as e:
            self.logger.error(f"Error collecting games from finder: {e}")

    def collect_games_from_log(self):
        """Collect games using LeagueGameLog - VERIFIED WORKING (2,460 rows!)"""
        self.logger.info("Collecting games from LeagueGameLog...")

        try:
            existing_count = self.conn.execute("SELECT COUNT(*) FROM games_log").fetchone()[0]
            if existing_count > 0:
                self.logger.info(f"Games log data already exists ({existing_count} records), skipping")
                return

            # Use NO parameters - this works and returns 2,460 rows!
            games_log = self.safe_api_call(leaguegamelog.LeagueGameLog)

            if games_log:
                df = games_log.get_data_frames()[0]
                new_records = 0

                self.logger.info(f"Processing {len(df)} game log records from LeagueGameLog...")

                for _, row in df.iterrows():
                    game_id = row.get('GAME_ID')
                    team_id = row.get('TEAM_ID')

                    if game_id and team_id and not self.check_existing_data('games_log', game_id=game_id, team_id=team_id):
                        self.conn.execute('''
                            INSERT INTO games_log
                            (season_id, team_id, team_abbreviation, team_name, game_id, game_date, matchup, wl,
                             min, pts, fgm, fga, fg_pct, fg3m, fg3a, fg3_pct, ftm, fta, ft_pct,
                             oreb, dreb, reb, ast, stl, blk, tov, pf, plus_minus)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (
                            row.get('SEASON_ID'), row.get('TEAM_ID'), row.get('TEAM_ABBREVIATION'),
                            row.get('TEAM_NAME'), row.get('GAME_ID'), row.get('GAME_DATE'),
                            row.get('MATCHUP'), row.get('WL'), row.get('MIN'),
                            row.get('PTS'), row.get('FGM'), row.get('FGA'), row.get('FG_PCT'),
                            row.get('FG3M'), row.get('FG3A'), row.get('FG3_PCT'),
                            row.get('FTM'), row.get('FTA'), row.get('FT_PCT'),
                            row.get('OREB'), row.get('DREB'), row.get('REB'),
                            row.get('AST'), row.get('STL'), row.get('BLK'),
                            row.get('TOV'), row.get('PF'), row.get('PLUS_MINUS')
                        ))
                        new_records += 1

                        if new_records % 500 == 0:
                            self.conn.commit()
                            self.logger.info(f"Processed {new_records} records...")

                self.conn.commit()
                self.logger.info(f"Added {new_records} new game log records")

        except Exception as e:
            self.logger.error(f"Error collecting games from log: {e}")

    def collect_league_leaders(self):
        """Collect league leaders - VERIFIED WORKING (157 rows with correct params!)"""
        self.logger.info("Collecting league leaders...")

        for season in self.seasons:
            try:
                existing_count = self.conn.execute("SELECT COUNT(*) FROM league_leaders WHERE season = ?", (season,)).fetchone()[0]
                if existing_count > 0:
                    self.logger.info(f"League leaders for season {season} already exists ({existing_count} records), skipping")
                    continue

                # Use verified working parameters: league_id and season
                leaders = self.safe_api_call(
                    leagueleaders.LeagueLeaders,
                    league_id=self.wnba_league_id,
                    season=season
                )

                if leaders:
                    df = leaders.get_data_frames()[0]
                    new_records = 0

                    for _, row in df.iterrows():
                        if not self.check_existing_data('league_leaders', player_id=row.get('PLAYER_ID'), season=season):
                            self.conn.execute('''
                                INSERT INTO league_leaders
                                (season, player_id, rank_num, player_name, team_id, team_name, gp, min, pts)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                            ''', (
                                season, row.get('PLAYER_ID'), row.get('RANK'),
                                row.get('PLAYER'), row.get('TEAM_ID'), row.get('TEAM'),
                                row.get('GP'), row.get('MIN'), row.get('PTS')
                            ))
                            new_records += 1

                    self.conn.commit()
                    self.logger.info(f"Added {new_records} new league leader records for season {season}")

            except Exception as e:
                self.logger.error(f"Error collecting league leaders for season {season}: {e}")

    def collect_team_stats(self):
        """Collect team statistics - VERIFIED WORKING"""
        self.logger.info("Collecting team stats...")

        for season in self.seasons:
            try:
                existing_count = self.conn.execute("SELECT COUNT(*) FROM team_stats WHERE season = ?", (season,)).fetchone()[0]
                if existing_count > 0:
                    self.logger.info(f"Team stats for season {season} already exists ({existing_count} records), skipping")
                    continue

                team_stats = self.safe_api_call(
                    leaguedashteamstats.LeagueDashTeamStats,
                    season=season,
                    season_type_all_star='Regular Season'
                )

                if team_stats:
                    df = team_stats.get_data_frames()[0]
                    new_records = 0
                    for _, row in df.iterrows():
                        if not self.check_existing_data('team_stats', team_id=row.get('TEAM_ID'), season=season):
                            self.conn.execute('''
                                INSERT INTO team_stats
                                (team_id, season, team_name, gp, w, l, w_pct, min, pts, fgm, fga, fg_pct,
                                 fg3m, fg3a, fg3_pct, ftm, fta, ft_pct, oreb, dreb, reb, ast, tov, stl, blk, pf)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                            ''', (
                                row.get('TEAM_ID'), season, row.get('TEAM_NAME'),
                                row.get('GP'), row.get('W'), row.get('L'), row.get('W_PCT'),
                                row.get('MIN'), row.get('PTS'), row.get('FGM'), row.get('FGA'), row.get('FG_PCT'),
                                row.get('FG3M'), row.get('FG3A'), row.get('FG3_PCT'),
                                row.get('FTM'), row.get('FTA'), row.get('FT_PCT'),
                                row.get('OREB'), row.get('DREB'), row.get('REB'),
                                row.get('AST'), row.get('TOV'), row.get('STL'), row.get('BLK'), row.get('PF')
                            ))
                            new_records += 1

                    self.conn.commit()
                    self.logger.info(f"Added {new_records} new team stat records for season {season}")

            except Exception as e:
                self.logger.error(f"Error collecting team stats for season {season}: {e}")

    def collect_player_stats(self):
        """Collect player statistics - VERIFIED WORKING"""
        self.logger.info("Collecting player stats...")

        for season in self.seasons:
            try:
                existing_count = self.conn.execute("SELECT COUNT(*) FROM player_stats WHERE season = ?", (season,)).fetchone()[0]
                if existing_count > 0:
                    self.logger.info(f"Player stats for season {season} already exists ({existing_count} records), skipping")
                    continue

                player_stats = self.safe_api_call(
                    leaguedashplayerstats.LeagueDashPlayerStats,
                    season=season,
                    season_type_all_star='Regular Season'
                )

                if player_stats:
                    df = player_stats.get_data_frames()[0]
                    new_records = 0
                    for _, row in df.iterrows():
                        if not self.check_existing_data('player_stats', player_id=row.get('PLAYER_ID'), season=season):
                            self.conn.execute('''
                                INSERT INTO player_stats
                                (player_id, season, player_name, team_id, gp, min, pts, fgm, fga, fg_pct,
                                 fg3m, fg3a, fg3_pct, ftm, fta, ft_pct, oreb, dreb, reb, ast, tov, stl, blk, pf)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                            ''', (
                                row.get('PLAYER_ID'), season, row.get('PLAYER_NAME'),
                                row.get('TEAM_ID'), row.get('GP'), row.get('MIN'),
                                row.get('PTS'), row.get('FGM'), row.get('FGA'), row.get('FG_PCT'),
                                row.get('FG3M'), row.get('FG3A'), row.get('FG3_PCT'),
                                row.get('FTM'), row.get('FTA'), row.get('FT_PCT'),
                                row.get('OREB'), row.get('DREB'), row.get('REB'),
                                row.get('AST'), row.get('TOV'), row.get('STL'), row.get('BLK'), row.get('PF')
                            ))
                            new_records += 1

                    self.conn.commit()
                    self.logger.info(f"Added {new_records} new player stat records for season {season}")

            except Exception as e:
                self.logger.error(f"Error collecting player stats for season {season}: {e}")

    def collect_scoreboard_data(self):
        """Collect teams and games from scoreboard - VERIFIED WORKING"""
        self.logger.info("Collecting scoreboard data...")

        # Focus on 2024 season where we know data exists
        for season in ['2024']:
            try:
                # Sample dates throughout the WNBA season (May-October)
                season_dates = []
                start_date = datetime(2024, 5, 15)
                end_date = datetime(2024, 10, 15)
                current_date = start_date

                # Sample every 7 days to get good coverage
                while current_date <= end_date:
                    season_dates.append(current_date)
                    current_date += timedelta(days=7)

                teams_collected = set()
                games_collected = 0

                for game_date in season_dates:
                    try:
                        scoreboard = self.safe_api_call(
                            scoreboardv2.ScoreboardV2,
                            league_id=self.wnba_league_id,
                            game_date=game_date.strftime('%m/%d/%Y')
                        )

                        if scoreboard:
                            data_frames = scoreboard.get_data_frames()
                            if len(data_frames) >= 5:
                                # Games data
                                games_df = data_frames[0]
                                for _, row in games_df.iterrows():
                                    game_id = row.get('GAME_ID')
                                    if game_id and not self.check_existing_data('scoreboard_games', game_id=game_id):
                                        self.conn.execute('''
                                            INSERT INTO scoreboard_games
                                            (game_id, game_date, season, home_team_id, away_team_id, game_status)
                                            VALUES (?, ?, ?, ?, ?, ?)
                                        ''', (
                                            game_id, row.get('GAME_DATE_EST'), season,
                                            row.get('HOME_TEAM_ID'), row.get('VISITOR_TEAM_ID'),
                                            row.get('GAME_STATUS_TEXT')
                                        ))
                                        games_collected += 1

                                # Teams data from standings
                                for standings_idx in [4, 5]:  # East and West standings
                                    if len(data_frames) > standings_idx:
                                        standings_df = data_frames[standings_idx]
                                        for _, row in standings_df.iterrows():
                                            team_id = row.get('TEAM_ID')
                                            if team_id and team_id not in teams_collected:
                                                if not self.check_existing_data('scoreboard_teams', team_id=team_id, season=season):
                                                    self.conn.execute('''
                                                        INSERT INTO scoreboard_teams
                                                        (team_id, season, team_name, conference, wins, losses, win_pct)
                                                        VALUES (?, ?, ?, ?, ?, ?, ?)
                                                    ''', (
                                                        team_id, season, row.get('TEAM'),
                                                        row.get('CONFERENCE'), row.get('W'), row.get('L'), row.get('W_PCT')
                                                    ))
                                                    teams_collected.add(team_id)

                    except Exception as e:
                        self.logger.warning(f"Error collecting scoreboard for date {game_date}: {e}")
                        continue

                self.conn.commit()
                self.logger.info(f"Collected {len(teams_collected)} teams and {games_collected} games from scoreboard for season {season}")

            except Exception as e:
                self.logger.error(f"Error in scoreboard collection for season {season}: {e}")

    def collect_all_data(self):
        """Collect all available WNBA data using verified working endpoints"""
        self.logger.info("Starting final WNBA data collection with verified endpoints...")

        # Collect data in order of importance
        self.collect_players_data()
        self.collect_games_from_finder()  # 30,000 rows!
        self.collect_games_from_log()     # 2,460 rows!
        self.collect_league_leaders()     # 157 rows per season!
        self.collect_team_stats()
        self.collect_player_stats()
        self.collect_scoreboard_data()

        self.logger.info("Data collection completed!")

    def export_to_csv(self):
        """Export all data to CSV files"""
        self.logger.info("Exporting data to CSV files...")

        os.makedirs(self.data_dir, exist_ok=True)

        tables = ['players', 'games_finder', 'games_log', 'league_leaders', 'team_stats',
                 'player_stats', 'scoreboard_games', 'scoreboard_teams']

        for table in tables:
            try:
                df = pd.read_sql_query(f"SELECT * FROM {table}", self.conn)
                if not df.empty:
                    csv_path = os.path.join(self.data_dir, f"{table}.csv")
                    df.to_csv(csv_path, index=False)
                    self.logger.info(f"Exported {len(df)} records to {csv_path}")
                else:
                    self.logger.warning(f"No data found for table {table}")
            except Exception as e:
                self.logger.error(f"Error exporting {table} to CSV: {e}")

    def get_collection_summary(self):
        """Get a summary of collected data"""
        summary = {}
        tables = ['players', 'games_finder', 'games_log', 'league_leaders', 'team_stats',
                 'player_stats', 'scoreboard_games', 'scoreboard_teams']

        for table in tables:
            try:
                count = self.conn.execute(f"SELECT COUNT(*) FROM {table}").fetchone()[0]
                summary[table] = count
            except Exception:
                summary[table] = 0

        return summary

    def print_summary(self):
        """Print collection summary"""
        summary = self.get_collection_summary()

        print("\n" + "=" * 60)
        print("WNBA DATA COLLECTION SUMMARY")
        print("=" * 60)

        total_records = 0
        for table, count in summary.items():
            print(f"{table.replace('_', ' ').title()}: {count:,} records")
            total_records += count

        print(f"\nTotal Records: {total_records:,}")
        print("=" * 60)

    def close(self):
        """Close database connection"""
        if hasattr(self, 'conn'):
            self.conn.close()
            self.logger.info("Database connection closed")


if __name__ == "__main__":
    collector = WNBAFinalCollector()

    try:
        print("🏀 STARTING FINAL WNBA DATA COLLECTION 🏀")
        print("Using VERIFIED working endpoints with correct parameters!")
        print(f"Seasons: {', '.join(collector.seasons)}")
        print(f"Database: wnba_final.db")
        print(f"Data directory: {collector.data_dir}")
        print("=" * 60)

        collector.collect_all_data()
        collector.export_to_csv()

        print("\n" + "=" * 60)
        print("🎉 COLLECTION COMPLETED SUCCESSFULLY! 🎉")
        print("=" * 60)

        collector.print_summary()

    except KeyboardInterrupt:
        print("\nCollection interrupted by user")
    except Exception as e:
        print(f"Collection failed: {e}")
        traceback.print_exc()
    finally:
        collector.close()
