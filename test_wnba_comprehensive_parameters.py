#!/usr/bin/env python3
"""
Comprehensive WNBA endpoint testing using detailed parameter documentation
"""

from nba_api.stats.endpoints import *
import pandas as pd
import time
from datetime import datetime

def safe_test_endpoint(endpoint_func, endpoint_name, **kwargs):
    """Safely test an endpoint and return results"""
    print(f"\nTesting {endpoint_name}...")
    print(f"Parameters: {kwargs}")
    
    try:
        time.sleep(0.6)
        result = endpoint_func(**kwargs)
        
        if result:
            data_frames = result.get_data_frames()
            total_rows = sum(len(df) for df in data_frames)
            print(f"✅ SUCCESS! {len(data_frames)} dataframes, {total_rows} total rows")
            
            if total_rows > 0:
                print(f"Sample columns: {list(data_frames[0].columns)[:5]}...")
                return True, total_rows, list(data_frames[0].columns)
            else:
                print("⚠️  SUCCESS but 0 rows")
                return True, 0, []
        else:
            print("❌ No result returned")
            return False, 0, []
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False, 0, []

def test_defense_category_endpoints():
    """Test endpoints with defense category parameters"""
    print("\n" + "="*60)
    print("TESTING DEFENSE CATEGORY ENDPOINTS")
    print("="*60)
    
    season = '2024'
    defense_categories = [
        "Overall",           # default
        "3 Pointers",
        "2 Pointers", 
        "Less Than 6Ft",
        "Less Than 10Ft",
        "Greater Than 15Ft"
    ]
    
    working_defense = []
    
    for defense_cat in defense_categories:
        print(f"\n--- Testing with defense_category: {defense_cat} ---")
        
        # Test defense hub with specific defense category
        success, rows, cols = safe_test_endpoint(
            defensehub.DefenseHub,
            f"DefenseHub_{defense_cat.replace(' ', '_')}",
            season=season,
            season_type_all_star='Regular Season',
            defense_category=defense_cat
        )
        if success and rows > 0:
            working_defense.append((f"DefenseHub_{defense_cat}", rows))
    
    print(f"\n🎉 WORKING DEFENSE CATEGORY ENDPOINTS: {len(working_defense)}")
    for name, rows in working_defense:
        print(f"  • {name}: {rows} rows")
    
    return working_defense

def test_measure_type_endpoints():
    """Test endpoints with different measure types"""
    print("\n" + "="*60)
    print("TESTING MEASURE TYPE ENDPOINTS")
    print("="*60)
    
    season = '2024'
    
    # Test different measure types
    measure_types = [
        "Base",           # default
        "Advanced",
        "Misc",
        "Four Factors",
        "Scoring",
        "Opponent",
        "Usage",
        "Defense"
    ]
    
    working_measure = []
    
    for measure_type in measure_types:
        print(f"\n--- Testing with measure_type: {measure_type} ---")
        
        # Test league dash player stats with different measure types
        success, rows, cols = safe_test_endpoint(
            leaguedashplayerstats.LeagueDashPlayerStats,
            f"LeagueDashPlayerStats_{measure_type.replace(' ', '_')}",
            season=season,
            season_type_all_star='Regular Season',
            measure_type_detailed_defense=measure_type
        )
        if success and rows > 0:
            working_measure.append((f"PlayerStats_{measure_type}", rows))
        
        # Test league dash team stats with different measure types
        success, rows, cols = safe_test_endpoint(
            leaguedashteamstats.LeagueDashTeamStats,
            f"LeagueDashTeamStats_{measure_type.replace(' ', '_')}",
            season=season,
            season_type_all_star='Regular Season',
            measure_type_detailed_defense=measure_type
        )
        if success and rows > 0:
            working_measure.append((f"TeamStats_{measure_type}", rows))
    
    print(f"\n🎉 WORKING MEASURE TYPE ENDPOINTS: {len(working_measure)}")
    for name, rows in working_measure:
        print(f"  • {name}: {rows} rows")
    
    return working_measure

def test_per_mode_endpoints():
    """Test endpoints with different per mode parameters"""
    print("\n" + "="*60)
    print("TESTING PER MODE ENDPOINTS")
    print("="*60)
    
    season = '2024'
    
    # Test different per modes
    per_modes = [
        "Totals",           # default
        "PerGame",
        "Per36",
        "Per48",
        "Per40",
        "PerMinute",
        "PerPossession",
        "Per100Possessions"
    ]
    
    working_per_mode = []
    
    for per_mode in per_modes:
        print(f"\n--- Testing with per_mode: {per_mode} ---")
        
        # Test league dash player stats with different per modes
        success, rows, cols = safe_test_endpoint(
            leaguedashplayerstats.LeagueDashPlayerStats,
            f"LeagueDashPlayerStats_{per_mode}",
            season=season,
            season_type_all_star='Regular Season',
            per_mode_detailed=per_mode
        )
        if success and rows > 0:
            working_per_mode.append((f"PlayerStats_{per_mode}", rows))
        
        # Test league dash team stats with different per modes
        success, rows, cols = safe_test_endpoint(
            leaguedashteamstats.LeagueDashTeamStats,
            f"LeagueDashTeamStats_{per_mode}",
            season=season,
            season_type_all_star='Regular Season',
            per_mode_detailed=per_mode
        )
        if success and rows > 0:
            working_per_mode.append((f"TeamStats_{per_mode}", rows))
    
    print(f"\n🎉 WORKING PER MODE ENDPOINTS: {len(working_per_mode)}")
    for name, rows in working_per_mode:
        print(f"  • {name}: {rows} rows")
    
    return working_per_mode

def test_pt_measure_type_endpoints():
    """Test player tracking endpoints with different measure types"""
    print("\n" + "="*60)
    print("TESTING PLAYER TRACKING MEASURE TYPE ENDPOINTS")
    print("="*60)
    
    season = '2024'
    
    # Test all player tracking measure types
    pt_measure_types = [
        "SpeedDistance",     # default
        "Rebounding",
        "Possessions",
        "CatchShoot",
        "PullUpShot",
        "Defense",
        "Drives",
        "Passing",
        "ElbowTouch",
        "PostTouch",
        "PaintTouch",
        "Efficiency"
    ]
    
    working_pt = []
    
    for pt_measure in pt_measure_types:
        print(f"\n--- Testing with pt_measure_type: {pt_measure} ---")
        
        # Test league dash pt stats
        success, rows, cols = safe_test_endpoint(
            leaguedashptstats.LeagueDashPtStats,
            f"LeagueDashPtStats_{pt_measure}",
            season=season,
            season_type_all_star='Regular Season',
            pt_measure_type=pt_measure
        )
        if success and rows > 0:
            working_pt.append((f"PtStats_{pt_measure}", rows))
        
        # Test league dash pt defend
        if pt_measure == "Defense":
            success, rows, cols = safe_test_endpoint(
                leaguedashptdefend.LeagueDashPtDefend,
                f"LeagueDashPtDefend_{pt_measure}",
                season=season,
                season_type_all_star='Regular Season'
            )
            if success and rows > 0:
                working_pt.append((f"PtDefend_{pt_measure}", rows))
    
    print(f"\n🎉 WORKING PT MEASURE TYPE ENDPOINTS: {len(working_pt)}")
    for name, rows in working_pt:
        print(f"  • {name}: {rows} rows")
    
    return working_pt

def test_player_experience_endpoints():
    """Test endpoints with player experience parameters"""
    print("\n" + "="*60)
    print("TESTING PLAYER EXPERIENCE ENDPOINTS")
    print("="*60)
    
    season = '2024'
    
    # Test different player experience levels
    experience_levels = [
        "Rookie",
        "Sophomore", 
        "Veteran"
    ]
    
    working_experience = []
    
    for experience in experience_levels:
        print(f"\n--- Testing with player_experience: {experience} ---")
        
        # Test league dash player stats with experience filter
        success, rows, cols = safe_test_endpoint(
            leaguedashplayerstats.LeagueDashPlayerStats,
            f"LeagueDashPlayerStats_{experience}",
            season=season,
            season_type_all_star='Regular Season',
            player_experience_nullable=experience
        )
        if success and rows > 0:
            working_experience.append((f"PlayerStats_{experience}", rows))
    
    print(f"\n🎉 WORKING PLAYER EXPERIENCE ENDPOINTS: {len(working_experience)}")
    for name, rows in working_experience:
        print(f"  • {name}: {rows} rows")
    
    return working_experience

def test_location_endpoints():
    """Test endpoints with home/road location parameters"""
    print("\n" + "="*60)
    print("TESTING LOCATION ENDPOINTS")
    print("="*60)
    
    season = '2024'
    
    # Test different locations
    locations = ["Home", "Road"]
    
    working_location = []
    
    for location in locations:
        print(f"\n--- Testing with location: {location} ---")
        
        # Test league dash player stats with location filter
        success, rows, cols = safe_test_endpoint(
            leaguedashplayerstats.LeagueDashPlayerStats,
            f"LeagueDashPlayerStats_{location}",
            season=season,
            season_type_all_star='Regular Season',
            location_nullable=location
        )
        if success and rows > 0:
            working_location.append((f"PlayerStats_{location}", rows))
        
        # Test league dash team stats with location filter
        success, rows, cols = safe_test_endpoint(
            leaguedashteamstats.LeagueDashTeamStats,
            f"LeagueDashTeamStats_{location}",
            season=season,
            season_type_all_star='Regular Season',
            location_nullable=location
        )
        if success and rows > 0:
            working_location.append((f"TeamStats_{location}", rows))
    
    print(f"\n🎉 WORKING LOCATION ENDPOINTS: {len(working_location)}")
    for name, rows in working_location:
        print(f"  • {name}: {rows} rows")
    
    return working_location

def test_outcome_endpoints():
    """Test endpoints with win/loss outcome parameters"""
    print("\n" + "="*60)
    print("TESTING OUTCOME ENDPOINTS")
    print("="*60)
    
    season = '2024'
    
    # Test different outcomes
    outcomes = ["W", "L"]  # Win, Loss
    
    working_outcome = []
    
    for outcome in outcomes:
        print(f"\n--- Testing with outcome: {outcome} ---")
        
        # Test league dash player stats with outcome filter
        success, rows, cols = safe_test_endpoint(
            leaguedashplayerstats.LeagueDashPlayerStats,
            f"LeagueDashPlayerStats_{outcome}",
            season=season,
            season_type_all_star='Regular Season',
            outcome_nullable=outcome
        )
        if success and rows > 0:
            working_outcome.append((f"PlayerStats_{outcome}", rows))
        
        # Test league dash team stats with outcome filter
        success, rows, cols = safe_test_endpoint(
            leaguedashteamstats.LeagueDashTeamStats,
            f"LeagueDashTeamStats_{outcome}",
            season=season,
            season_type_all_star='Regular Season',
            outcome_nullable=outcome
        )
        if success and rows > 0:
            working_outcome.append((f"TeamStats_{outcome}", rows))
    
    print(f"\n🎉 WORKING OUTCOME ENDPOINTS: {len(working_outcome)}")
    for name, rows in working_outcome:
        print(f"  • {name}: {rows} rows")
    
    return working_outcome

def main():
    """Run comprehensive WNBA endpoint testing with detailed parameters"""
    print("🏀 COMPREHENSIVE WNBA PARAMETER TESTING 🏀")
    print("="*60)
    
    all_working = []
    
    # Test different parameter categories
    all_working.extend(test_defense_category_endpoints())
    all_working.extend(test_measure_type_endpoints())
    all_working.extend(test_per_mode_endpoints())
    all_working.extend(test_pt_measure_type_endpoints())
    all_working.extend(test_player_experience_endpoints())
    all_working.extend(test_location_endpoints())
    all_working.extend(test_outcome_endpoints())
    
    # Final summary
    print("\n" + "="*60)
    print("🎉 FINAL SUMMARY - ALL WORKING PARAMETER COMBINATIONS 🎉")
    print("="*60)
    print(f"Total working parameter combinations: {len(all_working)}")
    
    for name, rows in all_working:
        print(f"✅ {name}: {rows:,} rows")
    
    print("\n" + "="*60)
    print("COMPREHENSIVE PARAMETER TESTING COMPLETE!")
    print("="*60)

if __name__ == "__main__":
    main()
