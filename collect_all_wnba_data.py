#!/usr/bin/env python3
"""
Collect ALL WNBA Data: 2015-2025
Force collection of all available data regardless of existing records
"""

from wnba_data_collector import WNBAComprehensiveCollector
import sqlite3
import os
from datetime import datetime

def force_collect_all_data():
    """Force collection of all WNBA data from 2015-2025"""
    print("COLLECTING ALL WNBA DATA: 2015-2025")
    print("=" * 80)
    print("FORCING COMPLETE DATA COLLECTION")
    print("=" * 80)
    
    start_time = datetime.now()
    
    # Create collector
    collector = WNBAComprehensiveCollector()
    
    try:
        print(f"Target seasons: {collector.seasons}")
        print(f"Start time: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Phase 1: Foundation Data - FORCE COLLECTION
        print(f"\nPHASE 1: FOUNDATION DATA (FORCED)")
        print("-" * 60)
        
        print("1. Collecting teams for ALL seasons...")
        # Temporarily clear existing team data to force re-collection
        collector.conn.execute("DELETE FROM teams WHERE season NOT IN ('2024', '2025')")
        collector.conn.commit()
        
        collector.collect_teams_data()
        collector.force_save()
        
        print("2. Collecting players for ALL seasons...")
        # Clear existing player data for historical seasons to force re-collection
        collector.conn.execute("DELETE FROM players WHERE season NOT IN ('2024', '2025')")
        collector.conn.commit()
        
        collector.collect_players_data()
        collector.force_save()
        
        # Check what we got
        teams = collector.conn.execute("SELECT COUNT(*) FROM teams").fetchone()[0]
        players = collector.conn.execute("SELECT COUNT(*) FROM players").fetchone()[0]
        
        # Show season breakdown
        cursor = collector.conn.execute("""
            SELECT season, COUNT(*) as team_count 
            FROM teams 
            GROUP BY season 
            ORDER BY season
        """)
        print("Teams by season:")
        for season, count in cursor.fetchall():
            print(f"  {season}: {count} teams")
        
        cursor = collector.conn.execute("""
            SELECT season, COUNT(*) as player_count 
            FROM players 
            GROUP BY season 
            ORDER BY season
        """)
        print("Players by season:")
        for season, count in cursor.fetchall():
            print(f"  {season}: {count} players")
        
        print(f"Foundation: {teams} teams | {players} players")
        
        # Phase 2: Game Data
        print(f"\nPHASE 2: GAME DATA")
        print("-" * 60)
        
        print("3. Collecting games schedule...")
        # Note: Games are collected through other methods
        # collector.collect_games_data()  # This method doesn't exist
        collector.force_save()
        
        print("4. Collecting player game logs...")
        collector.collect_player_game_logs()
        collector.force_save()
        
        print("5. Collecting team game logs...")
        collector.collect_team_game_logs()
        collector.force_save()
        
        print("6. Collecting league game logs...")
        collector.collect_league_game_log()
        collector.force_save()
        
        # Check game data
        games = collector.conn.execute("SELECT COUNT(*) FROM games").fetchone()[0]
        player_logs = collector.conn.execute("SELECT COUNT(*) FROM player_game_logs").fetchone()[0]
        team_logs = collector.conn.execute("SELECT COUNT(*) FROM team_game_logs").fetchone()[0]
        league_logs = collector.conn.execute("SELECT COUNT(*) FROM league_game_log").fetchone()[0]
        
        print(f"Game Data: {games} games | {player_logs} player logs | {team_logs} team logs | {league_logs} league logs")
        
        # Phase 3: Advanced Analytics
        print(f"\nPHASE 3: ADVANCED ANALYTICS")
        print("-" * 60)
        
        print("7. Collecting team rosters...")
        collector.collect_team_rosters()
        collector.force_save()
        
        print("8. Collecting player trends...")
        collector.collect_player_last_n_games_analytics()
        collector.force_save()
        
        print("9. Collecting shot analytics...")
        collector.collect_league_dash_player_pt_shot()
        collector.force_save()
        
        print("10. Collecting clutch performance...")
        collector.collect_league_dash_player_clutch()
        collector.force_save()
        
        print("11. Collecting defensive analytics...")
        collector.collect_defense_hub_stats()
        collector.collect_league_dash_pt_team_defend()
        collector.force_save()
        
        print("12. Collecting lineup analytics...")
        collector.collect_league_dash_lineups()
        collector.collect_league_lineup_viz()
        collector.force_save()
        
        print("13. Collecting matchup analytics...")
        collector.collect_team_vs_player_matchups()
        collector.force_save()
        
        print("14. Collecting play type analytics...")
        collector.collect_synergy_play_types()
        collector.force_save()
        
        print("15. Collecting season data...")
        collector.collect_team_game_streaks()
        collector.collect_playoff_picture()
        collector.force_save()
        
        print("16. Collecting game-level analytics...")
        collector.collect_boxscore_defensive_v2()
        collector.collect_boxscore_four_factors_v2()
        collector.collect_boxscore_usage_v2()
        collector.collect_boxscore_matchups_v3()
        collector.force_save()
        
        print("17. Collecting rotations...")
        collector.collect_game_rotations()
        collector.force_save()
        
        print("18. Collecting additional stats...")
        collector.collect_hustle_stats()
        collector.collect_team_stats()
        collector.collect_shot_chart_data()
        collector.collect_tracking_stats()
        collector.force_save()
        
        # Final Statistics
        end_time = datetime.now()
        duration = end_time - start_time
        
        print(f"\nCOLLECTION COMPLETED!")
        print("=" * 80)
        
        # Get comprehensive statistics
        all_tables = [
            'teams', 'players', 'games', 'player_game_logs', 'team_game_logs', 'league_game_log',
            'team_rosters', 'team_coaches', 'player_last_n_games', 'league_dash_player_pt_shot',
            'league_dash_player_clutch', 'defense_hub_stats', 'league_dash_pt_team_defend',
            'league_dash_lineups', 'league_lineup_viz', 'team_vs_player_matchups',
            'synergy_play_types', 'team_game_streaks', 'playoff_picture',
            'boxscore_defensive_v2', 'boxscore_four_factors_v2', 'boxscore_usage_v2',
            'boxscore_matchups_v3', 'game_rotations', 'hustle_stats', 'team_stats',
            'shot_chart_data', 'tracking_stats'
        ]
        
        total_records = 0
        working_tables = 0
        
        print("FINAL DATA SUMMARY:")
        for table in all_tables:
            try:
                count = collector.conn.execute(f"SELECT COUNT(*) FROM {table}").fetchone()[0]
                total_records += count
                if count > 0:
                    working_tables += 1
                print(f"  {table.replace('_', ' ').title()}: {count:,}")
            except:
                print(f"  {table.replace('_', ' ').title()}: 0")
        
        # Season coverage analysis
        print(f"\nSEASON COVERAGE:")
        try:
            cursor = collector.conn.execute("""
                SELECT season, 
                       COUNT(DISTINCT CASE WHEN table_name = 'teams' THEN team_id END) as teams,
                       COUNT(DISTINCT CASE WHEN table_name = 'players' THEN player_id END) as players,
                       COUNT(DISTINCT CASE WHEN table_name = 'games' THEN game_id END) as games
                FROM (
                    SELECT season, team_id, NULL as player_id, NULL as game_id, 'teams' as table_name FROM teams
                    UNION ALL
                    SELECT season, NULL as team_id, player_id, NULL as game_id, 'players' as table_name FROM players
                    UNION ALL
                    SELECT season, NULL as team_id, NULL as player_id, game_id, 'games' as table_name FROM games
                )
                GROUP BY season
                ORDER BY season
            """)
            
            seasons_covered = 0
            for season, teams, players, games in cursor.fetchall():
                if teams > 0 or players > 0 or games > 0:
                    seasons_covered += 1
                    print(f"  {season}: {teams} teams, {players} players, {games} games")
            
            print(f"\nOVERALL SUMMARY:")
            print(f"  Total Records: {total_records:,}")
            print(f"  Working Tables: {working_tables}/{len(all_tables)} ({working_tables/len(all_tables)*100:.0f}%)")
            print(f"  Seasons Covered: {seasons_covered}/11 (2015-2025)")
            print(f"  Duration: {duration}")
            print(f"  Database Size: {os.path.getsize('wnba_comprehensive.db') / (1024*1024):.1f} MB")
            
        except Exception as e:
            print(f"Error in season analysis: {e}")
        
        # Export data
        print(f"\nEXPORTING DATA...")
        collector.export_to_csv()
        
        print(f"\nWNBA DATABASE COMPLETE!")
        print(f"  Database: wnba_comprehensive.db")
        print(f"  CSV Files: wnba_data/ directory")
        print(f"  Coverage: 2015-2025")
        print(f"  Ready for analysis!")
        
    except Exception as e:
        print(f"Collection error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        collector.close()

def main():
    """Main execution"""
    force_collect_all_data()

if __name__ == "__main__":
    main()
