#!/usr/bin/env python3
"""
Debug WNBA Teams Collection
"""

from nba_api.stats.endpoints import scoreboardv2, commonteamyears
import pandas as pd
import time
from datetime import datetime

def debug_teams_collection():
    """Debug why teams collection isn't working"""
    print("🔍 DEBUGGING WNBA TEAMS COLLECTION")
    print("=" * 50)
    
    wnba_league_id = '10'
    
    try:
        print("1. Testing CommonTeamYears endpoint...")
        team_years = commonteamyears.CommonTeamYears(league_id=wnba_league_id)
        teams_df = team_years.get_data_frames()[0]
        print(f"   ✅ CommonTeamYears returned {len(teams_df)} records")
        
        if len(teams_df) > 0:
            print("   Sample teams:")
            for i, (_, row) in enumerate(teams_df.head(5).iterrows()):
                print(f"     • {row.get('ABBREVIATION')} - {row.get('TEAM_NAME')} ({row.get('MIN_YEAR')}-{row.get('MAX_YEAR')})")
        
        print("\n2. Testing ScoreboardV2 endpoint for 2024...")
        # Try different dates in 2024 WNBA season
        test_dates = [
            datetime(2024, 5, 15),  # Early season
            datetime(2024, 6, 15),  # Mid season
            datetime(2024, 7, 15),  # Mid season
            datetime(2024, 8, 15),  # Late season
        ]
        
        teams_found = set()
        
        for test_date in test_dates:
            try:
                print(f"   Testing date: {test_date.strftime('%m/%d/%Y')}")
                time.sleep(1)
                
                scoreboard = scoreboardv2.ScoreboardV2(
                    league_id=wnba_league_id,
                    game_date=test_date.strftime('%m/%d/%Y')
                )
                
                data_frames = scoreboard.get_data_frames()
                print(f"     Returned {len(data_frames)} dataframes")
                
                # Check for team data in standings (usually dataframes 4 and 5)
                for i, df in enumerate(data_frames):
                    print(f"     DataFrame {i}: {len(df)} rows, columns: {list(df.columns)[:5]}...")
                    
                    if 'TEAM_ID' in df.columns and 'TEAM' in df.columns:
                        print(f"       Found team data in DataFrame {i}!")
                        for _, row in df.iterrows():
                            team_id = row.get('TEAM_ID')
                            team_name = row.get('TEAM') or row.get('TEAM_NAME')
                            if team_id and team_name:
                                teams_found.add((team_id, team_name))
                                print(f"         • {team_name} (ID: {team_id})")
                
            except Exception as e:
                print(f"     ❌ Error for date {test_date.strftime('%m/%d/%Y')}: {e}")
        
        print(f"\n3. Summary of teams found:")
        print(f"   Total unique teams: {len(teams_found)}")
        for team_id, team_name in sorted(teams_found):
            print(f"     • {team_name} (ID: {team_id})")
        
        if len(teams_found) == 0:
            print("\n4. Trying alternative approach - get teams from players...")
            
            from nba_api.stats.endpoints import commonallplayers
            players = commonallplayers.CommonAllPlayers(
                league_id=wnba_league_id,
                season='2024',
                is_only_current_season=1
            )
            players_df = players.get_data_frames()[0]
            
            if len(players_df) > 0:
                unique_teams = players_df['TEAM_ID'].dropna().unique()
                print(f"   Found {len(unique_teams)} unique team IDs from players:")
                
                team_counts = players_df['TEAM_ID'].value_counts()
                for team_id in unique_teams[:10]:  # Show top 10
                    player_count = team_counts.get(team_id, 0)
                    print(f"     • Team ID {team_id}: {player_count} players")
        
        print("\n5. Testing with current date scoreboard...")
        try:
            current_scoreboard = scoreboardv2.ScoreboardV2(
                league_id=wnba_league_id,
                game_date=datetime.now().strftime('%m/%d/%Y')
            )
            current_data = current_scoreboard.get_data_frames()
            print(f"   Current date returned {len(current_data)} dataframes")
            
            for i, df in enumerate(current_data):
                if 'TEAM_ID' in df.columns:
                    print(f"     DataFrame {i} has team data: {len(df)} teams")
                    
        except Exception as e:
            print(f"   ❌ Current date error: {e}")
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_teams_collection()
