#!/usr/bin/env python3
"""
Test the final comprehensive filter for ALL seasons 2015-2025
"""

import sqlite3
from wnba_data_collector import WN<PERSON><PERSON><PERSON>prehensiveCollector

def test_final_filter():
    """Test the final comprehensive filter"""
    print("TESTING COMPREHENSIVE COLLECTION FOR ALL SEASONS 2015-2025")
    print("=" * 60)
    
    collector = WNBAComprehensiveCollector()
    
    try:
        # Test the exact query from the updated method
        cursor = collector.conn.execute("""
            SELECT DISTINCT p.player_id, p.player_name, p.season
            FROM players p
            LEFT JOIN player_game_logs pgl ON p.player_id = pgl.player_id AND p.season = pgl.season
            WHERE pgl.player_id IS NULL
            AND p.season BETWEEN '2015' AND '2025'
            ORDER BY p.season DESC, p.player_name
        """)
        players_to_collect = cursor.fetchall()
        
        # Break down by season
        cursor = collector.conn.execute("""
            SELECT season, COUNT(*) as player_count
            FROM players
            WHERE season BETWEEN '2015' AND '2025'
            GROUP BY season
            ORDER BY season
        """)
        
        print(f"PLAYERS TO COLLECT BY SEASON:")
        total_players = 0
        for season, count in cursor.fetchall():
            total_players += count
            print(f"  {season}: {count:>4} players")
        
        print(f"\nCOLLECTION SUMMARY:")
        print(f"  Total players to collect: {len(players_to_collect):,}")
        print(f"  All seasons covered: 2015-2025")
        print(f"  Estimated time: ~{len(players_to_collect)*6/3600:.1f} hours")
        print(f"  Strategy: Comprehensive collection, API will filter inactive players")
        
        # Sample players from different seasons
        print(f"\nSAMPLE PLAYERS BY SEASON:")
        for season in ['2015', '2018', '2021', '2024', '2025']:
            season_players = [p for p in players_to_collect if p[2] == season]
            if season_players:
                print(f"  {season}: {season_players[0][1]} (and {len(season_players)-1} others)")
        
        return len(players_to_collect)
        
    finally:
        collector.close()

def main():
    """Main execution"""
    player_count = test_final_filter()
    
    print(f"\n✅ COMPREHENSIVE COLLECTION READY")
    print(f"   Will attempt to collect data for {player_count:,} players")
    print(f"   Covers ALL seasons 2015-2025 as requested")
    print(f"   API will naturally filter out players with no game data")
    print(f"   Ready to start full collection!")

if __name__ == "__main__":
    main()
