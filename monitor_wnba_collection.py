#!/usr/bin/env python3
"""
Monitor WNBA Data Collection Progress
Real-time monitoring of the comprehensive WNBA data collection
"""

import sqlite3
import time
import os
from datetime import datetime
import pandas as pd

def monitor_collection_progress():
    """Monitor the progress of WNBA data collection"""
    print("📊 WNBA DATA COLLECTION MONITOR")
    print("=" * 80)
    
    db_path = 'wnba_comprehensive.db'
    
    # Key tables to monitor
    key_tables = [
        'teams', 'players', 'player_game_logs', 'team_game_logs', 
        'league_game_log', 'team_rosters', 'team_coaches',
        'player_last_n_games', 'league_dash_player_pt_shot',
        'league_dash_player_clutch', 'defense_hub_stats',
        'league_dash_lineups', 'team_vs_player_matchups',
        'synergy_play_types', 'playoff_picture'
    ]
    
    seasons = ['2015', '2016', '2017', '2018', '2019', '2020', '2021', '2022', '2023', '2024', '2025']
    
    while True:
        try:
            if not os.path.exists(db_path):
                print(f"⏳ Waiting for database to be created...")
                time.sleep(10)
                continue
            
            conn = sqlite3.connect(db_path)
            
            # Clear screen and show header
            os.system('cls' if os.name == 'nt' else 'clear')
            print("🏀 WNBA COMPREHENSIVE DATA COLLECTION MONITOR")
            print("=" * 100)
            print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            # Database size
            db_size = os.path.getsize(db_path) / (1024 * 1024)
            print(f"💾 Database Size: {db_size:.1f} MB")
            
            # Overall progress
            total_records = 0
            working_tables = 0
            
            print(f"\n📊 TABLE PROGRESS:")
            print("-" * 80)
            
            for table in key_tables:
                try:
                    cursor = conn.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    total_records += count
                    if count > 0:
                        working_tables += 1
                    
                    # Progress indicator
                    if count == 0:
                        status = "⏳"
                    elif count < 1000:
                        status = "🟡"
                    elif count < 10000:
                        status = "🟠"
                    else:
                        status = "🟢"
                    
                    print(f"   {status} {table.replace('_', ' ').title():<30}: {count:>8,} records")
                    
                except sqlite3.OperationalError:
                    print(f"   ⚪ {table.replace('_', ' ').title():<30}: Not created yet")
            
            print(f"\n📈 OVERALL PROGRESS:")
            print(f"   • Total Records: {total_records:,}")
            print(f"   • Active Tables: {working_tables}/{len(key_tables)} ({working_tables/len(key_tables)*100:.0f}%)")
            
            # Season coverage
            try:
                cursor = conn.execute("""
                    SELECT season, 
                           COUNT(DISTINCT CASE WHEN table_name = 'teams' THEN team_id END) as teams,
                           COUNT(DISTINCT CASE WHEN table_name = 'players' THEN player_id END) as players,
                           COUNT(DISTINCT CASE WHEN table_name = 'player_game_logs' THEN game_id END) as games
                    FROM (
                        SELECT season, team_id, NULL as player_id, NULL as game_id, 'teams' as table_name FROM teams
                        UNION ALL
                        SELECT season, NULL as team_id, player_id, NULL as game_id, 'players' as table_name FROM players
                        UNION ALL
                        SELECT season, NULL as team_id, NULL as player_id, game_id, 'player_game_logs' as table_name FROM player_game_logs
                    )
                    GROUP BY season
                    ORDER BY season
                """)
                
                season_data = cursor.fetchall()
                
                if season_data:
                    print(f"\n📅 SEASON COVERAGE:")
                    print("-" * 80)
                    
                    for season, teams, players, games in season_data:
                        # Progress indicators for each season
                        team_status = "🟢" if teams >= 12 else "🟡" if teams > 0 else "⚪"
                        player_status = "🟢" if players >= 100 else "🟡" if players > 0 else "⚪"
                        game_status = "🟢" if games >= 100 else "🟡" if games > 0 else "⚪"
                        
                        print(f"   {season}: {team_status} {teams:>2} teams | {player_status} {players:>3} players | {game_status} {games:>4} games")
                
            except sqlite3.OperationalError:
                print(f"\n📅 SEASON COVERAGE: Data not available yet")
            
            # Recent activity
            try:
                cursor = conn.execute("""
                    SELECT 'teams' as table_name, COUNT(*) as recent_count
                    FROM teams 
                    WHERE collected_at > datetime('now', '-1 hour')
                    UNION ALL
                    SELECT 'players' as table_name, COUNT(*) as recent_count
                    FROM players 
                    WHERE collected_at > datetime('now', '-1 hour')
                    UNION ALL
                    SELECT 'player_game_logs' as table_name, COUNT(*) as recent_count
                    FROM player_game_logs 
                    WHERE collected_at > datetime('now', '-1 hour')
                    ORDER BY recent_count DESC
                """)
                
                recent_activity = cursor.fetchall()
                
                if any(count > 0 for _, count in recent_activity):
                    print(f"\n⚡ RECENT ACTIVITY (Last Hour):")
                    print("-" * 80)
                    for table_name, count in recent_activity:
                        if count > 0:
                            print(f"   • {table_name.replace('_', ' ').title()}: +{count:,} records")
                
            except sqlite3.OperationalError:
                pass
            
            # Performance metrics
            try:
                # Get some performance indicators
                cursor = conn.execute("SELECT COUNT(*) FROM player_game_logs")
                player_logs = cursor.fetchone()[0]
                
                cursor = conn.execute("SELECT COUNT(DISTINCT player_id) FROM player_game_logs")
                unique_players = cursor.fetchone()[0]
                
                cursor = conn.execute("SELECT COUNT(DISTINCT game_id) FROM player_game_logs")
                unique_games = cursor.fetchone()[0]
                
                if player_logs > 0:
                    print(f"\n🎯 COLLECTION METRICS:")
                    print("-" * 80)
                    print(f"   • Unique Players Tracked: {unique_players:,}")
                    print(f"   • Unique Games Processed: {unique_games:,}")
                    print(f"   • Average Logs per Player: {player_logs/unique_players:.1f}" if unique_players > 0 else "")
                    print(f"   • Average Players per Game: {player_logs/unique_games:.1f}" if unique_games > 0 else "")
                
            except (sqlite3.OperationalError, ZeroDivisionError):
                pass
            
            # Estimated completion
            if total_records > 0:
                # Very rough estimation based on current progress
                estimated_total = 500000  # Rough estimate for complete WNBA dataset
                progress_pct = min(100, (total_records / estimated_total) * 100)
                
                print(f"\n🎯 ESTIMATED PROGRESS:")
                print("-" * 80)
                print(f"   • Collection Progress: {progress_pct:.1f}%")
                
                # Progress bar
                bar_length = 50
                filled_length = int(bar_length * progress_pct / 100)
                bar = "█" * filled_length + "░" * (bar_length - filled_length)
                print(f"   • Progress Bar: [{bar}] {progress_pct:.1f}%")
            
            print(f"\n🔄 Refreshing in 30 seconds... (Ctrl+C to exit)")
            print("=" * 100)
            
            conn.close()
            time.sleep(30)
            
        except KeyboardInterrupt:
            print(f"\n\n👋 Monitoring stopped by user")
            break
        except Exception as e:
            print(f"\n❌ Monitoring error: {e}")
            time.sleep(10)

def show_final_summary():
    """Show final collection summary"""
    db_path = 'wnba_comprehensive.db'
    
    if not os.path.exists(db_path):
        print("❌ Database not found")
        return
    
    conn = sqlite3.connect(db_path)
    
    print("🏀 FINAL WNBA COLLECTION SUMMARY")
    print("=" * 80)
    
    # All tables
    all_tables = [
        'teams', 'players', 'player_game_logs', 'team_game_logs', 'league_game_log',
        'team_rosters', 'team_coaches', 'player_last_n_games', 'league_dash_player_pt_shot',
        'league_dash_opp_pt_shot', 'league_dash_player_clutch', 'defense_hub_stats',
        'league_dash_lineups', 'league_lineup_viz', 'team_vs_player_matchups',
        'synergy_play_types', 'team_game_streaks', 'playoff_picture',
        'boxscore_defensive_v2', 'boxscore_four_factors_v2', 'boxscore_usage_v2',
        'boxscore_matchups_v3', 'game_rotations'
    ]
    
    total_records = 0
    
    for table in all_tables:
        try:
            count = conn.execute(f"SELECT COUNT(*) FROM {table}").fetchone()[0]
            total_records += count
            print(f"   • {table.replace('_', ' ').title()}: {count:,} records")
        except:
            print(f"   • {table.replace('_', ' ').title()}: 0 records")
    
    print(f"\n📊 TOTAL RECORDS: {total_records:,}")
    print(f"💾 DATABASE SIZE: {os.path.getsize(db_path) / (1024*1024):.1f} MB")
    
    conn.close()

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "summary":
        show_final_summary()
    else:
        monitor_collection_progress()
