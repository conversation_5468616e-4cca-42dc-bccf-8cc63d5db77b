from nba_api.stats.endpoints import *
import pandas as pd
import sqlite3
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import json
import os
from concurrent.futures import ThreadPoolExecutor, as_completed
import traceback

class WNBAComprehensiveCollector:
    def __init__(self):
        self.setup_logging()
        self.setup_database()
        self.wnba_league_id = '10'
        self.seasons = self.generate_seasons(2015, 2025)
        self.collected_data = {}
        self.base_dir = os.getcwd()
        self.data_dir = os.path.join(self.base_dir, 'data')
        
    def setup_logging(self):
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('wnba_comprehensive.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def setup_database(self):
        self.conn = sqlite3.connect('wnba_comprehensive.db', check_same_thread=False)
        self.create_comprehensive_tables()
        
    def create_comprehensive_tables(self):
        tables = {
            'teams': '''CREATE TABLE IF NOT EXISTS teams (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                team_id TEXT,
                season TEXT,
                team_name TEXT,
                abbreviation TEXT,
                city TEXT,
                conference TEXT,
                division TEXT,
                w INTEGER,
                l INTEGER,
                pct REAL,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(team_id, season)
            )''',
            
            'players': '''CREATE TABLE IF NOT EXISTS players (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                player_id TEXT,
                season TEXT,
                team_id TEXT,
                player_name TEXT,
                position TEXT,
                height TEXT,
                weight TEXT,
                birth_date TEXT,
                experience TEXT,
                college TEXT,
                country TEXT,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(player_id, season)
            )''',
            
            'games': '''CREATE TABLE IF NOT EXISTS games (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                game_id TEXT UNIQUE,
                game_date TEXT,
                season TEXT,
                home_team_id TEXT,
                away_team_id TEXT,
                home_score INTEGER,
                away_score INTEGER,
                game_status TEXT,
                season_type TEXT,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )''',
            
            'boxscore_traditional': '''CREATE TABLE IF NOT EXISTS boxscore_traditional (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                game_id TEXT,
                team_id TEXT,
                player_id TEXT,
                player_name TEXT,
                start_position TEXT,
                comment TEXT,
                min TEXT,
                fgm INTEGER,
                fga INTEGER,
                fg_pct REAL,
                fg3m INTEGER,
                fg3a INTEGER,
                fg3_pct REAL,
                ftm INTEGER,
                fta INTEGER,
                ft_pct REAL,
                oreb INTEGER,
                dreb INTEGER,
                reb INTEGER,
                ast INTEGER,
                stl INTEGER,
                blk INTEGER,
                tov INTEGER,
                pf INTEGER,
                pts INTEGER,
                plus_minus INTEGER,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(game_id, player_id)
            )''',
            
            'boxscore_advanced': '''CREATE TABLE IF NOT EXISTS boxscore_advanced (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                game_id TEXT,
                team_id TEXT,
                player_id TEXT,
                player_name TEXT,
                min TEXT,
                e_off_rating REAL,
                off_rating REAL,
                e_def_rating REAL,
                def_rating REAL,
                e_net_rating REAL,
                net_rating REAL,
                ast_pct REAL,
                ast_tov REAL,
                ast_ratio REAL,
                oreb_pct REAL,
                dreb_pct REAL,
                reb_pct REAL,
                tm_tov_pct REAL,
                efg_pct REAL,
                ts_pct REAL,
                usg_pct REAL,
                e_usg_pct REAL,
                e_pace REAL,
                pace REAL,
                pace_per40 REAL,
                poss INTEGER,
                pie REAL,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(game_id, player_id)
            )''',
            
            'player_career_stats': '''CREATE TABLE IF NOT EXISTS player_career_stats (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                player_id TEXT,
                season_id TEXT,
                league_id TEXT,
                team_id TEXT,
                team_abbreviation TEXT,
                player_age INTEGER,
                gp INTEGER,
                gs INTEGER,
                min REAL,
                fgm REAL,
                fga REAL,
                fg_pct REAL,
                fg3m REAL,
                fg3a REAL,
                fg3_pct REAL,
                ftm REAL,
                fta REAL,
                ft_pct REAL,
                oreb REAL,
                dreb REAL,
                reb REAL,
                ast REAL,
                stl REAL,
                blk REAL,
                tov REAL,
                pf REAL,
                pts REAL,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(player_id, season_id)
            )''',
            
            'team_stats': '''CREATE TABLE IF NOT EXISTS team_stats (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                team_id TEXT,
                season TEXT,
                season_type TEXT,
                gp INTEGER,
                w INTEGER,
                l INTEGER,
                w_pct REAL,
                min REAL,
                fgm REAL,
                fga REAL,
                fg_pct REAL,
                fg3m REAL,
                fg3a REAL,
                fg3_pct REAL,
                ftm REAL,
                fta REAL,
                ft_pct REAL,
                oreb REAL,
                dreb REAL,
                reb REAL,
                ast REAL,
                tov REAL,
                stl REAL,
                blk REAL,
                blka REAL,
                pf REAL,
                pfd REAL,
                pts REAL,
                plus_minus REAL,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(team_id, season, season_type)
            )''',
            
            'shot_chart_data': '''CREATE TABLE IF NOT EXISTS shot_chart_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                game_id TEXT,
                game_event_id TEXT,
                player_id TEXT,
                player_name TEXT,
                team_id TEXT,
                team_name TEXT,
                period INTEGER,
                minutes_remaining INTEGER,
                seconds_remaining INTEGER,
                event_type TEXT,
                action_type TEXT,
                shot_type TEXT,
                shot_zone_basic TEXT,
                shot_zone_area TEXT,
                shot_zone_range TEXT,
                shot_distance INTEGER,
                loc_x INTEGER,
                loc_y INTEGER,
                shot_attempted_flag INTEGER,
                shot_made_flag INTEGER,
                game_date TEXT,
                htm TEXT,
                vtm TEXT,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(game_id, game_event_id)
            )''',
            
            'hustle_stats': '''CREATE TABLE IF NOT EXISTS hustle_stats (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                player_id TEXT,
                team_id TEXT,
                season TEXT,
                player_name TEXT,
                team_abbreviation TEXT,
                age INTEGER,
                gp INTEGER,
                min REAL,
                screen_assists REAL,
                screen_ast_pts REAL,
                off_loose_balls_recovered REAL,
                def_loose_balls_recovered REAL,
                loose_balls_recovered REAL,
                charges_drawn REAL,
                contested_shots REAL,
                contested_shots_2pt REAL,
                contested_shots_3pt REAL,
                deflections REAL,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(player_id, season)
            )''',
            
            'tracking_stats': '''CREATE TABLE IF NOT EXISTS tracking_stats (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                player_id TEXT,
                team_id TEXT,
                season TEXT,
                player_name TEXT,
                gp INTEGER,
                min REAL,
                dist_feet REAL,
                dist_miles REAL,
                dist_miles_off REAL,
                dist_miles_def REAL,
                avg_speed REAL,
                avg_speed_off REAL,
                avg_speed_def REAL,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(player_id, season)
            )'''
        }
        
        for table_name, query in tables.items():
            self.conn.execute(query)
        self.conn.commit()

    def generate_seasons(self, start_year: int, end_year: int) -> List[str]:
        """Generate season strings from start to end year for WNBA"""
        seasons = []
        current_year = datetime.now().year
        # For WNBA, seasons are single years (e.g., 2024, not 2023-24)
        for year in range(start_year, min(end_year + 1, current_year + 1)):
            seasons.append(str(year))
        return seasons
        
    def safe_api_call(self, endpoint_func, **kwargs):
        """Safely call API endpoint with error handling and retries"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                time.sleep(0.6)  # Rate limiting
                result = endpoint_func(**kwargs)
                return result
            except Exception as e:
                self.logger.warning(f"API call failed (attempt {attempt + 1}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)
                else:
                    self.logger.error(f"API call failed after {max_retries} attempts: {e}")
                    return None

    def check_existing_data(self, table_name: str, **conditions) -> bool:
        """Check if data already exists in database"""
        try:
            where_clause = " AND ".join([f"{key} = ?" for key in conditions.keys()])
            query = f"SELECT COUNT(*) FROM {table_name} WHERE {where_clause}"
            cursor = self.conn.execute(query, list(conditions.values()))
            count = cursor.fetchone()[0]
            return count > 0
        except Exception:
            return False
            
    def get_missing_seasons(self, table_name: str) -> List[str]:
        """Get seasons that haven't been collected yet"""
        try:
            cursor = self.conn.execute(f"SELECT DISTINCT season FROM {table_name}")
            existing_seasons = set(row[0] for row in cursor.fetchall())
            missing_seasons = [s for s in self.seasons if s not in existing_seasons]
            return missing_seasons
        except Exception:
            return self.seasons
            
    def get_missing_games(self) -> List[str]:
        """Get game IDs that don't have boxscore data yet"""
        try:
            cursor = self.conn.execute("""
                SELECT DISTINCT g.game_id 
                FROM games g 
                LEFT JOIN boxscore_traditional bt ON g.game_id = bt.game_id 
                WHERE bt.game_id IS NULL AND g.game_id IS NOT NULL
            """)
            return [row[0] for row in cursor.fetchall()]
        except Exception:
            cursor = self.conn.execute("SELECT DISTINCT game_id FROM games WHERE game_id IS NOT NULL")
            return [row[0] for row in cursor.fetchall()]

    def collect_teams_data(self):
        """Collect all WNBA teams data for all seasons"""
        self.logger.info("Collecting teams data...")
        
        missing_seasons = self.get_missing_seasons('teams')
        if not missing_seasons:
            self.logger.info("All teams data already collected")
            return
            
        self.logger.info(f"Collecting teams data for {len(missing_seasons)} missing seasons: {missing_seasons}")
        
        for season in missing_seasons:
            try:
                # Check if this specific season already exists
                if self.check_existing_data('teams', season=season):
                    self.logger.info(f"Teams data for season {season} already exists, skipping")
                    continue
                    
                standings = self.safe_api_call(
                    leaguestandings.LeagueStandings,
                    league_id=self.wnba_league_id,
                    season=season,
                    season_type='Regular Season'
                )
                
                if standings:
                    df = standings.get_data_frames()[0]
                    new_records = 0
                    for _, row in df.iterrows():
                        # Check for duplicate before inserting
                        if not self.check_existing_data('teams', team_id=row.get('TeamID'), season=season):
                            self.conn.execute('''
                                INSERT INTO teams 
                                (team_id, season, team_name, abbreviation, city, conference, division, w, l, pct)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                            ''', (
                                row.get('TeamID'), season, row.get('TeamName'), row.get('TeamSlug'),
                                row.get('TeamCity'), row.get('Conference'), row.get('Division'),
                                row.get('WINS'), row.get('LOSSES'), row.get('WinPCT')
                            ))
                            new_records += 1
                    
                    self.conn.commit()
                    self.logger.info(f"Added {new_records} new team records for season {season}")
                    
            except Exception as e:
                self.logger.error(f"Error collecting teams for season {season}: {e}")
                
    def collect_players_data(self):
        """Collect all WNBA players data"""
        self.logger.info("Collecting players data...")
        
        missing_seasons = self.get_missing_seasons('players')
        if not missing_seasons:
            self.logger.info("All players data already collected")
            return
            
        self.logger.info(f"Collecting players data for {len(missing_seasons)} missing seasons: {missing_seasons}")
        
        for season in missing_seasons:
            try:
                # Check if this specific season already exists
                existing_count = self.conn.execute("SELECT COUNT(*) FROM players WHERE season = ?", (season,)).fetchone()[0]
                if existing_count > 0:
                    self.logger.info(f"Players data for season {season} already exists ({existing_count} records), skipping")
                    continue
                    
                players = self.safe_api_call(
                    commonallplayers.CommonAllPlayers,
                    league_id=self.wnba_league_id,
                    season=season,
                    is_only_current_season=0
                )
                
                if players:
                    df = players.get_data_frames()[0]
                    new_records = 0
                    for _, row in df.iterrows():
                        # Check for duplicate before inserting
                        if not self.check_existing_data('players', player_id=row.get('PERSON_ID'), season=season):
                            self.conn.execute('''
                                INSERT INTO players 
                                (player_id, season, team_id, player_name, position, height, weight, birth_date, experience, college, country)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                            ''', (
                                row.get('PERSON_ID'), season, row.get('TEAM_ID'), row.get('DISPLAY_FIRST_LAST'),
                                row.get('POSITION'), row.get('HEIGHT'), row.get('WEIGHT'), row.get('BIRTHDATE'),
                                row.get('EXPERIENCE'), row.get('SCHOOL'), row.get('COUNTRY')
                            ))
                            new_records += 1
                    
                    self.conn.commit()
                    self.logger.info(f"Added {new_records} new player records for season {season}")
                    
            except Exception as e:
                self.logger.error(f"Error collecting players for season {season}: {e}")
                
    def collect_schedule_and_games(self):
        """Collect schedule and game data"""
        self.logger.info("Collecting schedule and games data...")
        
        missing_seasons = self.get_missing_seasons('games')
        if not missing_seasons:
            self.logger.info("All games data already collected")
            return
            
        self.logger.info(f"Collecting games data for {len(missing_seasons)} missing seasons: {missing_seasons}")
        
        for season in missing_seasons:
            try:
                existing_count = self.conn.execute("SELECT COUNT(*) FROM games WHERE season = ?", (season,)).fetchone()[0]
                if existing_count > 0:
                    self.logger.info(f"Games data for season {season} already exists ({existing_count} records), skipping")
                    continue
                    
                schedule = self.safe_api_call(
                    scheduleleaguev2.ScheduleLeagueV2,
                    league_id=self.wnba_league_id,
                    season=season
                )
                
                if schedule:
                    df = schedule.get_data_frames()[0]
                    new_records = 0
                    for _, row in df.iterrows():
                        game_id = row.get('GAME_ID')
                        if game_id and not self.check_existing_data('games', game_id=game_id):
                            self.conn.execute('''
                                INSERT INTO games 
                                (game_id, game_date, season, home_team_id, away_team_id, home_score, away_score, game_status, season_type)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                            ''', (
                                game_id, row.get('GAME_DATE'), season, row.get('HOME_TEAM_ID'),
                                row.get('VISITOR_TEAM_ID'), row.get('HOME_TEAM_SCORE'), row.get('VISITOR_TEAM_SCORE'),
                                row.get('GAME_STATUS_TEXT'), row.get('SEASON_TYPE')
                            ))
                            new_records += 1
                    
                    self.conn.commit()
                    self.logger.info(f"Added {new_records} new game records for season {season}")
                    
            except Exception as e:
                self.logger.error(f"Error collecting schedule for season {season}: {e}")

    def collect_boxscore_data(self):
        """Collect detailed boxscore data for all games"""
        self.logger.info("Collecting boxscore data...")
        
        missing_games = self.get_missing_games()
        if not missing_games:
            self.logger.info("All boxscore data already collected")
            return
            
        self.logger.info(f"Collecting boxscore data for {len(missing_games)} missing games")
        
        for i, game_id in enumerate(missing_games):
            if i % 100 == 0:
                self.logger.info(f"Processing game {i+1}/{len(missing_games)}: {game_id}")
                
            try:
                # Check if boxscore already exists
                if self.check_existing_data('boxscore_traditional', game_id=game_id):
                    continue
                    
                # Traditional boxscore
                traditional = self.safe_api_call(
                    boxscoretraditionalv2.BoxScoreTraditionalV2,
                    game_id=game_id
                )
                
                if traditional:
                    player_stats = traditional.get_data_frames()[0]
                    for _, row in player_stats.iterrows():
                        if not self.check_existing_data('boxscore_traditional', game_id=game_id, player_id=row.get('PLAYER_ID')):
                            self.conn.execute('''
                                INSERT INTO boxscore_traditional 
                                (game_id, team_id, player_id, player_name, start_position, comment, min, 
                                 fgm, fga, fg_pct, fg3m, fg3a, fg3_pct, ftm, fta, ft_pct,
                                 oreb, dreb, reb, ast, stl, blk, tov, pf, pts, plus_minus)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                            ''', (
                                game_id, row.get('TEAM_ID'), row.get('PLAYER_ID'), row.get('PLAYER_NAME'),
                                row.get('START_POSITION'), row.get('COMMENT'), row.get('MIN'),
                                row.get('FGM'), row.get('FGA'), row.get('FG_PCT'),
                                row.get('FG3M'), row.get('FG3A'), row.get('FG3_PCT'),
                                row.get('FTM'), row.get('FTA'), row.get('FT_PCT'),
                                row.get('OREB'), row.get('DREB'), row.get('REB'),
                                row.get('AST'), row.get('STL'), row.get('BLK'),
                                row.get('TOV'), row.get('PF'), row.get('PTS'), row.get('PLUS_MINUS')
                            ))
                
                # Advanced boxscore
                advanced = self.safe_api_call(
                    boxscoreadvancedv2.BoxScoreAdvancedV2,
                    game_id=game_id
                )
                
                if advanced:
                    player_advanced = advanced.get_data_frames()[0]
                    for _, row in player_advanced.iterrows():
                        if not self.check_existing_data('boxscore_advanced', game_id=game_id, player_id=row.get('PLAYER_ID')):
                            self.conn.execute('''
                                INSERT INTO boxscore_advanced 
                                (game_id, team_id, player_id, player_name, min, e_off_rating, off_rating,
                                 e_def_rating, def_rating, e_net_rating, net_rating, ast_pct, ast_tov,
                                 ast_ratio, oreb_pct, dreb_pct, reb_pct, tm_tov_pct, efg_pct, ts_pct,
                                 usg_pct, e_usg_pct, e_pace, pace, pace_per40, poss, pie)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                            ''', (
                                game_id, row.get('TEAM_ID'), row.get('PLAYER_ID'), row.get('PLAYER_NAME'),
                                row.get('MIN'), row.get('E_OFF_RATING'), row.get('OFF_RATING'),
                                row.get('E_DEF_RATING'), row.get('DEF_RATING'), row.get('E_NET_RATING'),
                                row.get('NET_RATING'), row.get('AST_PCT'), row.get('AST_TOV'),
                                row.get('AST_RATIO'), row.get('OREB_PCT'), row.get('DREB_PCT'),
                                row.get('REB_PCT'), row.get('TM_TOV_PCT'), row.get('EFG_PCT'),
                                row.get('TS_PCT'), row.get('USG_PCT'), row.get('E_USG_PCT'),
                                row.get('E_PACE'), row.get('PACE'), row.get('PACE_PER40'),
                                row.get('POSS'), row.get('PIE')
                            ))
                
                if i % 50 == 0:
                    self.conn.commit()
                    
            except Exception as e:
                self.logger.error(f"Error collecting boxscore for game {game_id}: {e}")
                
        self.conn.commit()

    def collect_hustle_stats(self):
        """Collect hustle statistics for all players"""
        self.logger.info("Collecting hustle stats...")
        
        missing_seasons = self.get_missing_seasons('hustle_stats')
        if not missing_seasons:
            self.logger.info("All hustle stats already collected")
            return
            
        self.logger.info(f"Collecting hustle stats for {len(missing_seasons)} missing seasons: {missing_seasons}")
        
        for season in missing_seasons:
            try:
                existing_count = self.conn.execute("SELECT COUNT(*) FROM hustle_stats WHERE season = ?", (season,)).fetchone()[0]
                if existing_count > 0:
                    self.logger.info(f"Hustle stats for season {season} already exists ({existing_count} records), skipping")
                    continue
                    
                hustle_stats = self.safe_api_call(
                    leaguehustlestatsplayer.LeagueHustleStatsPlayer,
                    league_id=self.wnba_league_id,
                    season=season,
                    season_type_all_star='Regular Season'
                )
                
                if hustle_stats:
                    df = hustle_stats.get_data_frames()[0]
                    new_records = 0
                    for _, row in df.iterrows():
                        if not self.check_existing_data('hustle_stats', player_id=row.get('PLAYER_ID'), season=season):
                            self.conn.execute('''
                                INSERT INTO hustle_stats 
                                (player_id, team_id, season, player_name, team_abbreviation, age, gp, min,
                                 screen_assists, screen_ast_pts, off_loose_balls_recovered, def_loose_balls_recovered,
                                 loose_balls_recovered, charges_drawn, contested_shots, contested_shots_2pt,
                                 contested_shots_3pt, deflections)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                            ''', (
                                row.get('PLAYER_ID'), row.get('TEAM_ID'), season,
                                row.get('PLAYER_NAME'), row.get('TEAM_ABBREVIATION'), row.get('AGE'),
                                row.get('GP'), row.get('MIN'), row.get('SCREEN_ASSISTS'),
                                row.get('SCREEN_AST_PTS'), row.get('OFF_LOOSE_BALLS_RECOVERED'),
                                row.get('DEF_LOOSE_BALLS_RECOVERED'), row.get('LOOSE_BALLS_RECOVERED'),
                                row.get('CHARGES_DRAWN'), row.get('CONTESTED_SHOTS'),
                                row.get('CONTESTED_SHOTS_2PT'), row.get('CONTESTED_SHOTS_3PT'),
                                row.get('DEFLECTIONS')
                            ))
                            new_records += 1
                    
                    self.conn.commit()
                    self.logger.info(f"Added {new_records} new hustle stat records for season {season}")
                    
            except Exception as e:
                self.logger.error(f"Error collecting hustle stats for season {season}: {e}")

    def get_collection_progress(self):
        """Get detailed progress report"""
        progress = {}
        
        # Check seasons coverage
        for table in ['teams', 'players', 'games', 'hustle_stats']:
            try:
                cursor = self.conn.execute(f"SELECT DISTINCT season FROM {table}")
                existing_seasons = set(row[0] for row in cursor.fetchall())
                missing_seasons = [s for s in self.seasons if s not in existing_seasons]
                progress[table] = {
                    'total_seasons': len(self.seasons),
                    'completed_seasons': len(existing_seasons),
                    'missing_seasons': missing_seasons,
                    'completion_pct': (len(existing_seasons) / len(self.seasons)) * 100
                }
            except Exception:
                progress[table] = {
                    'total_seasons': len(self.seasons),
                    'completed_seasons': 0,
                    'missing_seasons': self.seasons,
                    'completion_pct': 0
                }
        
        # Check boxscore coverage
        try:
            total_games = self.conn.execute("SELECT COUNT(DISTINCT game_id) FROM games WHERE game_id IS NOT NULL").fetchone()[0]
            completed_games = self.conn.execute("SELECT COUNT(DISTINCT game_id) FROM boxscore_traditional").fetchone()[0]
            progress['boxscore'] = {
                'total_games': total_games,
                'completed_games': completed_games,
                'completion_pct': (completed_games / total_games * 100) if total_games > 0 else 0
            }
        except Exception:
            progress['boxscore'] = {'total_games': 0, 'completed_games': 0, 'completion_pct': 0}
            
        return progress
        
    def print_progress_report(self):
        """Print detailed progress report"""
        progress = self.get_collection_progress()
        
        print("\n" + "=" * 60)
        print("DATA COLLECTION PROGRESS REPORT")
        print("=" * 60)
        
        for table, data in progress.items():
            if table == 'boxscore':
                print(f"\n{table.upper()}:")
                print(f"  Games with boxscore data: {data['completed_games']:,} / {data['total_games']:,}")
                print(f"  Completion: {data['completion_pct']:.1f}%")
            else:
                print(f"\n{table.upper()}:")
                print(f"  Seasons completed: {data['completed_seasons']} / {data['total_seasons']}")
                print(f"  Completion: {data['completion_pct']:.1f}%")
                if data['missing_seasons']:
                    print(f"  Missing seasons: {', '.join(data['missing_seasons'])}")
        
        print("\n" + "=" * 60)

    def collect_all_data(self):
        """Collect all WNBA data in sequence"""
        self.logger.info("Starting comprehensive WNBA data collection...")

        # Collect basic data first
        self.collect_teams_data()
        self.collect_players_data()
        self.collect_schedule_and_games()

        # Collect detailed game data
        self.collect_boxscore_data()

        # Collect advanced stats
        self.collect_hustle_stats()

        self.logger.info("Data collection completed!")

    def export_to_csv(self):
        """Export all data to CSV files"""
        self.logger.info("Exporting data to CSV files...")

        # Create data directory if it doesn't exist
        os.makedirs(self.data_dir, exist_ok=True)

        tables = ['teams', 'players', 'games', 'boxscore_traditional',
                 'boxscore_advanced', 'player_career_stats', 'team_stats',
                 'shot_chart_data', 'hustle_stats', 'tracking_stats']

        for table in tables:
            try:
                df = pd.read_sql_query(f"SELECT * FROM {table}", self.conn)
                if not df.empty:
                    csv_path = os.path.join(self.data_dir, f"{table}.csv")
                    df.to_csv(csv_path, index=False)
                    self.logger.info(f"Exported {len(df)} records to {csv_path}")
                else:
                    self.logger.warning(f"No data found for table {table}")
            except Exception as e:
                self.logger.error(f"Error exporting {table} to CSV: {e}")

    def export_to_json(self):
        """Export all data to JSON files"""
        self.logger.info("Exporting data to JSON files...")

        # Create data directory if it doesn't exist
        os.makedirs(self.data_dir, exist_ok=True)

        tables = ['teams', 'players', 'games', 'boxscore_traditional',
                 'boxscore_advanced', 'player_career_stats', 'team_stats',
                 'shot_chart_data', 'hustle_stats', 'tracking_stats']

        for table in tables:
            try:
                df = pd.read_sql_query(f"SELECT * FROM {table}", self.conn)
                if not df.empty:
                    json_path = os.path.join(self.data_dir, f"{table}.json")
                    df.to_json(json_path, orient='records', indent=2)
                    self.logger.info(f"Exported {len(df)} records to {json_path}")
                else:
                    self.logger.warning(f"No data found for table {table}")
            except Exception as e:
                self.logger.error(f"Error exporting {table} to JSON: {e}")

    def create_summary_report(self):
        """Create a summary report of collected data"""
        self.logger.info("Creating summary report...")

        report_path = os.path.join(self.base_dir, 'wnba_collection_summary.txt')

        with open(report_path, 'w') as f:
            f.write("WNBA Data Collection Summary Report\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"Collection Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Seasons Covered: {', '.join(self.seasons)}\n\n")

            # Get record counts for each table
            tables = ['teams', 'players', 'games', 'boxscore_traditional',
                     'boxscore_advanced', 'hustle_stats']

            for table in tables:
                try:
                    count = self.conn.execute(f"SELECT COUNT(*) FROM {table}").fetchone()[0]
                    f.write(f"{table.replace('_', ' ').title()}: {count:,} records\n")
                except Exception:
                    f.write(f"{table.replace('_', ' ').title()}: 0 records\n")

            f.write(f"\nData files saved to: {self.data_dir}\n")
            f.write(f"Database file: wnba_comprehensive.db\n")

        self.logger.info(f"Summary report saved to {report_path}")
        return report_path

    def get_collection_summary(self):
        """Get a summary of collected data"""
        summary = {}

        tables = ['teams', 'players', 'games', 'boxscore_traditional',
                 'boxscore_advanced', 'hustle_stats']

        for table in tables:
            try:
                count = self.conn.execute(f"SELECT COUNT(*) FROM {table}").fetchone()[0]
                summary[table] = count
            except Exception:
                summary[table] = 0

        return summary

    def close(self):
        """Close database connection"""
        if hasattr(self, 'conn'):
            self.conn.close()
            self.logger.info("Database connection closed")


if __name__ == "__main__":
    collector = WNBAComprehensiveCollector()
    
    try:
        print(f"Starting WNBA data collection...")
        print(f"Data will be organized in: {collector.base_dir}")
        print(f"Database: {os.path.join(collector.data_dir, 'wnba_comprehensive.db')}")
        
        # Show initial progress
        collector.print_progress_report()
        
        print("\nStarting collection process...")
        print("-" * 60)
        
        collector.collect_all_data()
        collector.export_to_csv()
        collector.export_to_json()
        
        summary_report = collector.create_summary_report()
        summary = collector.get_collection_summary()
        
        print("\n" + "=" * 60)
        print("COLLECTION COMPLETED SUCCESSFULLY!")
        print("=" * 60)
        
        # Show final progress
        collector.print_progress_report()
        
        print(f"\nFiles organized in: {collector.base_dir}")
        print(f"Summary report: {summary_report}")
            
    except KeyboardInterrupt:
        print("\nCollection interrupted by user")
        collector.print_progress_report()
    except Exception as e:
        print(f"Collection failed: {e}")
        traceback.print_exc()
    finally:
        collector.close()

