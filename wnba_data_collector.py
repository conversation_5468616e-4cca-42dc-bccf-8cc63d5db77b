from nba_api.stats.endpoints import *
import pandas as pd
import sqlite3
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import json
import os
from concurrent.futures import ThreadPoolExecutor, as_completed
import traceback

class WNBAComprehensiveCollector:
    def __init__(self):
        self.setup_logging()
        self.setup_database()
        self.wnba_league_id = '10'
        # Generate seasons from 2015 to current year (up to July 17, 2025)
        current_date = datetime.now()
        end_year = 2025 if current_date <= datetime(2025, 7, 17) else current_date.year
        self.seasons = self.generate_seasons(2015, end_year)
        self.collected_data = {}
        self.base_dir = os.getcwd()
        self.data_dir = os.path.join(self.base_dir, 'data')
        
    def setup_logging(self):
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('wnba_comprehensive.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def setup_database(self):
        self.conn = sqlite3.connect('wnba_comprehensive.db', check_same_thread=False)
        self.create_comprehensive_tables()
        
    def create_comprehensive_tables(self):
        tables = {
            'teams': '''CREATE TABLE IF NOT EXISTS teams (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                team_id TEXT,
                season TEXT,
                team_name TEXT,
                abbreviation TEXT,
                city TEXT,
                conference TEXT,
                division TEXT,
                w INTEGER,
                l INTEGER,
                pct REAL,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(team_id, season)
            )''',
            
            'players': '''CREATE TABLE IF NOT EXISTS players (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                player_id TEXT,
                season TEXT,
                team_id TEXT,
                player_name TEXT,
                position TEXT,
                height TEXT,
                weight TEXT,
                birth_date TEXT,
                experience TEXT,
                college TEXT,
                country TEXT,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(player_id, season)
            )''',
            
            'games': '''CREATE TABLE IF NOT EXISTS games (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                game_id TEXT UNIQUE,
                game_date TEXT,
                season TEXT,
                home_team_id TEXT,
                away_team_id TEXT,
                home_score INTEGER,
                away_score INTEGER,
                game_status TEXT,
                season_type TEXT,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )''',
            
            'boxscore_traditional': '''CREATE TABLE IF NOT EXISTS boxscore_traditional (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                game_id TEXT,
                team_id TEXT,
                player_id TEXT,
                player_name TEXT,
                start_position TEXT,
                comment TEXT,
                min TEXT,
                fgm INTEGER,
                fga INTEGER,
                fg_pct REAL,
                fg3m INTEGER,
                fg3a INTEGER,
                fg3_pct REAL,
                ftm INTEGER,
                fta INTEGER,
                ft_pct REAL,
                oreb INTEGER,
                dreb INTEGER,
                reb INTEGER,
                ast INTEGER,
                stl INTEGER,
                blk INTEGER,
                tov INTEGER,
                pf INTEGER,
                pts INTEGER,
                plus_minus INTEGER,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(game_id, player_id)
            )''',
            
            'boxscore_advanced': '''CREATE TABLE IF NOT EXISTS boxscore_advanced (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                game_id TEXT,
                team_id TEXT,
                player_id TEXT,
                player_name TEXT,
                min TEXT,
                e_off_rating REAL,
                off_rating REAL,
                e_def_rating REAL,
                def_rating REAL,
                e_net_rating REAL,
                net_rating REAL,
                ast_pct REAL,
                ast_tov REAL,
                ast_ratio REAL,
                oreb_pct REAL,
                dreb_pct REAL,
                reb_pct REAL,
                tm_tov_pct REAL,
                efg_pct REAL,
                ts_pct REAL,
                usg_pct REAL,
                e_usg_pct REAL,
                e_pace REAL,
                pace REAL,
                pace_per40 REAL,
                poss INTEGER,
                pie REAL,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(game_id, player_id)
            )''',
            
            'player_career_stats': '''CREATE TABLE IF NOT EXISTS player_career_stats (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                player_id TEXT,
                season_id TEXT,
                league_id TEXT,
                team_id TEXT,
                team_abbreviation TEXT,
                player_age INTEGER,
                gp INTEGER,
                gs INTEGER,
                min REAL,
                fgm REAL,
                fga REAL,
                fg_pct REAL,
                fg3m REAL,
                fg3a REAL,
                fg3_pct REAL,
                ftm REAL,
                fta REAL,
                ft_pct REAL,
                oreb REAL,
                dreb REAL,
                reb REAL,
                ast REAL,
                stl REAL,
                blk REAL,
                tov REAL,
                pf REAL,
                pts REAL,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(player_id, season_id)
            )''',

            'player_game_logs': '''CREATE TABLE IF NOT EXISTS player_game_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                player_id TEXT,
                player_name TEXT,
                season TEXT,
                game_id TEXT,
                game_date TEXT,
                matchup TEXT,
                wl TEXT,
                min INTEGER,
                pts INTEGER,
                fgm INTEGER,
                fga INTEGER,
                fg_pct REAL,
                fg3m INTEGER,
                fg3a INTEGER,
                fg3_pct REAL,
                ftm INTEGER,
                fta INTEGER,
                ft_pct REAL,
                oreb INTEGER,
                dreb INTEGER,
                reb INTEGER,
                ast INTEGER,
                stl INTEGER,
                blk INTEGER,
                tov INTEGER,
                pf INTEGER,
                plus_minus INTEGER,
                video_available INTEGER,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(player_id, game_id)
            )''',

            'team_game_logs': '''CREATE TABLE IF NOT EXISTS team_game_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                team_id TEXT,
                team_name TEXT,
                season TEXT,
                game_id TEXT,
                game_date TEXT,
                matchup TEXT,
                wl TEXT,
                w INTEGER,
                l INTEGER,
                w_pct REAL,
                min INTEGER,
                pts INTEGER,
                fgm INTEGER,
                fga INTEGER,
                fg_pct REAL,
                fg3m INTEGER,
                fg3a INTEGER,
                fg3_pct REAL,
                ftm INTEGER,
                fta INTEGER,
                ft_pct REAL,
                oreb INTEGER,
                dreb INTEGER,
                reb INTEGER,
                ast INTEGER,
                stl INTEGER,
                blk INTEGER,
                tov INTEGER,
                pf INTEGER,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(team_id, game_id)
            )''',

            'defense_hub_stats': '''CREATE TABLE IF NOT EXISTS defense_hub_stats (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                season TEXT,
                stat_type TEXT,
                rank_num INTEGER,
                team_id TEXT,
                team_abbreviation TEXT,
                team_name TEXT,
                dreb REAL,
                stl REAL,
                blk REAL,
                tm_def_rating REAL,
                overall_pm REAL,
                threep_dfgpct REAL,
                twop_dfgpct REAL,
                fifeteenf_dfgpct REAL,
                def_rim_pct REAL,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(season, stat_type, team_id)
            )''',

            'game_rotations': '''CREATE TABLE IF NOT EXISTS game_rotations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                game_id TEXT,
                team_id TEXT,
                team_city TEXT,
                team_name TEXT,
                person_id TEXT,
                player_first TEXT,
                player_last TEXT,
                in_time_real TEXT,
                out_time_real TEXT,
                player_pts INTEGER,
                pt_diff INTEGER,
                usg_pct REAL,
                home_away TEXT,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(game_id, person_id, in_time_real)
            )''',

            'lineups': '''CREATE TABLE IF NOT EXISTS lineups (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                season TEXT,
                group_set TEXT,
                group_id TEXT,
                group_name TEXT,
                team_id TEXT,
                team_abbreviation TEXT,
                gp INTEGER,
                w INTEGER,
                l INTEGER,
                w_pct REAL,
                min REAL,
                fgm REAL,
                fga REAL,
                fg_pct REAL,
                fg3m REAL,
                fg3a REAL,
                fg3_pct REAL,
                ftm REAL,
                fta REAL,
                ft_pct REAL,
                oreb REAL,
                dreb REAL,
                reb REAL,
                ast REAL,
                tov REAL,
                stl REAL,
                blk REAL,
                blka REAL,
                pf REAL,
                pfd REAL,
                pts REAL,
                plus_minus REAL,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(season, group_id)
            )''',

            'team_rosters': '''CREATE TABLE IF NOT EXISTS team_rosters (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                team_id TEXT,
                season TEXT,
                league_id TEXT,
                player_id TEXT,
                player_name TEXT,
                player_slug TEXT,
                jersey_num TEXT,
                position TEXT,
                height TEXT,
                weight TEXT,
                birth_date TEXT,
                age INTEGER,
                experience TEXT,
                school TEXT,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(team_id, season, player_id)
            )''',

            'team_coaches': '''CREATE TABLE IF NOT EXISTS team_coaches (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                team_id TEXT,
                season TEXT,
                coach_id TEXT,
                first_name TEXT,
                last_name TEXT,
                coach_name TEXT,
                is_assistant INTEGER,
                coach_type TEXT,
                sort_sequence INTEGER,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(team_id, season, coach_id)
            )''',

            'player_last_n_games': '''CREATE TABLE IF NOT EXISTS player_last_n_games (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                player_id TEXT,
                season TEXT,
                game_period TEXT,
                group_set TEXT,
                group_value TEXT,
                gp INTEGER,
                w INTEGER,
                l INTEGER,
                w_pct REAL,
                min REAL,
                fgm REAL,
                fga REAL,
                fg_pct REAL,
                fg3m REAL,
                fg3a REAL,
                fg3_pct REAL,
                ftm REAL,
                fta REAL,
                ft_pct REAL,
                oreb REAL,
                dreb REAL,
                reb REAL,
                ast REAL,
                tov REAL,
                stl REAL,
                blk REAL,
                blka REAL,
                pf REAL,
                pfd REAL,
                pts REAL,
                plus_minus REAL,
                nba_fantasy_pts REAL,
                dd2 INTEGER,
                td3 INTEGER,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(player_id, season, game_period, group_value)
            )''',

            'team_vs_player_matchups': '''CREATE TABLE IF NOT EXISTS team_vs_player_matchups (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                season TEXT,
                team_id TEXT,
                team_name TEXT,
                vs_player_id TEXT,
                vs_player_name TEXT,
                matchup_type TEXT,
                court_status TEXT,
                group_value TEXT,
                gp INTEGER,
                w INTEGER,
                l INTEGER,
                w_pct REAL,
                min REAL,
                fgm REAL,
                fga REAL,
                fg_pct REAL,
                fg3m REAL,
                fg3a REAL,
                fg3_pct REAL,
                ftm REAL,
                fta REAL,
                ft_pct REAL,
                oreb REAL,
                dreb REAL,
                reb REAL,
                ast REAL,
                tov REAL,
                stl REAL,
                blk REAL,
                blka REAL,
                pf REAL,
                pfd REAL,
                pts REAL,
                plus_minus REAL,
                nba_fantasy_pts REAL,
                dd2 INTEGER,
                td3 INTEGER,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(season, team_id, vs_player_id, matchup_type, court_status, group_value)
            )''',

            'team_game_streaks': '''CREATE TABLE IF NOT EXISTS team_game_streaks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                team_id TEXT,
                team_name TEXT,
                abbreviation TEXT,
                game_streak TEXT,
                start_date TEXT,
                end_date TEXT,
                active_streak INTEGER,
                num_seasons INTEGER,
                last_season TEXT,
                first_season TEXT,
                streak_type TEXT,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(team_id, game_streak, start_date, end_date)
            )''',

            'synergy_play_types': '''CREATE TABLE IF NOT EXISTS synergy_play_types (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                season_id TEXT,
                team_id TEXT,
                team_abbreviation TEXT,
                team_name TEXT,
                play_type TEXT,
                type_grouping TEXT,
                percentile REAL,
                gp INTEGER,
                poss_pct REAL,
                ppp REAL,
                fg_pct REAL,
                ft_poss_pct REAL,
                tov_poss_pct REAL,
                sf_poss_pct REAL,
                plusone_poss_pct REAL,
                score_poss_pct REAL,
                efg_pct REAL,
                poss INTEGER,
                pts INTEGER,
                fgm INTEGER,
                fga INTEGER,
                fgmx INTEGER,
                player_or_team TEXT,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(season_id, team_id, play_type, type_grouping, player_or_team)
            )''',

            'playoff_picture': '''CREATE TABLE IF NOT EXISTS playoff_picture (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                season_id TEXT,
                conference TEXT,
                data_type TEXT,
                team_id TEXT,
                team_name TEXT,
                team_slug TEXT,
                rank_position INTEGER,
                wins INTEGER,
                losses INTEGER,
                win_pct REAL,
                games_back TEXT,
                remaining_games INTEGER,
                remaining_home_games INTEGER,
                remaining_away_games INTEGER,
                clinched_playoffs INTEGER,
                clinched_conference INTEGER,
                clinched_division INTEGER,
                clinched_play_in INTEGER,
                eliminated_playoffs INTEGER,
                high_seed_rank INTEGER,
                low_seed_rank INTEGER,
                series_wins INTEGER,
                series_losses INTEGER,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(season_id, conference, data_type, team_id)
            )''',

            'league_lineup_viz': '''CREATE TABLE IF NOT EXISTS league_lineup_viz (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                season TEXT,
                group_id TEXT,
                group_name TEXT,
                team_id TEXT,
                team_abbreviation TEXT,
                minutes REAL,
                off_rating REAL,
                def_rating REAL,
                net_rating REAL,
                pace REAL,
                ts_pct REAL,
                fta_rate REAL,
                tm_ast_pct REAL,
                pct_fga_2pt REAL,
                pct_fga_3pt REAL,
                pct_pts_2pt_mr REAL,
                pct_pts_fb REAL,
                pct_pts_ft REAL,
                pct_pts_paint REAL,
                pct_ast_fgm REAL,
                pct_uast_fgm REAL,
                opp_fg3_pct REAL,
                opp_efg_pct REAL,
                opp_fta_rate REAL,
                opp_tov_pct REAL,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(season, group_id, team_id)
            )''',

            'league_game_log': '''CREATE TABLE IF NOT EXISTS league_game_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                season_id TEXT,
                team_id TEXT,
                team_abbreviation TEXT,
                team_name TEXT,
                game_id TEXT,
                game_date TEXT,
                matchup TEXT,
                wl TEXT,
                min INTEGER,
                fgm INTEGER,
                fga INTEGER,
                fg_pct REAL,
                fg3m INTEGER,
                fg3a INTEGER,
                fg3_pct REAL,
                ftm INTEGER,
                fta INTEGER,
                ft_pct REAL,
                oreb INTEGER,
                dreb INTEGER,
                reb INTEGER,
                ast INTEGER,
                stl INTEGER,
                blk INTEGER,
                tov INTEGER,
                pf INTEGER,
                pts INTEGER,
                plus_minus INTEGER,
                video_available INTEGER,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(season_id, team_id, game_id)
            )''',

            'league_dash_lineups': '''CREATE TABLE IF NOT EXISTS league_dash_lineups (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                season TEXT,
                group_set TEXT,
                group_id TEXT,
                group_name TEXT,
                team_id TEXT,
                team_abbreviation TEXT,
                gp INTEGER,
                w INTEGER,
                l INTEGER,
                w_pct REAL,
                min REAL,
                fgm REAL,
                fga REAL,
                fg_pct REAL,
                fg3m REAL,
                fg3a REAL,
                fg3_pct REAL,
                ftm REAL,
                fta REAL,
                ft_pct REAL,
                oreb REAL,
                dreb REAL,
                reb REAL,
                ast REAL,
                tov REAL,
                stl REAL,
                blk REAL,
                blka REAL,
                pf REAL,
                pfd REAL,
                pts REAL,
                plus_minus REAL,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(season, group_id, team_id)
            )''',

            'league_dash_opp_pt_shot': '''CREATE TABLE IF NOT EXISTS league_dash_opp_pt_shot (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                season TEXT,
                team_id TEXT,
                team_name TEXT,
                team_abbreviation TEXT,
                gp INTEGER,
                g INTEGER,
                fga_frequency REAL,
                fgm INTEGER,
                fga INTEGER,
                fg_pct REAL,
                efg_pct REAL,
                fg2a_frequency REAL,
                fg2m INTEGER,
                fg2a INTEGER,
                fg2_pct REAL,
                fg3a_frequency REAL,
                fg3m INTEGER,
                fg3a INTEGER,
                fg3_pct REAL,
                shot_category TEXT,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(season, team_id, shot_category)
            )''',

            'league_dash_player_clutch': '''CREATE TABLE IF NOT EXISTS league_dash_player_clutch (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                season TEXT,
                group_set TEXT,
                player_id TEXT,
                player_name TEXT,
                team_id TEXT,
                team_abbreviation TEXT,
                age INTEGER,
                gp INTEGER,
                w INTEGER,
                l INTEGER,
                w_pct REAL,
                min REAL,
                fgm REAL,
                fga REAL,
                fg_pct REAL,
                fg3m REAL,
                fg3a REAL,
                fg3_pct REAL,
                ftm REAL,
                fta REAL,
                ft_pct REAL,
                oreb REAL,
                dreb REAL,
                reb REAL,
                ast REAL,
                tov REAL,
                stl REAL,
                blk REAL,
                blka REAL,
                pf REAL,
                pfd REAL,
                pts REAL,
                plus_minus REAL,
                nba_fantasy_pts REAL,
                dd2 INTEGER,
                td3 INTEGER,
                clutch_scenario TEXT,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(season, player_id, team_id, clutch_scenario)
            )''',

            'league_dash_player_pt_shot': '''CREATE TABLE IF NOT EXISTS league_dash_player_pt_shot (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                season TEXT,
                player_id TEXT,
                player_name TEXT,
                player_last_team_id TEXT,
                player_last_team_abbreviation TEXT,
                age INTEGER,
                gp INTEGER,
                g INTEGER,
                fga_frequency REAL,
                fgm INTEGER,
                fga INTEGER,
                fg_pct REAL,
                efg_pct REAL,
                fg2a_frequency REAL,
                fg2m INTEGER,
                fg2a INTEGER,
                fg2_pct REAL,
                fg3a_frequency REAL,
                fg3m INTEGER,
                fg3a INTEGER,
                fg3_pct REAL,
                shot_category TEXT,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(season, player_id, shot_category)
            )''',

            'league_dash_pt_team_defend': '''CREATE TABLE IF NOT EXISTS league_dash_pt_team_defend (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                season TEXT,
                team_id TEXT,
                team_name TEXT,
                team_abbreviation TEXT,
                gp INTEGER,
                g INTEGER,
                freq REAL,
                d_fgm INTEGER,
                d_fga INTEGER,
                d_fg_pct REAL,
                normal_fg_pct REAL,
                pct_plusminus REAL,
                defense_category TEXT,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(season, team_id, defense_category)
            )''',

            'boxscore_defensive_v2': '''CREATE TABLE IF NOT EXISTS boxscore_defensive_v2 (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                game_id TEXT,
                team_id TEXT,
                team_city TEXT,
                team_name TEXT,
                team_tricode TEXT,
                team_slug TEXT,
                person_id TEXT,
                first_name TEXT,
                family_name TEXT,
                name_i TEXT,
                player_slug TEXT,
                position TEXT,
                comment TEXT,
                jersey_num TEXT,
                matchup_minutes REAL,
                partial_possessions REAL,
                switches_on INTEGER,
                player_points INTEGER,
                defensive_rebounds INTEGER,
                matchup_assists INTEGER,
                matchup_turnovers INTEGER,
                steals INTEGER,
                blocks INTEGER,
                matchup_field_goals_made INTEGER,
                matchup_field_goals_attempted INTEGER,
                matchup_field_goal_percentage REAL,
                matchup_three_pointers_made INTEGER,
                matchup_three_pointers_attempted INTEGER,
                matchup_three_pointer_percentage REAL,
                data_type TEXT,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(game_id, team_id, person_id, data_type)
            )''',

            'boxscore_four_factors_v2': '''CREATE TABLE IF NOT EXISTS boxscore_four_factors_v2 (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                game_id TEXT,
                team_id TEXT,
                team_name TEXT,
                team_abbreviation TEXT,
                team_city TEXT,
                player_id TEXT,
                player_name TEXT,
                start_position TEXT,
                comment TEXT,
                min REAL,
                efg_pct REAL,
                fta_rate REAL,
                tm_tov_pct REAL,
                oreb_pct REAL,
                opp_efg_pct REAL,
                opp_fta_rate REAL,
                opp_tov_pct REAL,
                opp_oreb_pct REAL,
                data_type TEXT,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(game_id, team_id, player_id, data_type)
            )''',
            
            'team_stats': '''CREATE TABLE IF NOT EXISTS team_stats (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                team_id TEXT,
                season TEXT,
                season_type TEXT,
                gp INTEGER,
                w INTEGER,
                l INTEGER,
                w_pct REAL,
                min REAL,
                fgm REAL,
                fga REAL,
                fg_pct REAL,
                fg3m REAL,
                fg3a REAL,
                fg3_pct REAL,
                ftm REAL,
                fta REAL,
                ft_pct REAL,
                oreb REAL,
                dreb REAL,
                reb REAL,
                ast REAL,
                tov REAL,
                stl REAL,
                blk REAL,
                blka REAL,
                pf REAL,
                pfd REAL,
                pts REAL,
                plus_minus REAL,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(team_id, season, season_type)
            )''',
            
            'shot_chart_data': '''CREATE TABLE IF NOT EXISTS shot_chart_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                game_id TEXT,
                game_event_id TEXT,
                player_id TEXT,
                player_name TEXT,
                team_id TEXT,
                team_name TEXT,
                period INTEGER,
                minutes_remaining INTEGER,
                seconds_remaining INTEGER,
                event_type TEXT,
                action_type TEXT,
                shot_type TEXT,
                shot_zone_basic TEXT,
                shot_zone_area TEXT,
                shot_zone_range TEXT,
                shot_distance INTEGER,
                loc_x INTEGER,
                loc_y INTEGER,
                shot_attempted_flag INTEGER,
                shot_made_flag INTEGER,
                game_date TEXT,
                htm TEXT,
                vtm TEXT,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(game_id, game_event_id)
            )''',
            
            'hustle_stats': '''CREATE TABLE IF NOT EXISTS hustle_stats (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                player_id TEXT,
                team_id TEXT,
                season TEXT,
                player_name TEXT,
                team_abbreviation TEXT,
                age INTEGER,
                gp INTEGER,
                min REAL,
                screen_assists REAL,
                screen_ast_pts REAL,
                off_loose_balls_recovered REAL,
                def_loose_balls_recovered REAL,
                loose_balls_recovered REAL,
                charges_drawn REAL,
                contested_shots REAL,
                contested_shots_2pt REAL,
                contested_shots_3pt REAL,
                deflections REAL,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(player_id, season)
            )''',
            
            'tracking_stats': '''CREATE TABLE IF NOT EXISTS tracking_stats (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                player_id TEXT,
                team_id TEXT,
                season TEXT,
                player_name TEXT,
                gp INTEGER,
                min REAL,
                dist_feet REAL,
                dist_miles REAL,
                dist_miles_off REAL,
                dist_miles_def REAL,
                avg_speed REAL,
                avg_speed_off REAL,
                avg_speed_def REAL,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(player_id, season)
            )'''
        }
        
        for table_name, query in tables.items():
            self.conn.execute(query)
        self.conn.commit()

    def generate_seasons(self, start_year: int, end_year: int) -> List[str]:
        """Generate season strings from start to end year for WNBA"""
        seasons = []
        current_year = datetime.now().year
        # For WNBA, seasons are single years (e.g., 2024, not 2023-24)
        for year in range(start_year, min(end_year + 1, current_year + 1)):
            seasons.append(str(year))
        return seasons
        
    def safe_api_call(self, endpoint_func, **kwargs):
        """Safely call API endpoint with error handling and retries"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                time.sleep(0.6)  # Rate limiting
                result = endpoint_func(**kwargs)
                return result
            except Exception as e:
                self.logger.warning(f"API call failed (attempt {attempt + 1}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)
                else:
                    self.logger.error(f"API call failed after {max_retries} attempts: {e}")
                    return None

    def check_existing_data(self, table_name: str, **conditions) -> bool:
        """Check if data already exists in database"""
        try:
            where_clause = " AND ".join([f"{key} = ?" for key in conditions.keys()])
            query = f"SELECT COUNT(*) FROM {table_name} WHERE {where_clause}"
            cursor = self.conn.execute(query, list(conditions.values()))
            count = cursor.fetchone()[0]
            return count > 0
        except Exception:
            return False
            
    def get_missing_seasons(self, table_name: str) -> List[str]:
        """Get seasons that haven't been collected yet"""
        try:
            cursor = self.conn.execute(f"SELECT DISTINCT season FROM {table_name}")
            existing_seasons = set(row[0] for row in cursor.fetchall())
            missing_seasons = [s for s in self.seasons if s not in existing_seasons]
            return missing_seasons
        except Exception:
            return self.seasons
            
    def get_missing_games(self) -> List[str]:
        """Get game IDs that don't have boxscore data yet"""
        try:
            cursor = self.conn.execute("""
                SELECT DISTINCT g.game_id 
                FROM games g 
                LEFT JOIN boxscore_traditional bt ON g.game_id = bt.game_id 
                WHERE bt.game_id IS NULL AND g.game_id IS NOT NULL
            """)
            return [row[0] for row in cursor.fetchall()]
        except Exception:
            cursor = self.conn.execute("SELECT DISTINCT game_id FROM games WHERE game_id IS NOT NULL")
            return [row[0] for row in cursor.fetchall()]

    def collect_teams_data(self):
        """Collect all WNBA teams data for all seasons"""
        self.logger.info("Collecting teams data...")
        
        missing_seasons = self.get_missing_seasons('teams')
        if not missing_seasons:
            self.logger.info("All teams data already collected")
            return
            
        self.logger.info(f"Collecting teams data for {len(missing_seasons)} missing seasons: {missing_seasons}")
        
        for season in missing_seasons:
            try:
                # Check if this specific season already exists
                if self.check_existing_data('teams', season=season):
                    self.logger.info(f"Teams data for season {season} already exists, skipping")
                    continue
                    
                # For recent seasons (2024+), use scoreboard method directly as it's more reliable
                if int(season) >= 2024:
                    self.logger.info(f"Using scoreboard method for recent season {season}")
                    self.collect_teams_from_scoreboard(season)
                else:
                    # For older seasons, try traditional standings first
                    standings = self.safe_api_call(
                        leaguestandings.LeagueStandings,
                        league_id=self.wnba_league_id,
                        season=season,
                        season_type='Regular Season'
                    )

                    if standings:
                        data_frames = standings.get_data_frames()
                        if data_frames and len(data_frames) > 0:
                            df = data_frames[0]
                            new_records = 0
                            for _, row in df.iterrows():
                                # Check for duplicate before inserting
                                if not self.check_existing_data('teams', team_id=row.get('TeamID'), season=season):
                                    self.conn.execute('''
                                        INSERT INTO teams
                                        (team_id, season, team_name, abbreviation, city, conference, division, w, l, pct)
                                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                                    ''', (
                                        row.get('TeamID'), season, row.get('TeamName'), row.get('TeamSlug'),
                                        row.get('TeamCity'), row.get('Conference'), row.get('Division'),
                                        row.get('WINS'), row.get('LOSSES'), row.get('WinPCT')
                                    ))
                                    new_records += 1

                            self.conn.commit()
                            self.logger.info(f"Added {new_records} new team records for season {season}")
                        else:
                            self.logger.warning(f"No standings data found for season {season}")
                            # Fallback to scoreboard method
                            self.logger.info(f"Trying fallback scoreboard method for season {season}")
                            self.collect_teams_from_scoreboard(season)

            except Exception as e:
                self.logger.error(f"Error collecting teams for season {season}: {e}")
                # Try alternative method for recent seasons
                if int(season) >= 2024:
                    self.logger.info(f"Trying alternative scoreboard method for teams in season {season}")
                    self.collect_teams_from_scoreboard(season)

    def collect_teams_from_scoreboard(self, season):
        """Alternative method to collect teams using scoreboard endpoint"""
        self.logger.info(f"Collecting teams from scoreboard for season {season}")

        try:
            from datetime import datetime

            # Try multiple dates from the season to find team data
            test_dates = []
            season_year = int(season)

            if season_year == 2024:
                test_dates = [
                    datetime(2024, 5, 15),
                    datetime(2024, 6, 15),
                    datetime(2024, 7, 15)
                ]
            elif season_year == 2025:
                test_dates = [
                    datetime(2025, 5, 15),
                    datetime(2025, 6, 15),
                    datetime(2025, 7, 15)
                ]
            else:
                # For other seasons, try mid-year dates
                test_dates = [
                    datetime(season_year, 5, 15),
                    datetime(season_year, 6, 15),
                    datetime(season_year, 7, 15)
                ]

            teams_found = set()

            for test_date in test_dates:
                try:
                    self.logger.info(f"Trying date {test_date.strftime('%m/%d/%Y')} for season {season}")

                    scoreboard = self.safe_api_call(
                        scoreboardv2.ScoreboardV2,
                        league_id=self.wnba_league_id,
                        game_date=test_date.strftime('%m/%d/%Y')
                    )

                    if scoreboard:
                        data_frames = scoreboard.get_data_frames()
                        self.logger.info(f"Scoreboard returned {len(data_frames)} dataframes")

                        # Check all dataframes for team data
                        for df_idx, df in enumerate(data_frames):
                            if 'TEAM_ID' in df.columns and 'TEAM' in df.columns:
                                self.logger.info(f"Found team data in DataFrame {df_idx} with {len(df)} teams")

                                for _, row in df.iterrows():
                                    team_id = row.get('TEAM_ID')
                                    team_name = row.get('TEAM')

                                    if team_id and team_name:
                                        teams_found.add((
                                            str(team_id),
                                            str(team_name),
                                            row.get('CONFERENCE', ''),
                                            row.get('W', 0),
                                            row.get('L', 0),
                                            row.get('W_PCT', 0.0)
                                        ))

                    # If we found teams, break out of the date loop
                    if teams_found:
                        break

                except Exception as e:
                    self.logger.warning(f"Error with date {test_date.strftime('%m/%d/%Y')}: {e}")
                    continue

            # Insert the teams we found
            new_records = 0
            for team_id, team_name, conference, w, l, w_pct in teams_found:
                if not self.check_existing_data('teams', team_id=team_id, season=season):
                    self.conn.execute('''
                        INSERT INTO teams
                        (team_id, season, team_name, conference, w, l, pct)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    ''', (team_id, season, team_name, conference, w, l, w_pct))
                    new_records += 1

            if new_records > 0:
                self.conn.commit()
                self.logger.info(f"Added {new_records} new team records from scoreboard for season {season}")
            else:
                self.logger.warning(f"No new teams found from scoreboard for season {season}")

        except Exception as e:
            self.logger.error(f"Error in scoreboard team collection for season {season}: {e}")

    def collect_players_data(self):
        """Collect all WNBA players data"""
        self.logger.info("Collecting players data...")
        
        missing_seasons = self.get_missing_seasons('players')
        if not missing_seasons:
            self.logger.info("All players data already collected")
            return
            
        self.logger.info(f"Collecting players data for {len(missing_seasons)} missing seasons: {missing_seasons}")
        
        for season in missing_seasons:
            try:
                # Check if this specific season already exists
                existing_count = self.conn.execute("SELECT COUNT(*) FROM players WHERE season = ?", (season,)).fetchone()[0]
                if existing_count > 0:
                    self.logger.info(f"Players data for season {season} already exists ({existing_count} records), skipping")
                    continue
                    
                players = self.safe_api_call(
                    commonallplayers.CommonAllPlayers,
                    league_id=self.wnba_league_id,
                    season=season,
                    is_only_current_season=0
                )
                
                if players:
                    df = players.get_data_frames()[0]
                    new_records = 0
                    for _, row in df.iterrows():
                        # Check for duplicate before inserting
                        if not self.check_existing_data('players', player_id=row.get('PERSON_ID'), season=season):
                            self.conn.execute('''
                                INSERT INTO players 
                                (player_id, season, team_id, player_name, position, height, weight, birth_date, experience, college, country)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                            ''', (
                                row.get('PERSON_ID'), season, row.get('TEAM_ID'), row.get('DISPLAY_FIRST_LAST'),
                                row.get('POSITION'), row.get('HEIGHT'), row.get('WEIGHT'), row.get('BIRTHDATE'),
                                row.get('EXPERIENCE'), row.get('SCHOOL'), row.get('COUNTRY')
                            ))
                            new_records += 1
                    
                    self.conn.commit()
                    self.logger.info(f"Added {new_records} new player records for season {season}")
                    
            except Exception as e:
                self.logger.error(f"Error collecting players for season {season}: {e}")
                
    def collect_schedule_and_games(self):
        """Collect schedule and game data"""
        self.logger.info("Collecting schedule and games data...")
        
        missing_seasons = self.get_missing_seasons('games')
        if not missing_seasons:
            self.logger.info("All games data already collected")
            return
            
        self.logger.info(f"Collecting games data for {len(missing_seasons)} missing seasons: {missing_seasons}")
        
        for season in missing_seasons:
            try:
                existing_count = self.conn.execute("SELECT COUNT(*) FROM games WHERE season = ?", (season,)).fetchone()[0]
                if existing_count > 0:
                    self.logger.info(f"Games data for season {season} already exists ({existing_count} records), skipping")
                    continue
                    
                schedule = self.safe_api_call(
                    scheduleleaguev2.ScheduleLeagueV2,
                    league_id=self.wnba_league_id,
                    season=season
                )

                if schedule:
                    data_frames = schedule.get_data_frames()
                    if data_frames and len(data_frames) > 0:
                        df = data_frames[0]
                        new_records = 0
                        for _, row in df.iterrows():
                            game_id = row.get('GAME_ID')
                            if game_id and not self.check_existing_data('games', game_id=game_id):
                                self.conn.execute('''
                                    INSERT INTO games
                                    (game_id, game_date, season, home_team_id, away_team_id, home_score, away_score, game_status, season_type)
                                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                                ''', (
                                    game_id, row.get('GAME_DATE'), season, row.get('HOME_TEAM_ID'),
                                    row.get('VISITOR_TEAM_ID'), row.get('HOME_TEAM_SCORE'), row.get('VISITOR_TEAM_SCORE'),
                                    row.get('GAME_STATUS_TEXT'), row.get('SEASON_TYPE')
                                ))
                                new_records += 1

                        self.conn.commit()
                        self.logger.info(f"Added {new_records} new game records for season {season}")
                    else:
                        self.logger.warning(f"No schedule data found for season {season}")
                        # Try alternative method using scoreboard for recent seasons
                        if int(season) >= 2024:
                            self.logger.info(f"Trying alternative scoreboard method for season {season}")
                            self.collect_games_from_scoreboard(season)

            except Exception as e:
                self.logger.error(f"Error collecting schedule for season {season}: {e}")
                # Try alternative method for recent seasons
                if int(season) >= 2024:
                    self.logger.info(f"Trying alternative scoreboard method for season {season}")
                    self.collect_games_from_scoreboard(season)

    def collect_games_from_scoreboard(self, season):
        """Alternative method to collect games using scoreboard endpoint"""
        self.logger.info(f"Collecting games from scoreboard for season {season}")

        try:
            # For 2024 WNBA season, try some key dates
            from datetime import datetime, timedelta

            # WNBA season typically runs May-October
            season_dates = []
            if season == '2024':
                # Sample some dates throughout the 2024 season
                start_date = datetime(2024, 5, 15)
                end_date = datetime(2024, 10, 15)
                current_date = start_date

                # Sample every 7 days to get good coverage
                while current_date <= end_date:
                    season_dates.append(current_date)
                    current_date += timedelta(days=7)

            new_records = 0
            for game_date in season_dates:
                try:
                    scoreboard = self.safe_api_call(
                        scoreboardv2.ScoreboardV2,
                        league_id=self.wnba_league_id,
                        game_date=game_date.strftime('%m/%d/%Y')
                    )

                    if scoreboard:
                        data_frames = scoreboard.get_data_frames()
                        if data_frames and len(data_frames) > 0:
                            games_df = data_frames[0]  # First dataframe contains game info

                            for _, row in games_df.iterrows():
                                game_id = row.get('GAME_ID')
                                if game_id and not self.check_existing_data('games', game_id=game_id):
                                    self.conn.execute('''
                                        INSERT INTO games
                                        (game_id, game_date, season, home_team_id, away_team_id, game_status, season_type)
                                        VALUES (?, ?, ?, ?, ?, ?, ?)
                                    ''', (
                                        game_id, row.get('GAME_DATE_EST'), season,
                                        row.get('HOME_TEAM_ID'), row.get('VISITOR_TEAM_ID'),
                                        row.get('GAME_STATUS_TEXT'), 'Regular Season'
                                    ))
                                    new_records += 1

                except Exception as e:
                    self.logger.warning(f"Error collecting scoreboard for date {game_date}: {e}")
                    continue

            if new_records > 0:
                self.conn.commit()
                self.logger.info(f"Added {new_records} new game records from scoreboard for season {season}")
            else:
                self.logger.warning(f"No games found from scoreboard for season {season}")

        except Exception as e:
            self.logger.error(f"Error in scoreboard collection for season {season}: {e}")

    def collect_boxscore_data(self):
        """Collect detailed boxscore data for all games"""
        self.logger.info("Collecting boxscore data...")
        
        missing_games = self.get_missing_games()
        if not missing_games:
            self.logger.info("All boxscore data already collected")
            return
            
        self.logger.info(f"Collecting boxscore data for {len(missing_games)} missing games")
        
        for i, game_id in enumerate(missing_games):
            if i % 100 == 0:
                self.logger.info(f"Processing game {i+1}/{len(missing_games)}: {game_id}")
                
            try:
                # Check if boxscore already exists
                if self.check_existing_data('boxscore_traditional', game_id=game_id):
                    continue
                    
                # Traditional boxscore
                traditional = self.safe_api_call(
                    boxscoretraditionalv2.BoxScoreTraditionalV2,
                    game_id=game_id
                )
                
                if traditional:
                    player_stats = traditional.get_data_frames()[0]
                    for _, row in player_stats.iterrows():
                        if not self.check_existing_data('boxscore_traditional', game_id=game_id, player_id=row.get('PLAYER_ID')):
                            self.conn.execute('''
                                INSERT INTO boxscore_traditional 
                                (game_id, team_id, player_id, player_name, start_position, comment, min, 
                                 fgm, fga, fg_pct, fg3m, fg3a, fg3_pct, ftm, fta, ft_pct,
                                 oreb, dreb, reb, ast, stl, blk, tov, pf, pts, plus_minus)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                            ''', (
                                game_id, row.get('TEAM_ID'), row.get('PLAYER_ID'), row.get('PLAYER_NAME'),
                                row.get('START_POSITION'), row.get('COMMENT'), row.get('MIN'),
                                row.get('FGM'), row.get('FGA'), row.get('FG_PCT'),
                                row.get('FG3M'), row.get('FG3A'), row.get('FG3_PCT'),
                                row.get('FTM'), row.get('FTA'), row.get('FT_PCT'),
                                row.get('OREB'), row.get('DREB'), row.get('REB'),
                                row.get('AST'), row.get('STL'), row.get('BLK'),
                                row.get('TOV'), row.get('PF'), row.get('PTS'), row.get('PLUS_MINUS')
                            ))
                
                # Advanced boxscore
                advanced = self.safe_api_call(
                    boxscoreadvancedv2.BoxScoreAdvancedV2,
                    game_id=game_id
                )
                
                if advanced:
                    player_advanced = advanced.get_data_frames()[0]
                    for _, row in player_advanced.iterrows():
                        if not self.check_existing_data('boxscore_advanced', game_id=game_id, player_id=row.get('PLAYER_ID')):
                            self.conn.execute('''
                                INSERT INTO boxscore_advanced 
                                (game_id, team_id, player_id, player_name, min, e_off_rating, off_rating,
                                 e_def_rating, def_rating, e_net_rating, net_rating, ast_pct, ast_tov,
                                 ast_ratio, oreb_pct, dreb_pct, reb_pct, tm_tov_pct, efg_pct, ts_pct,
                                 usg_pct, e_usg_pct, e_pace, pace, pace_per40, poss, pie)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                            ''', (
                                game_id, row.get('TEAM_ID'), row.get('PLAYER_ID'), row.get('PLAYER_NAME'),
                                row.get('MIN'), row.get('E_OFF_RATING'), row.get('OFF_RATING'),
                                row.get('E_DEF_RATING'), row.get('DEF_RATING'), row.get('E_NET_RATING'),
                                row.get('NET_RATING'), row.get('AST_PCT'), row.get('AST_TOV'),
                                row.get('AST_RATIO'), row.get('OREB_PCT'), row.get('DREB_PCT'),
                                row.get('REB_PCT'), row.get('TM_TOV_PCT'), row.get('EFG_PCT'),
                                row.get('TS_PCT'), row.get('USG_PCT'), row.get('E_USG_PCT'),
                                row.get('E_PACE'), row.get('PACE'), row.get('PACE_PER40'),
                                row.get('POSS'), row.get('PIE')
                            ))
                
                if i % 50 == 0:
                    self.conn.commit()
                    
            except Exception as e:
                self.logger.error(f"Error collecting boxscore for game {game_id}: {e}")
                
        self.conn.commit()

    def collect_hustle_stats(self):
        """Collect hustle statistics for all players"""
        self.logger.info("Collecting hustle stats...")
        
        missing_seasons = self.get_missing_seasons('hustle_stats')
        if not missing_seasons:
            self.logger.info("All hustle stats already collected")
            return
            
        self.logger.info(f"Collecting hustle stats for {len(missing_seasons)} missing seasons: {missing_seasons}")
        
        for season in missing_seasons:
            try:
                existing_count = self.conn.execute("SELECT COUNT(*) FROM hustle_stats WHERE season = ?", (season,)).fetchone()[0]
                if existing_count > 0:
                    self.logger.info(f"Hustle stats for season {season} already exists ({existing_count} records), skipping")
                    continue
                    
                hustle_stats = self.safe_api_call(
                    leaguehustlestatsplayer.LeagueHustleStatsPlayer,
                    season=season,
                    season_type_all_star='Regular Season'
                )
                
                if hustle_stats:
                    df = hustle_stats.get_data_frames()[0]
                    new_records = 0
                    for _, row in df.iterrows():
                        if not self.check_existing_data('hustle_stats', player_id=row.get('PLAYER_ID'), season=season):
                            self.conn.execute('''
                                INSERT INTO hustle_stats 
                                (player_id, team_id, season, player_name, team_abbreviation, age, gp, min,
                                 screen_assists, screen_ast_pts, off_loose_balls_recovered, def_loose_balls_recovered,
                                 loose_balls_recovered, charges_drawn, contested_shots, contested_shots_2pt,
                                 contested_shots_3pt, deflections)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                            ''', (
                                row.get('PLAYER_ID'), row.get('TEAM_ID'), season,
                                row.get('PLAYER_NAME'), row.get('TEAM_ABBREVIATION'), row.get('AGE'),
                                row.get('GP'), row.get('MIN'), row.get('SCREEN_ASSISTS'),
                                row.get('SCREEN_AST_PTS'), row.get('OFF_LOOSE_BALLS_RECOVERED'),
                                row.get('DEF_LOOSE_BALLS_RECOVERED'), row.get('LOOSE_BALLS_RECOVERED'),
                                row.get('CHARGES_DRAWN'), row.get('CONTESTED_SHOTS'),
                                row.get('CONTESTED_SHOTS_2PT'), row.get('CONTESTED_SHOTS_3PT'),
                                row.get('DEFLECTIONS')
                            ))
                            new_records += 1
                    
                    self.conn.commit()
                    self.logger.info(f"Added {new_records} new hustle stat records for season {season}")
                    
            except Exception as e:
                self.logger.error(f"Error collecting hustle stats for season {season}: {e}")

    def collect_player_career_stats(self):
        """Collect comprehensive player career statistics using all available data sets"""
        self.logger.info("Collecting comprehensive player career stats...")

        missing_seasons = self.get_missing_seasons('player_career_stats')
        if not missing_seasons:
            self.logger.info("All player career stats already collected")
            return

        self.logger.info(f"Collecting player career stats for {len(missing_seasons)} missing seasons: {missing_seasons}")

        for season in missing_seasons:
            try:
                existing_count = self.conn.execute("SELECT COUNT(*) FROM player_career_stats WHERE season_id = ?", (season,)).fetchone()[0]
                if existing_count > 0:
                    self.logger.info(f"Player career stats for season {season} already exists ({existing_count} records), skipping")
                    continue

                # Get all players for this season first
                players_cursor = self.conn.execute("SELECT DISTINCT player_id FROM players WHERE season = ?", (season,))
                player_ids = [row[0] for row in players_cursor.fetchall()]

                new_records = 0
                for i, player_id in enumerate(player_ids):
                    if i % 50 == 0:
                        self.logger.info(f"Processing player {i+1}/{len(player_ids)} for season {season}")

                    try:
                        career_stats = self.safe_api_call(
                            playercareerstats.PlayerCareerStats,
                            player_id=player_id,
                            league_id_nullable=self.wnba_league_id
                        )

                        if career_stats:
                            # Get season totals regular season (primary data set)
                            season_totals = career_stats.season_totals_regular_season.get_data_frame()
                            for _, row in season_totals.iterrows():
                                if row.get('SEASON_ID') == season:
                                    if not self.check_existing_data('player_career_stats', player_id=player_id, season_id=season):
                                        self.conn.execute('''
                                            INSERT INTO player_career_stats
                                            (player_id, season_id, league_id, team_id, team_abbreviation, player_age,
                                             gp, gs, min, fgm, fga, fg_pct, fg3m, fg3a, fg3_pct, ftm, fta, ft_pct,
                                             oreb, dreb, reb, ast, stl, blk, tov, pf, pts)
                                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                                        ''', (
                                            player_id, season, row.get('LEAGUE_ID'), row.get('TEAM_ID'),
                                            row.get('TEAM_ABBREVIATION'), row.get('PLAYER_AGE'),
                                            row.get('GP'), row.get('GS'), row.get('MIN'),
                                            row.get('FGM'), row.get('FGA'), row.get('FG_PCT'),
                                            row.get('FG3M'), row.get('FG3A'), row.get('FG3_PCT'),
                                            row.get('FTM'), row.get('FTA'), row.get('FT_PCT'),
                                            row.get('OREB'), row.get('DREB'), row.get('REB'),
                                            row.get('AST'), row.get('STL'), row.get('BLK'),
                                            row.get('TOV'), row.get('PF'), row.get('PTS')
                                        ))
                                        new_records += 1
                    except Exception as e:
                        self.logger.warning(f"Error collecting career stats for player {player_id}: {e}")
                        continue

                if new_records > 0:
                    self.conn.commit()
                    self.logger.info(f"Added {new_records} new player career stat records for season {season}")

            except Exception as e:
                self.logger.error(f"Error collecting player career stats for season {season}: {e}")

    def collect_player_game_logs(self):
        """Collect comprehensive player game logs for all players and seasons"""
        self.logger.info("Collecting player game logs...")

        # Get all players from the database
        try:
            cursor = self.conn.execute("""
                SELECT DISTINCT p.player_id, p.player_name, p.season
                FROM players p
                LEFT JOIN player_game_logs pgl ON p.player_id = pgl.player_id AND p.season = pgl.season
                WHERE pgl.player_id IS NULL
                ORDER BY p.season DESC, p.player_name
                LIMIT 100
            """)
            missing_players = cursor.fetchall()
        except Exception:
            # Fallback: get all players
            cursor = self.conn.execute("SELECT DISTINCT player_id, player_name, season FROM players ORDER BY season DESC LIMIT 100")
            missing_players = cursor.fetchall()

        if not missing_players:
            self.logger.info("All player game logs already collected or no players available")
            return

        self.logger.info(f"Collecting game logs for {len(missing_players)} players")

        for i, (player_id, player_name, season) in enumerate(missing_players):
            if i % 10 == 0:
                self.logger.info(f"Processing player {i+1}/{len(missing_players)}: {player_name} ({season})")

            try:
                # Check if this player's game logs already exist for this season
                existing_count = self.conn.execute(
                    "SELECT COUNT(*) FROM player_game_logs WHERE player_id = ? AND season = ?",
                    (player_id, season)
                ).fetchone()[0]

                if existing_count > 0:
                    self.logger.debug(f"Game logs for {player_name} ({season}) already exist ({existing_count} games), skipping")
                    continue

                # Collect game logs using PlayerGameLog endpoint
                game_logs = self.safe_api_call(
                    playergamelog.PlayerGameLog,
                    player_id=player_id,
                    season=season,
                    season_type_all_star='Regular Season'
                )

                if game_logs:
                    df = game_logs.get_data_frames()[0]
                    new_records = 0

                    for _, row in df.iterrows():
                        game_id = row.get('Game_ID') or row.get('GAME_ID')
                        if game_id and not self.check_existing_data('player_game_logs', player_id=player_id, game_id=game_id):
                            self.conn.execute('''
                                INSERT INTO player_game_logs
                                (player_id, player_name, season, game_id, game_date, matchup, wl, min, pts,
                                 fgm, fga, fg_pct, fg3m, fg3a, fg3_pct, ftm, fta, ft_pct,
                                 oreb, dreb, reb, ast, stl, blk, tov, pf, plus_minus, video_available)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                            ''', (
                                player_id, player_name, season, str(game_id),
                                row.get('GAME_DATE'), row.get('MATCHUP'), row.get('WL'),
                                row.get('MIN'), row.get('PTS'), row.get('FGM'), row.get('FGA'), row.get('FG_PCT'),
                                row.get('FG3M'), row.get('FG3A'), row.get('FG3_PCT'),
                                row.get('FTM'), row.get('FTA'), row.get('FT_PCT'),
                                row.get('OREB'), row.get('DREB'), row.get('REB'),
                                row.get('AST'), row.get('STL'), row.get('BLK'),
                                row.get('TOV'), row.get('PF'), row.get('PLUS_MINUS'),
                                row.get('VIDEO_AVAILABLE', 0)
                            ))
                            new_records += 1

                    if new_records > 0:
                        self.conn.commit()
                        self.logger.info(f"Added {new_records} game log records for {player_name} ({season})")
                    else:
                        self.logger.debug(f"No new game logs found for {player_name} ({season})")

            except Exception as e:
                self.logger.error(f"Error collecting game logs for {player_name} ({player_id}): {e}")
                continue

        self.logger.info("Player game log collection completed")

    def collect_team_game_logs(self):
        """Collect comprehensive team game logs for all teams and seasons"""
        self.logger.info("Collecting team game logs...")

        # Get all teams from the database
        try:
            cursor = self.conn.execute("""
                SELECT DISTINCT t.team_id, t.team_name, t.season
                FROM teams t
                LEFT JOIN team_game_logs tgl ON t.team_id = tgl.team_id AND t.season = tgl.season
                WHERE tgl.team_id IS NULL
                ORDER BY t.season DESC, t.team_name
                LIMIT 50
            """)
            missing_teams = cursor.fetchall()
        except Exception:
            # Fallback: get all teams
            cursor = self.conn.execute("SELECT DISTINCT team_id, team_name, season FROM teams ORDER BY season DESC LIMIT 50")
            missing_teams = cursor.fetchall()

        if not missing_teams:
            self.logger.info("All team game logs already collected or no teams available")
            return

        self.logger.info(f"Collecting game logs for {len(missing_teams)} teams")

        for i, (team_id, team_name, season) in enumerate(missing_teams):
            if i % 5 == 0:
                self.logger.info(f"Processing team {i+1}/{len(missing_teams)}: {team_name} ({season})")

            try:
                # Check if this team's game logs already exist for this season
                existing_count = self.conn.execute(
                    "SELECT COUNT(*) FROM team_game_logs WHERE team_id = ? AND season = ?",
                    (team_id, season)
                ).fetchone()[0]

                if existing_count > 0:
                    self.logger.debug(f"Game logs for {team_name} ({season}) already exist ({existing_count} games), skipping")
                    continue

                # Collect game logs using TeamGameLog endpoint with correct parameters
                game_logs = self.safe_api_call(
                    teamgamelog.TeamGameLog,
                    team_id=team_id,
                    season=season,
                    season_type_all_star='Regular Season',
                    league_id_nullable=self.wnba_league_id
                )

                if game_logs:
                    df = game_logs.get_data_frames()[0]
                    new_records = 0

                    for _, row in df.iterrows():
                        game_id = row.get('Game_ID') or row.get('GAME_ID')
                        if game_id and not self.check_existing_data('team_game_logs', team_id=team_id, game_id=game_id):
                            self.conn.execute('''
                                INSERT INTO team_game_logs
                                (team_id, team_name, season, game_id, game_date, matchup, wl, w, l, w_pct, min, pts,
                                 fgm, fga, fg_pct, fg3m, fg3a, fg3_pct, ftm, fta, ft_pct,
                                 oreb, dreb, reb, ast, stl, blk, tov, pf)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                            ''', (
                                team_id, team_name, season, str(game_id),
                                row.get('GAME_DATE'), row.get('MATCHUP'), row.get('WL'),
                                row.get('W'), row.get('L'), row.get('W_PCT'),
                                row.get('MIN'), row.get('PTS'), row.get('FGM'), row.get('FGA'), row.get('FG_PCT'),
                                row.get('FG3M'), row.get('FG3A'), row.get('FG3_PCT'),
                                row.get('FTM'), row.get('FTA'), row.get('FT_PCT'),
                                row.get('OREB'), row.get('DREB'), row.get('REB'),
                                row.get('AST'), row.get('STL'), row.get('BLK'),
                                row.get('TOV'), row.get('PF')
                            ))
                            new_records += 1

                    if new_records > 0:
                        self.conn.commit()
                        self.logger.info(f"Added {new_records} game log records for {team_name} ({season})")
                    else:
                        self.logger.debug(f"No new game logs found for {team_name} ({season})")

            except Exception as e:
                self.logger.error(f"Error collecting game logs for {team_name} ({team_id}): {e}")
                continue

        self.logger.info("Team game log collection completed")

    def collect_defense_hub_stats(self):
        """Collect defensive statistics from DefenseHub endpoint"""
        self.logger.info("Collecting defense hub stats...")

        for season in self.seasons:
            try:
                existing_count = self.conn.execute("SELECT COUNT(*) FROM defense_hub_stats WHERE season = ?", (season,)).fetchone()[0]
                if existing_count > 0:
                    self.logger.info(f"Defense hub stats for season {season} already exists ({existing_count} records), skipping")
                    continue

                # Use correct parameters for DefenseHub
                defense_stats = self.safe_api_call(
                    defensehub.DefenseHub,
                    season=season,
                    season_type_playoffs='Regular Season',  # Correct parameter name
                    league_id=self.wnba_league_id,
                    player_or_team='Team'
                )

                if defense_stats:
                    # DefenseHub returns multiple stat datasets
                    data_frames = defense_stats.get_data_frames()
                    new_records = 0

                    for i, df in enumerate(data_frames):
                        stat_type = f"DefenseHubStat{i+1}"

                        for _, row in df.iterrows():
                            if not self.check_existing_data('defense_hub_stats', season=season, stat_type=stat_type, team_id=row.get('TEAM_ID')):
                                self.conn.execute('''
                                    INSERT INTO defense_hub_stats
                                    (season, stat_type, rank_num, team_id, team_abbreviation, team_name,
                                     dreb, stl, blk, tm_def_rating, overall_pm, threep_dfgpct, twop_dfgpct,
                                     fifeteenf_dfgpct, def_rim_pct)
                                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                                ''', (
                                    season, stat_type, row.get('RANK'), row.get('TEAM_ID'),
                                    row.get('TEAM_ABBREVIATION'), row.get('TEAM_NAME'),
                                    row.get('DREB'), row.get('STL'), row.get('BLK'),
                                    row.get('TM_DEF_RATING'), row.get('OVERALL_PM'),
                                    row.get('THREEP_DFGPCT'), row.get('TWOP_DFGPCT'),
                                    row.get('FIFETEENF_DFGPCT'), row.get('DEF_RIM_PCT')
                                ))
                                new_records += 1

                    if new_records > 0:
                        self.conn.commit()
                        self.logger.info(f"Added {new_records} defense hub stat records for season {season}")

            except Exception as e:
                self.logger.error(f"Error collecting defense hub stats for season {season}: {e}")

    def collect_game_rotations(self):
        """Collect game rotation data for available games"""
        self.logger.info("Collecting game rotations...")

        # Get unique game IDs from our game data
        try:
            cursor = self.conn.execute("""
                SELECT DISTINCT game_id
                FROM (
                    SELECT game_id FROM player_game_logs
                    UNION
                    SELECT game_id FROM team_game_logs
                )
                WHERE game_id NOT IN (SELECT DISTINCT game_id FROM game_rotations WHERE game_id IS NOT NULL)
                LIMIT 20
            """)
            missing_games = [row[0] for row in cursor.fetchall()]
        except Exception:
            missing_games = []

        if not missing_games:
            self.logger.info("All game rotation data already collected or no games available")
            return

        self.logger.info(f"Collecting rotation data for {len(missing_games)} games")

        for i, game_id in enumerate(missing_games):
            if i % 5 == 0:
                self.logger.info(f"Processing game {i+1}/{len(missing_games)}: {game_id}")

            try:
                rotations = self.safe_api_call(
                    gamerotation.GameRotation,
                    game_id=game_id,
                    league_id=self.wnba_league_id
                )

                if rotations:
                    data_frames = rotations.get_data_frames()
                    new_records = 0

                    # Process both home and away team rotations
                    for df_idx, df in enumerate(data_frames):
                        home_away = 'Home' if df_idx == 1 else 'Away'

                        for _, row in df.iterrows():
                            if not self.check_existing_data('game_rotations', game_id=game_id, person_id=row.get('PERSON_ID'), in_time_real=row.get('IN_TIME_REAL')):
                                self.conn.execute('''
                                    INSERT INTO game_rotations
                                    (game_id, team_id, team_city, team_name, person_id, player_first, player_last,
                                     in_time_real, out_time_real, player_pts, pt_diff, usg_pct, home_away)
                                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                                ''', (
                                    game_id, row.get('TEAM_ID'), row.get('TEAM_CITY'), row.get('TEAM_NAME'),
                                    row.get('PERSON_ID'), row.get('PLAYER_FIRST'), row.get('PLAYER_LAST'),
                                    row.get('IN_TIME_REAL'), row.get('OUT_TIME_REAL'),
                                    row.get('PLAYER_PTS'), row.get('PT_DIFF'), row.get('USG_PCT'), home_away
                                ))
                                new_records += 1

                    if new_records > 0:
                        self.conn.commit()
                        self.logger.info(f"Added {new_records} rotation records for game {game_id}")

            except Exception as e:
                self.logger.error(f"Error collecting rotations for game {game_id}: {e}")

    def collect_lineups_data(self):
        """Collect lineup data using LeagueDashLineups endpoint"""
        self.logger.info("Collecting lineups data...")

        for season in self.seasons:
            try:
                existing_count = self.conn.execute("SELECT COUNT(*) FROM lineups WHERE season = ?", (season,)).fetchone()[0]
                if existing_count > 0:
                    self.logger.info(f"Lineups for season {season} already exists ({existing_count} records), skipping")
                    continue

                # Use correct parameters for LeagueDashLineups
                lineups = self.safe_api_call(
                    leaguedashlineups.LeagueDashLineups,
                    season=season,
                    season_type_all_star='Regular Season',
                    league_id_nullable=self.wnba_league_id
                )

                if lineups:
                    df = lineups.get_data_frames()[0]
                    new_records = 0

                    for _, row in df.iterrows():
                        if not self.check_existing_data('lineups', season=season, group_id=row.get('GROUP_ID')):
                            self.conn.execute('''
                                INSERT INTO lineups
                                (season, group_set, group_id, group_name, team_id, team_abbreviation,
                                 gp, w, l, w_pct, min, fgm, fga, fg_pct, fg3m, fg3a, fg3_pct,
                                 ftm, fta, ft_pct, oreb, dreb, reb, ast, tov, stl, blk, blka, pf, pfd, pts, plus_minus)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                            ''', (
                                season, row.get('GROUP_SET'), row.get('GROUP_ID'), row.get('GROUP_NAME'),
                                row.get('TEAM_ID'), row.get('TEAM_ABBREVIATION'),
                                row.get('GP'), row.get('W'), row.get('L'), row.get('W_PCT'),
                                row.get('MIN'), row.get('FGM'), row.get('FGA'), row.get('FG_PCT'),
                                row.get('FG3M'), row.get('FG3A'), row.get('FG3_PCT'),
                                row.get('FTM'), row.get('FTA'), row.get('FT_PCT'),
                                row.get('OREB'), row.get('DREB'), row.get('REB'),
                                row.get('AST'), row.get('TOV'), row.get('STL'),
                                row.get('BLK'), row.get('BLKA'), row.get('PF'), row.get('PFD'),
                                row.get('PTS'), row.get('PLUS_MINUS')
                            ))
                            new_records += 1

                    if new_records > 0:
                        self.conn.commit()
                        self.logger.info(f"Added {new_records} lineup records for season {season}")

            except Exception as e:
                self.logger.error(f"Error collecting lineups for season {season}: {e}")

    def collect_team_rosters(self):
        """Collect detailed team roster information including players and coaches"""
        self.logger.info("Collecting team rosters...")

        # Get all teams from the database
        try:
            cursor = self.conn.execute("""
                SELECT DISTINCT t.team_id, t.team_name, t.season
                FROM teams t
                LEFT JOIN team_rosters tr ON t.team_id = tr.team_id AND t.season = tr.season
                WHERE tr.team_id IS NULL
                ORDER BY t.season DESC, t.team_name
                LIMIT 30
            """)
            missing_teams = cursor.fetchall()
        except Exception:
            # Fallback: get all teams
            cursor = self.conn.execute("SELECT DISTINCT team_id, team_name, season FROM teams ORDER BY season DESC LIMIT 30")
            missing_teams = cursor.fetchall()

        if not missing_teams:
            self.logger.info("All team rosters already collected or no teams available")
            return

        self.logger.info(f"Collecting rosters for {len(missing_teams)} teams")

        for i, (team_id, team_name, season) in enumerate(missing_teams):
            if i % 5 == 0:
                self.logger.info(f"Processing team {i+1}/{len(missing_teams)}: {team_name} ({season})")

            try:
                # Check if this team's roster already exists for this season
                existing_count = self.conn.execute(
                    "SELECT COUNT(*) FROM team_rosters WHERE team_id = ? AND season = ?",
                    (team_id, season)
                ).fetchone()[0]

                if existing_count > 0:
                    self.logger.debug(f"Roster for {team_name} ({season}) already exists ({existing_count} players), skipping")
                    continue

                # Collect roster using CommonTeamRoster endpoint
                roster = self.safe_api_call(
                    commonteamroster.CommonTeamRoster,
                    team_id=team_id,
                    season=season,
                    league_id_nullable=self.wnba_league_id
                )

                if roster:
                    data_frames = roster.get_data_frames()
                    new_player_records = 0
                    new_coach_records = 0

                    # Process player roster data (CommonTeamRoster dataset)
                    if len(data_frames) > 0:
                        players_df = data_frames[0]  # CommonTeamRoster

                        for _, row in players_df.iterrows():
                            player_id = row.get('PLAYER_ID')
                            if player_id and not self.check_existing_data('team_rosters', team_id=team_id, season=season, player_id=player_id):
                                self.conn.execute('''
                                    INSERT INTO team_rosters
                                    (team_id, season, league_id, player_id, player_name, player_slug, jersey_num,
                                     position, height, weight, birth_date, age, experience, school)
                                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                                ''', (
                                    team_id, season, row.get('LeagueID'), player_id,
                                    row.get('PLAYER'), row.get('PLAYER_SLUG'), row.get('NUM'),
                                    row.get('POSITION'), row.get('HEIGHT'), row.get('WEIGHT'),
                                    row.get('BIRTH_DATE'), row.get('AGE'), row.get('EXP'), row.get('SCHOOL')
                                ))
                                new_player_records += 1

                    # Process coaches data (Coaches dataset)
                    if len(data_frames) > 1:
                        coaches_df = data_frames[1]  # Coaches

                        for _, row in coaches_df.iterrows():
                            coach_id = row.get('COACH_ID')
                            if coach_id and not self.check_existing_data('team_coaches', team_id=team_id, season=season, coach_id=coach_id):
                                self.conn.execute('''
                                    INSERT INTO team_coaches
                                    (team_id, season, coach_id, first_name, last_name, coach_name,
                                     is_assistant, coach_type, sort_sequence)
                                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                                ''', (
                                    team_id, season, coach_id,
                                    row.get('FIRST_NAME'), row.get('LAST_NAME'), row.get('COACH_NAME'),
                                    row.get('IS_ASSISTANT'), row.get('COACH_TYPE'), row.get('SORT_SEQUENCE')
                                ))
                                new_coach_records += 1

                    if new_player_records > 0 or new_coach_records > 0:
                        self.conn.commit()
                        self.logger.info(f"Added {new_player_records} players and {new_coach_records} coaches for {team_name} ({season})")
                    else:
                        self.logger.debug(f"No new roster data found for {team_name} ({season})")

            except Exception as e:
                self.logger.error(f"Error collecting roster for {team_name} ({team_id}): {e}")
                continue

        self.logger.info("Team roster collection completed")

    def collect_player_last_n_games_analytics(self):
        """Collect player performance analytics over different time periods (Last 5, 10, 15, 20 games)"""
        self.logger.info("Collecting player last N games analytics...")

        # Get players who have game logs but no last N games analytics
        try:
            cursor = self.conn.execute("""
                SELECT DISTINCT pgl.player_id, p.player_name, pgl.season
                FROM player_game_logs pgl
                JOIN players p ON pgl.player_id = p.player_id AND pgl.season = p.season
                LEFT JOIN player_last_n_games plng ON pgl.player_id = plng.player_id AND pgl.season = plng.season
                WHERE plng.player_id IS NULL
                GROUP BY pgl.player_id, p.player_name, pgl.season
                HAVING COUNT(*) >= 5
                ORDER BY pgl.season DESC, COUNT(*) DESC
                LIMIT 20
            """)
            missing_players = cursor.fetchall()
        except Exception:
            # Fallback: get players from players table
            cursor = self.conn.execute("""
                SELECT DISTINCT player_id, player_name, season
                FROM players
                ORDER BY season DESC
                LIMIT 20
            """)
            missing_players = cursor.fetchall()

        if not missing_players:
            self.logger.info("All player last N games analytics already collected or no eligible players")
            return

        self.logger.info(f"Collecting last N games analytics for {len(missing_players)} players")

        for i, (player_id, player_name, season) in enumerate(missing_players):
            if i % 5 == 0:
                self.logger.info(f"Processing player {i+1}/{len(missing_players)}: {player_name} ({season})")

            try:
                # Check if this player's analytics already exist for this season
                existing_count = self.conn.execute(
                    "SELECT COUNT(*) FROM player_last_n_games WHERE player_id = ? AND season = ?",
                    (player_id, season)
                ).fetchone()[0]

                if existing_count > 0:
                    self.logger.debug(f"Last N games analytics for {player_name} ({season}) already exist, skipping")
                    continue

                # Collect analytics using PlayerDashboardByLastNGames endpoint
                analytics = self.safe_api_call(
                    playerdashboardbylastngames.PlayerDashboardByLastNGames,
                    player_id=player_id,
                    season=season,
                    season_type_playoffs='Regular Season',
                    league_id_nullable=self.wnba_league_id
                )

                if analytics:
                    data_frames = analytics.get_data_frames()
                    new_records = 0

                    # Map dataframe indices to game periods
                    period_mapping = {
                        0: 'GameNumber',    # GameNumberPlayerDashboard
                        1: 'Last10',        # Last10PlayerDashboard
                        2: 'Last15',        # Last15PlayerDashboard
                        3: 'Last20',        # Last20PlayerDashboard
                        4: 'Last5',         # Last5PlayerDashboard
                        5: 'Overall'        # OverallPlayerDashboard
                    }

                    for df_idx, df in enumerate(data_frames):
                        game_period = period_mapping.get(df_idx, f'Unknown_{df_idx}')

                        for _, row in df.iterrows():
                            group_value = row.get('GROUP_VALUE', '')

                            if not self.check_existing_data('player_last_n_games',
                                                          player_id=player_id,
                                                          season=season,
                                                          game_period=game_period,
                                                          group_value=group_value):
                                self.conn.execute('''
                                    INSERT INTO player_last_n_games
                                    (player_id, season, game_period, group_set, group_value, gp, w, l, w_pct,
                                     min, fgm, fga, fg_pct, fg3m, fg3a, fg3_pct, ftm, fta, ft_pct,
                                     oreb, dreb, reb, ast, tov, stl, blk, blka, pf, pfd, pts, plus_minus,
                                     nba_fantasy_pts, dd2, td3)
                                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                                ''', (
                                    player_id, season, game_period, row.get('GROUP_SET'), group_value,
                                    row.get('GP'), row.get('W'), row.get('L'), row.get('W_PCT'),
                                    row.get('MIN'), row.get('FGM'), row.get('FGA'), row.get('FG_PCT'),
                                    row.get('FG3M'), row.get('FG3A'), row.get('FG3_PCT'),
                                    row.get('FTM'), row.get('FTA'), row.get('FT_PCT'),
                                    row.get('OREB'), row.get('DREB'), row.get('REB'),
                                    row.get('AST'), row.get('TOV'), row.get('STL'),
                                    row.get('BLK'), row.get('BLKA'), row.get('PF'), row.get('PFD'),
                                    row.get('PTS'), row.get('PLUS_MINUS'), row.get('NBA_FANTASY_PTS'),
                                    row.get('DD2'), row.get('TD3')
                                ))
                                new_records += 1

                    if new_records > 0:
                        self.conn.commit()
                        self.logger.info(f"Added {new_records} last N games analytics for {player_name} ({season})")
                    else:
                        self.logger.debug(f"No new analytics found for {player_name} ({season})")

            except Exception as e:
                self.logger.error(f"Error collecting last N games analytics for {player_name} ({player_id}): {e}")
                continue

        self.logger.info("Player last N games analytics collection completed")

    def collect_team_vs_player_matchups(self):
        """Collect team vs player matchup analytics (on/off court impact)"""
        self.logger.info("Collecting team vs player matchup analytics...")

        # Get combinations of teams and key players for matchup analysis
        try:
            cursor = self.conn.execute("""
                SELECT DISTINCT
                    t.team_id, t.team_name, t.season,
                    p.player_id, p.player_name
                FROM teams t
                CROSS JOIN (
                    SELECT DISTINCT player_id, player_name, season
                    FROM player_game_logs
                    GROUP BY player_id, player_name, season
                    HAVING COUNT(*) >= 10
                    ORDER BY AVG(pts) DESC
                    LIMIT 20
                ) p ON t.season = p.season
                LEFT JOIN team_vs_player_matchups tvpm ON t.team_id = tvpm.team_id
                    AND p.player_id = tvpm.vs_player_id AND t.season = tvpm.season
                WHERE tvpm.team_id IS NULL
                ORDER BY t.season DESC
                LIMIT 30
            """)
            missing_matchups = cursor.fetchall()
        except Exception:
            # Fallback: get basic team/player combinations
            cursor = self.conn.execute("""
                SELECT DISTINCT t.team_id, t.team_name, t.season, p.player_id, p.player_name
                FROM teams t, players p
                WHERE t.season = p.season
                ORDER BY t.season DESC
                LIMIT 10
            """)
            missing_matchups = cursor.fetchall()

        if not missing_matchups:
            self.logger.info("All team vs player matchups already collected or no eligible combinations")
            return

        self.logger.info(f"Collecting matchup analytics for {len(missing_matchups)} team-player combinations")

        for i, (team_id, team_name, season, vs_player_id, vs_player_name) in enumerate(missing_matchups):
            if i % 5 == 0:
                self.logger.info(f"Processing matchup {i+1}/{len(missing_matchups)}: {team_name} vs {vs_player_name} ({season})")

            try:
                # Check if this matchup already exists
                existing_count = self.conn.execute(
                    "SELECT COUNT(*) FROM team_vs_player_matchups WHERE team_id = ? AND vs_player_id = ? AND season = ?",
                    (team_id, vs_player_id, season)
                ).fetchone()[0]

                if existing_count > 0:
                    self.logger.debug(f"Matchup {team_name} vs {vs_player_name} ({season}) already exists, skipping")
                    continue

                # Collect matchup analytics using TeamVsPlayer endpoint
                matchup = self.safe_api_call(
                    teamvsplayer.TeamVsPlayer,
                    vs_player_id=vs_player_id,
                    team_id=team_id,
                    season=season,
                    season_type_playoffs='Regular Season',
                    league_id_nullable=self.wnba_league_id
                )

                if matchup:
                    data_frames = matchup.get_data_frames()
                    new_records = 0

                    # Map dataframe indices to matchup types
                    matchup_mapping = {
                        0: 'OnOffCourt',           # OnOffCourt
                        1: 'Overall',              # Overall
                        2: 'ShotAreaOffCourt',     # ShotAreaOffCourt
                        3: 'ShotAreaOnCourt',      # ShotAreaOnCourt
                        4: 'ShotAreaOverall',      # ShotAreaOverall
                        5: 'ShotDistanceOffCourt', # ShotDistanceOffCourt
                        6: 'ShotDistanceOnCourt',  # ShotDistanceOnCourt
                        7: 'ShotDistanceOverall',  # ShotDistanceOverall
                        8: 'vsPlayerOverall'       # vsPlayerOverall
                    }

                    for df_idx, df in enumerate(data_frames):
                        matchup_type = matchup_mapping.get(df_idx, f'Unknown_{df_idx}')

                        for _, row in df.iterrows():
                            court_status = row.get('COURT_STATUS', '')
                            group_value = row.get('GROUP_VALUE', '')

                            if not self.check_existing_data('team_vs_player_matchups',
                                                          team_id=team_id,
                                                          vs_player_id=vs_player_id,
                                                          season=season,
                                                          matchup_type=matchup_type,
                                                          court_status=court_status,
                                                          group_value=group_value):
                                self.conn.execute('''
                                    INSERT INTO team_vs_player_matchups
                                    (season, team_id, team_name, vs_player_id, vs_player_name, matchup_type,
                                     court_status, group_value, gp, w, l, w_pct, min, fgm, fga, fg_pct,
                                     fg3m, fg3a, fg3_pct, ftm, fta, ft_pct, oreb, dreb, reb, ast, tov,
                                     stl, blk, blka, pf, pfd, pts, plus_minus, nba_fantasy_pts, dd2, td3)
                                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                                ''', (
                                    season, team_id, team_name, vs_player_id, vs_player_name, matchup_type,
                                    court_status, group_value, row.get('GP'), row.get('W'), row.get('L'), row.get('W_PCT'),
                                    row.get('MIN'), row.get('FGM'), row.get('FGA'), row.get('FG_PCT'),
                                    row.get('FG3M'), row.get('FG3A'), row.get('FG3_PCT'),
                                    row.get('FTM'), row.get('FTA'), row.get('FT_PCT'),
                                    row.get('OREB'), row.get('DREB'), row.get('REB'),
                                    row.get('AST'), row.get('TOV'), row.get('STL'),
                                    row.get('BLK'), row.get('BLKA'), row.get('PF'), row.get('PFD'),
                                    row.get('PTS'), row.get('PLUS_MINUS'), row.get('NBA_FANTASY_PTS'),
                                    row.get('DD2'), row.get('TD3')
                                ))
                                new_records += 1

                    if new_records > 0:
                        self.conn.commit()
                        self.logger.info(f"Added {new_records} matchup records for {team_name} vs {vs_player_name} ({season})")
                    else:
                        self.logger.debug(f"No new matchup data found for {team_name} vs {vs_player_name} ({season})")

            except Exception as e:
                self.logger.error(f"Error collecting matchup for {team_name} vs {vs_player_name}: {e}")
                continue

        self.logger.info("Team vs player matchup analytics collection completed")

    def collect_team_game_streaks(self):
        """Collect team game streak data (winning/losing streaks and patterns)"""
        self.logger.info("Collecting team game streak data...")

        for season in self.seasons:
            try:
                existing_count = self.conn.execute("SELECT COUNT(*) FROM team_game_streaks WHERE last_season = ?", (season,)).fetchone()[0]
                if existing_count > 0:
                    self.logger.info(f"Team game streaks for season {season} already exists ({existing_count} records), skipping")
                    continue

                # Collect different types of streaks
                streak_types = [
                    {'name': 'WinningStreaks', 'params': {'wstreak_nullable': '3'}},  # 3+ game winning streaks
                    {'name': 'LosingStreaks', 'params': {'lstreak_nullable': '3'}},   # 3+ game losing streaks
                    {'name': 'ActiveStreaks', 'params': {'active_streaks_only_nullable': 'Y'}},  # Current active streaks
                    {'name': 'HighScoringStreaks', 'params': {'gt_pts_nullable': '80'}},  # Games scoring 80+ points
                    {'name': 'LowScoringStreaks', 'params': {'lt_pts_nullable': '70'}},  # Games scoring under 70 points
                ]

                total_new_records = 0

                for streak_config in streak_types:
                    streak_type = streak_config['name']
                    params = streak_config['params']

                    try:
                        self.logger.info(f"Collecting {streak_type} for season {season}")

                        # Use TeamGameStreakFinder endpoint
                        streaks = self.safe_api_call(
                            teamgamestreakfinder.TeamGameStreakFinder,
                            season_nullable=season,
                            season_type_nullable='Regular Season',
                            league_id_nullable=self.wnba_league_id,
                            **params
                        )

                        if streaks:
                            df = streaks.get_data_frames()[0]
                            new_records = 0

                            for _, row in df.iterrows():
                                team_id = row.get('TEAM_ID')
                                game_streak = row.get('GAMESTREAK')
                                start_date = row.get('STARTDATE')
                                end_date = row.get('ENDDATE')

                                if team_id and game_streak and not self.check_existing_data(
                                    'team_game_streaks',
                                    team_id=team_id,
                                    game_streak=game_streak,
                                    start_date=start_date,
                                    end_date=end_date
                                ):
                                    self.conn.execute('''
                                        INSERT INTO team_game_streaks
                                        (team_id, team_name, abbreviation, game_streak, start_date, end_date,
                                         active_streak, num_seasons, last_season, first_season, streak_type)
                                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                                    ''', (
                                        team_id, row.get('TEAM_NAME'), row.get('ABBREVIATION'),
                                        game_streak, start_date, end_date,
                                        row.get('ACTIVESTREAK'), row.get('NUMSEASONS'),
                                        row.get('LASTSEASON'), row.get('FIRSTSEASON'), streak_type
                                    ))
                                    new_records += 1

                            if new_records > 0:
                                self.conn.commit()
                                self.logger.info(f"Added {new_records} {streak_type} records for season {season}")
                                total_new_records += new_records
                            else:
                                self.logger.debug(f"No new {streak_type} found for season {season}")

                    except Exception as e:
                        self.logger.error(f"Error collecting {streak_type} for season {season}: {e}")
                        continue

                if total_new_records > 0:
                    self.logger.info(f"Total streak records added for season {season}: {total_new_records}")

            except Exception as e:
                self.logger.error(f"Error collecting team game streaks for season {season}: {e}")

        self.logger.info("Team game streak collection completed")

    def collect_synergy_play_types(self):
        """Collect Synergy play type analytics (offensive and defensive play patterns)"""
        self.logger.info("Collecting Synergy play type analytics...")

        for season in self.seasons:
            try:
                existing_count = self.conn.execute("SELECT COUNT(*) FROM synergy_play_types WHERE season_id = ?", (season,)).fetchone()[0]
                if existing_count > 0:
                    self.logger.info(f"Synergy play types for season {season} already exists ({existing_count} records), skipping")
                    continue

                # Collect both team offensive and defensive play types
                play_type_configs = [
                    {'player_or_team': 'T', 'type_grouping': 'offensive', 'description': 'Team Offensive'},
                    {'player_or_team': 'T', 'type_grouping': 'defensive', 'description': 'Team Defensive'},
                ]

                total_new_records = 0

                for config in play_type_configs:
                    player_or_team = config['player_or_team']
                    type_grouping = config['type_grouping']
                    description = config['description']

                    try:
                        self.logger.info(f"Collecting {description} play types for season {season}")

                        # Use SynergyPlayTypes endpoint
                        play_types = self.safe_api_call(
                            synergyplaytypes.SynergyPlayTypes,
                            league_id=self.wnba_league_id,
                            season=season,
                            season_type_all_star='Regular Season',
                            player_or_team_abbreviation=player_or_team,
                            type_grouping_nullable=type_grouping
                        )

                        if play_types:
                            df = play_types.get_data_frames()[0]
                            new_records = 0

                            for _, row in df.iterrows():
                                season_id = row.get('SEASON_ID')
                                team_id = row.get('TEAM_ID')
                                play_type = row.get('PLAY_TYPE')
                                type_group = row.get('TYPE_GROUPING')

                                if season_id and team_id and play_type and not self.check_existing_data(
                                    'synergy_play_types',
                                    season_id=season_id,
                                    team_id=team_id,
                                    play_type=play_type,
                                    type_grouping=type_group,
                                    player_or_team=player_or_team
                                ):
                                    self.conn.execute('''
                                        INSERT INTO synergy_play_types
                                        (season_id, team_id, team_abbreviation, team_name, play_type, type_grouping,
                                         percentile, gp, poss_pct, ppp, fg_pct, ft_poss_pct, tov_poss_pct, sf_poss_pct,
                                         plusone_poss_pct, score_poss_pct, efg_pct, poss, pts, fgm, fga, fgmx, player_or_team)
                                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                                    ''', (
                                        season_id, team_id, row.get('TEAM_ABBREVIATION'), row.get('TEAM_NAME'),
                                        play_type, type_group, row.get('PERCENTILE'), row.get('GP'),
                                        row.get('POSS_PCT'), row.get('PPP'), row.get('FG_PCT'),
                                        row.get('FT_POSS_PCT'), row.get('TOV_POSS_PCT'), row.get('SF_POSS_PCT'),
                                        row.get('PLUSONE_POSS_PCT'), row.get('SCORE_POSS_PCT'), row.get('EFG_PCT'),
                                        row.get('POSS'), row.get('PTS'), row.get('FGM'), row.get('FGA'),
                                        row.get('FGMX'), player_or_team
                                    ))
                                    new_records += 1

                            if new_records > 0:
                                self.conn.commit()
                                self.logger.info(f"Added {new_records} {description} play type records for season {season}")
                                total_new_records += new_records
                            else:
                                self.logger.debug(f"No new {description} play types found for season {season}")

                    except Exception as e:
                        self.logger.error(f"Error collecting {description} play types for season {season}: {e}")
                        continue

                if total_new_records > 0:
                    self.logger.info(f"Total Synergy play type records added for season {season}: {total_new_records}")

            except Exception as e:
                self.logger.error(f"Error collecting Synergy play types for season {season}: {e}")

        self.logger.info("Synergy play type analytics collection completed")

    def collect_playoff_picture(self):
        """Collect playoff picture data including standings, remaining games, and playoff scenarios"""
        self.logger.info("Collecting playoff picture data...")

        for season in self.seasons:
            try:
                existing_count = self.conn.execute("SELECT COUNT(*) FROM playoff_picture WHERE season_id = ?", (season,)).fetchone()[0]
                if existing_count > 0:
                    self.logger.info(f"Playoff picture for season {season} already exists ({existing_count} records), skipping")
                    continue

                # Convert season to season_id format (e.g., "2024" -> "22024")
                season_id = f"2{season}"

                # Use PlayoffPicture endpoint
                playoff_data = self.safe_api_call(
                    playoffpicture.PlayoffPicture,
                    league_id=self.wnba_league_id,
                    season_id=season_id
                )

                if playoff_data:
                    data_frames = playoff_data.get_data_frames()
                    new_records = 0

                    # Map dataframe indices to data types
                    data_mapping = [
                        {'df_idx': 0, 'data_type': 'EastPlayoffPicture', 'conference': 'East'},
                        {'df_idx': 1, 'data_type': 'EastRemainingGames', 'conference': 'East'},
                        {'df_idx': 2, 'data_type': 'EastStandings', 'conference': 'East'},
                        {'df_idx': 3, 'data_type': 'WestPlayoffPicture', 'conference': 'West'},
                        {'df_idx': 4, 'data_type': 'WestRemainingGames', 'conference': 'West'},
                        {'df_idx': 5, 'data_type': 'WestStandings', 'conference': 'West'},
                    ]

                    for mapping in data_mapping:
                        df_idx = mapping['df_idx']
                        data_type = mapping['data_type']
                        conference = mapping['conference']

                        if df_idx < len(data_frames):
                            df = data_frames[df_idx]

                            for _, row in df.iterrows():
                                team_id = row.get('TEAM_ID') or row.get('HIGH_SEED_TEAM_ID') or row.get('LOW_SEED_TEAM_ID')

                                if team_id and not self.check_existing_data(
                                    'playoff_picture',
                                    season_id=season,
                                    conference=conference,
                                    data_type=data_type,
                                    team_id=team_id
                                ):
                                    # Extract data based on data type
                                    if 'Standings' in data_type:
                                        # Standings data
                                        self.conn.execute('''
                                            INSERT INTO playoff_picture
                                            (season_id, conference, data_type, team_id, team_name, team_slug, rank_position,
                                             wins, losses, win_pct, games_back, clinched_playoffs, clinched_conference,
                                             clinched_division, clinched_play_in, eliminated_playoffs)
                                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                                        ''', (
                                            season, conference, data_type, team_id,
                                            row.get('TEAM'), row.get('TEAM_SLUG'), row.get('RANK'),
                                            row.get('WINS'), row.get('LOSSES'), row.get('PCT'), row.get('GB'),
                                            row.get('CLINCHED_PLAYOFFS'), row.get('CLINCHED_CONFERENCE'),
                                            row.get('CLINCHED_DIVISION'), row.get('Clinched_Play_In'),
                                            row.get('ELIMINATED_PLAYOFFS')
                                        ))
                                    elif 'RemainingGames' in data_type:
                                        # Remaining games data
                                        self.conn.execute('''
                                            INSERT INTO playoff_picture
                                            (season_id, conference, data_type, team_id, team_name, remaining_games,
                                             remaining_home_games, remaining_away_games)
                                            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                                        ''', (
                                            season, conference, data_type, team_id,
                                            row.get('TEAM'), row.get('REMAINING_G'),
                                            row.get('REMAINING_HOME_G'), row.get('REMAINING_AWAY_G')
                                        ))
                                    elif 'PlayoffPicture' in data_type:
                                        # Playoff matchup data
                                        high_seed_team_id = row.get('HIGH_SEED_TEAM_ID')
                                        low_seed_team_id = row.get('LOW_SEED_TEAM_ID')

                                        # Insert for high seed team
                                        if high_seed_team_id:
                                            self.conn.execute('''
                                                INSERT INTO playoff_picture
                                                (season_id, conference, data_type, team_id, team_name, rank_position,
                                                 high_seed_rank, low_seed_rank, series_wins, series_losses)
                                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                                            ''', (
                                                season, conference, data_type, high_seed_team_id,
                                                row.get('HIGH_SEED_TEAM'), row.get('HIGH_SEED_RANK'),
                                                row.get('HIGH_SEED_RANK'), row.get('LOW_SEED_RANK'),
                                                row.get('HIGH_SEED_SERIES_W'), row.get('HIGH_SEED_SERIES_L')
                                            ))

                                        # Insert for low seed team
                                        if low_seed_team_id and low_seed_team_id != high_seed_team_id:
                                            self.conn.execute('''
                                                INSERT INTO playoff_picture
                                                (season_id, conference, data_type, team_id, team_name, rank_position,
                                                 high_seed_rank, low_seed_rank, series_wins, series_losses)
                                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                                            ''', (
                                                season, conference, data_type, low_seed_team_id,
                                                row.get('LOW_SEED_TEAM'), row.get('LOW_SEED_RANK'),
                                                row.get('HIGH_SEED_RANK'), row.get('LOW_SEED_RANK'),
                                                row.get('HIGH_SEED_SERIES_L'), row.get('HIGH_SEED_SERIES_W')  # Reversed for low seed
                                            ))

                                    new_records += 1

                    if new_records > 0:
                        self.conn.commit()
                        self.logger.info(f"Added {new_records} playoff picture records for season {season}")
                    else:
                        self.logger.debug(f"No new playoff picture data found for season {season}")

            except Exception as e:
                self.logger.error(f"Error collecting playoff picture for season {season}: {e}")

        self.logger.info("Playoff picture collection completed")

    def collect_league_lineup_viz(self):
        """Collect advanced lineup visualization analytics with efficiency metrics"""
        self.logger.info("Collecting league lineup visualization analytics...")

        for season in self.seasons:
            try:
                existing_count = self.conn.execute("SELECT COUNT(*) FROM league_lineup_viz WHERE season = ?", (season,)).fetchone()[0]
                if existing_count > 0:
                    self.logger.info(f"League lineup viz for season {season} already exists ({existing_count} records), skipping")
                    continue

                # Collect lineup analytics with different minimum minutes thresholds
                minutes_thresholds = [10, 25, 50]  # Different minimum minutes to capture various lineup usage levels

                total_new_records = 0

                for min_minutes in minutes_thresholds:
                    try:
                        self.logger.info(f"Collecting lineup viz with {min_minutes}+ minutes for season {season}")

                        # Use LeagueLineupViz endpoint
                        lineup_viz = self.safe_api_call(
                            leaguelineupviz.LeagueLineupViz,
                            minutes_min=min_minutes,
                            season=season,
                            season_type_all_star='Regular Season',
                            league_id_nullable=self.wnba_league_id,
                            group_quantity=5  # 5-man lineups
                        )

                        if lineup_viz:
                            df = lineup_viz.get_data_frames()[0]
                            new_records = 0

                            for _, row in df.iterrows():
                                group_id = row.get('GROUP_ID')
                                team_id = row.get('TEAM_ID')

                                if group_id and team_id and not self.check_existing_data(
                                    'league_lineup_viz',
                                    season=season,
                                    group_id=group_id,
                                    team_id=team_id
                                ):
                                    self.conn.execute('''
                                        INSERT INTO league_lineup_viz
                                        (season, group_id, group_name, team_id, team_abbreviation, minutes,
                                         off_rating, def_rating, net_rating, pace, ts_pct, fta_rate, tm_ast_pct,
                                         pct_fga_2pt, pct_fga_3pt, pct_pts_2pt_mr, pct_pts_fb, pct_pts_ft, pct_pts_paint,
                                         pct_ast_fgm, pct_uast_fgm, opp_fg3_pct, opp_efg_pct, opp_fta_rate, opp_tov_pct)
                                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                                    ''', (
                                        season, group_id, row.get('GROUP_NAME'), team_id, row.get('TEAM_ABBREVIATION'),
                                        row.get('MIN'), row.get('OFF_RATING'), row.get('DEF_RATING'), row.get('NET_RATING'),
                                        row.get('PACE'), row.get('TS_PCT'), row.get('FTA_RATE'), row.get('TM_AST_PCT'),
                                        row.get('PCT_FGA_2PT'), row.get('PCT_FGA_3PT'), row.get('PCT_PTS_2PT_MR'),
                                        row.get('PCT_PTS_FB'), row.get('PCT_PTS_FT'), row.get('PCT_PTS_PAINT'),
                                        row.get('PCT_AST_FGM'), row.get('PCT_UAST_FGM'), row.get('OPP_FG3_PCT'),
                                        row.get('OPP_EFG_PCT'), row.get('OPP_FTA_RATE'), row.get('OPP_TOV_PCT')
                                    ))
                                    new_records += 1

                            if new_records > 0:
                                self.conn.commit()
                                self.logger.info(f"Added {new_records} lineup viz records ({min_minutes}+ min) for season {season}")
                                total_new_records += new_records
                            else:
                                self.logger.debug(f"No new lineup viz data found ({min_minutes}+ min) for season {season}")

                    except Exception as e:
                        self.logger.error(f"Error collecting lineup viz ({min_minutes}+ min) for season {season}: {e}")
                        continue

                if total_new_records > 0:
                    self.logger.info(f"Total lineup viz records added for season {season}: {total_new_records}")

            except Exception as e:
                self.logger.error(f"Error collecting league lineup viz for season {season}: {e}")

        self.logger.info("League lineup visualization collection completed")

    def collect_league_game_log(self):
        """Collect comprehensive league-wide game log data for all teams"""
        self.logger.info("Collecting league game log data...")

        for season in self.seasons:
            try:
                existing_count = self.conn.execute("SELECT COUNT(*) FROM league_game_log WHERE season_id = ?", (season,)).fetchone()[0]
                if existing_count > 0:
                    self.logger.info(f"League game log for season {season} already exists ({existing_count} records), skipping")
                    continue

                # Use LeagueGameLog endpoint to get all team game logs at once
                league_log = self.safe_api_call(
                    leaguegamelog.LeagueGameLog,
                    league_id=self.wnba_league_id,
                    season=season,
                    season_type_all_star='Regular Season',
                    player_or_team_abbreviation='T'  # Team logs
                )

                if league_log:
                    df = league_log.get_data_frames()[0]
                    new_records = 0

                    self.logger.info(f"Processing {len(df)} league game log records for season {season}")

                    for _, row in df.iterrows():
                        season_id = row.get('SEASON_ID')
                        team_id = row.get('TEAM_ID')
                        game_id = row.get('GAME_ID')

                        if season_id and team_id and game_id and not self.check_existing_data(
                            'league_game_log',
                            season_id=season_id,
                            team_id=team_id,
                            game_id=game_id
                        ):
                            self.conn.execute('''
                                INSERT INTO league_game_log
                                (season_id, team_id, team_abbreviation, team_name, game_id, game_date, matchup, wl,
                                 min, fgm, fga, fg_pct, fg3m, fg3a, fg3_pct, ftm, fta, ft_pct,
                                 oreb, dreb, reb, ast, stl, blk, tov, pf, pts, plus_minus, video_available)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                            ''', (
                                season_id, team_id, row.get('TEAM_ABBREVIATION'), row.get('TEAM_NAME'),
                                game_id, row.get('GAME_DATE'), row.get('MATCHUP'), row.get('WL'),
                                row.get('MIN'), row.get('FGM'), row.get('FGA'), row.get('FG_PCT'),
                                row.get('FG3M'), row.get('FG3A'), row.get('FG3_PCT'),
                                row.get('FTM'), row.get('FTA'), row.get('FT_PCT'),
                                row.get('OREB'), row.get('DREB'), row.get('REB'),
                                row.get('AST'), row.get('STL'), row.get('BLK'),
                                row.get('TOV'), row.get('PF'), row.get('PTS'),
                                row.get('PLUS_MINUS'), row.get('VIDEO_AVAILABLE')
                            ))
                            new_records += 1

                    if new_records > 0:
                        self.conn.commit()
                        self.logger.info(f"Added {new_records} league game log records for season {season}")

                        # Show some statistics about the collected data
                        cursor = self.conn.execute("""
                            SELECT COUNT(DISTINCT team_id) as teams, COUNT(DISTINCT game_id) as games
                            FROM league_game_log WHERE season_id = ?
                        """, (season_id,))
                        teams, games = cursor.fetchone()
                        self.logger.info(f"Season {season} summary: {teams} teams, {games} unique games")
                    else:
                        self.logger.debug(f"No new league game log data found for season {season}")

            except Exception as e:
                self.logger.error(f"Error collecting league game log for season {season}: {e}")

        self.logger.info("League game log collection completed")

    def collect_league_dash_lineups(self):
        """Collect comprehensive league lineup statistics and rankings"""
        self.logger.info("Collecting league dash lineups...")

        for season in self.seasons:
            try:
                existing_count = self.conn.execute("SELECT COUNT(*) FROM league_dash_lineups WHERE season = ?", (season,)).fetchone()[0]
                if existing_count > 0:
                    self.logger.info(f"League dash lineups for season {season} already exists ({existing_count} records), skipping")
                    continue

                # Use LeagueDashLineups endpoint
                lineups = self.safe_api_call(
                    leaguedashlineups.LeagueDashLineups,
                    season=season,
                    season_type_all_star='Regular Season',
                    league_id_nullable=self.wnba_league_id,
                    group_quantity=5  # 5-man lineups
                )

                if lineups:
                    df = lineups.get_data_frames()[0]
                    new_records = 0

                    for _, row in df.iterrows():
                        group_id = row.get('GROUP_ID')
                        team_id = row.get('TEAM_ID')

                        if group_id and team_id and not self.check_existing_data(
                            'league_dash_lineups',
                            season=season,
                            group_id=group_id,
                            team_id=team_id
                        ):
                            self.conn.execute('''
                                INSERT INTO league_dash_lineups
                                (season, group_set, group_id, group_name, team_id, team_abbreviation,
                                 gp, w, l, w_pct, min, fgm, fga, fg_pct, fg3m, fg3a, fg3_pct,
                                 ftm, fta, ft_pct, oreb, dreb, reb, ast, tov, stl, blk, blka, pf, pfd, pts, plus_minus)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                            ''', (
                                season, row.get('GROUP_SET'), group_id, row.get('GROUP_NAME'),
                                team_id, row.get('TEAM_ABBREVIATION'), row.get('GP'), row.get('W'), row.get('L'),
                                row.get('W_PCT'), row.get('MIN'), row.get('FGM'), row.get('FGA'), row.get('FG_PCT'),
                                row.get('FG3M'), row.get('FG3A'), row.get('FG3_PCT'), row.get('FTM'), row.get('FTA'),
                                row.get('FT_PCT'), row.get('OREB'), row.get('DREB'), row.get('REB'), row.get('AST'),
                                row.get('TOV'), row.get('STL'), row.get('BLK'), row.get('BLKA'), row.get('PF'),
                                row.get('PFD'), row.get('PTS'), row.get('PLUS_MINUS')
                            ))
                            new_records += 1

                    if new_records > 0:
                        self.conn.commit()
                        self.logger.info(f"Added {new_records} league dash lineup records for season {season}")
                    else:
                        self.logger.debug(f"No new league dash lineup data found for season {season}")

            except Exception as e:
                self.logger.error(f"Error collecting league dash lineups for season {season}: {e}")

        self.logger.info("League dash lineups collection completed")

    def collect_league_dash_opp_pt_shot(self):
        """Collect opponent shot tracking and defensive analytics"""
        self.logger.info("Collecting league dash opponent shot tracking...")

        for season in self.seasons:
            try:
                existing_count = self.conn.execute("SELECT COUNT(*) FROM league_dash_opp_pt_shot WHERE season = ?", (season,)).fetchone()[0]
                if existing_count > 0:
                    self.logger.info(f"League dash opponent shot tracking for season {season} already exists ({existing_count} records), skipping")
                    continue

                # Different shot categories to collect
                shot_categories = [
                    {'general_range': 'Overall', 'description': 'Overall'},
                    {'close_def_dist_range': '0-2 Feet - Very Tight', 'description': 'VeryTight'},
                    {'close_def_dist_range': '2-4 Feet - Tight', 'description': 'Tight'},
                    {'close_def_dist_range': '4-6 Feet - Open', 'description': 'Open'},
                    {'close_def_dist_range': '6+ Feet - Wide Open', 'description': 'WideOpen'},
                    {'shot_dist_range': '5ft Range', 'description': '5ftRange'},
                    {'shot_dist_range': '8ft Range', 'description': '8ftRange'},
                    {'dribble_range': '0 Dribbles', 'description': '0Dribbles'},
                    {'dribble_range': '1 Dribble', 'description': '1Dribble'},
                    {'dribble_range': '2 Dribbles', 'description': '2Dribbles'},
                    {'dribble_range': '3-6 Dribbles', 'description': '3-6Dribbles'},
                    {'dribble_range': '7+ Dribbles', 'description': '7+Dribbles'},
                ]

                total_new_records = 0

                for category in shot_categories:
                    description = category['description']
                    params = {k: v for k, v in category.items() if k != 'description'}

                    try:
                        self.logger.info(f"Collecting {description} opponent shot data for season {season}")

                        # Use LeagueDashOppPtShot endpoint
                        opp_shots = self.safe_api_call(
                            leaguedashoppptshot.LeagueDashOppPtShot,
                            league_id=self.wnba_league_id,
                            season=season,
                            season_type_all_star='Regular Season',
                            **params
                        )

                        if opp_shots:
                            df = opp_shots.get_data_frames()[0]
                            new_records = 0

                            for _, row in df.iterrows():
                                team_id = row.get('TEAM_ID')

                                if team_id and not self.check_existing_data(
                                    'league_dash_opp_pt_shot',
                                    season=season,
                                    team_id=team_id,
                                    shot_category=description
                                ):
                                    self.conn.execute('''
                                        INSERT INTO league_dash_opp_pt_shot
                                        (season, team_id, team_name, team_abbreviation, gp, g, fga_frequency,
                                         fgm, fga, fg_pct, efg_pct, fg2a_frequency, fg2m, fg2a, fg2_pct,
                                         fg3a_frequency, fg3m, fg3a, fg3_pct, shot_category)
                                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                                    ''', (
                                        season, team_id, row.get('TEAM_NAME'), row.get('TEAM_ABBREVIATION'),
                                        row.get('GP'), row.get('G'), row.get('FGA_FREQUENCY'),
                                        row.get('FGM'), row.get('FGA'), row.get('FG_PCT'), row.get('EFG_PCT'),
                                        row.get('FG2A_FREQUENCY'), row.get('FG2M'), row.get('FG2A'), row.get('FG2_PCT'),
                                        row.get('FG3A_FREQUENCY'), row.get('FG3M'), row.get('FG3A'), row.get('FG3_PCT'),
                                        description
                                    ))
                                    new_records += 1

                            if new_records > 0:
                                self.conn.commit()
                                self.logger.info(f"Added {new_records} {description} opponent shot records for season {season}")
                                total_new_records += new_records

                    except Exception as e:
                        self.logger.error(f"Error collecting {description} opponent shots for season {season}: {e}")
                        continue

                if total_new_records > 0:
                    self.logger.info(f"Total opponent shot tracking records added for season {season}: {total_new_records}")

            except Exception as e:
                self.logger.error(f"Error collecting league dash opponent shot tracking for season {season}: {e}")

        self.logger.info("League dash opponent shot tracking collection completed")

    def collect_league_dash_player_clutch(self):
        """Collect clutch time player performance analytics"""
        self.logger.info("Collecting league dash player clutch performance...")

        for season in self.seasons:
            try:
                existing_count = self.conn.execute("SELECT COUNT(*) FROM league_dash_player_clutch WHERE season = ?", (season,)).fetchone()[0]
                if existing_count > 0:
                    self.logger.info(f"League dash player clutch for season {season} already exists ({existing_count} records), skipping")
                    continue

                # Different clutch scenarios to collect
                clutch_scenarios = [
                    {'clutch_time': 'Last 5 Minutes', 'point_diff': 5, 'description': 'Last5Min_Within5'},
                    {'clutch_time': 'Last 3 Minutes', 'point_diff': 5, 'description': 'Last3Min_Within5'},
                    {'clutch_time': 'Last 1 Minute', 'point_diff': 3, 'description': 'Last1Min_Within3'},
                    {'clutch_time': 'Last 30 Seconds', 'point_diff': 3, 'description': 'Last30Sec_Within3'},
                    {'clutch_time': 'Last 10 Seconds', 'point_diff': 3, 'description': 'Last10Sec_Within3'},
                ]

                total_new_records = 0

                for scenario in clutch_scenarios:
                    description = scenario['description']
                    clutch_time = scenario['clutch_time']
                    point_diff = scenario['point_diff']

                    try:
                        self.logger.info(f"Collecting {description} clutch performance for season {season}")

                        # Use LeagueDashPlayerClutch endpoint
                        clutch_stats = self.safe_api_call(
                            leaguedashplayerclutch.LeagueDashPlayerClutch,
                            season=season,
                            season_type_all_star='Regular Season',
                            league_id_nullable=self.wnba_league_id,
                            clutch_time=clutch_time,
                            point_diff=point_diff
                        )

                        if clutch_stats:
                            df = clutch_stats.get_data_frames()[0]
                            new_records = 0

                            for _, row in df.iterrows():
                                player_id = row.get('PLAYER_ID')
                                team_id = row.get('TEAM_ID')

                                if player_id and team_id and not self.check_existing_data(
                                    'league_dash_player_clutch',
                                    season=season,
                                    player_id=player_id,
                                    team_id=team_id,
                                    clutch_scenario=description
                                ):
                                    self.conn.execute('''
                                        INSERT INTO league_dash_player_clutch
                                        (season, group_set, player_id, player_name, team_id, team_abbreviation, age,
                                         gp, w, l, w_pct, min, fgm, fga, fg_pct, fg3m, fg3a, fg3_pct,
                                         ftm, fta, ft_pct, oreb, dreb, reb, ast, tov, stl, blk, blka, pf, pfd,
                                         pts, plus_minus, nba_fantasy_pts, dd2, td3, clutch_scenario)
                                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                                    ''', (
                                        season, row.get('GROUP_SET'), player_id, row.get('PLAYER_NAME'),
                                        team_id, row.get('TEAM_ABBREVIATION'), row.get('AGE'),
                                        row.get('GP'), row.get('W'), row.get('L'), row.get('W_PCT'),
                                        row.get('MIN'), row.get('FGM'), row.get('FGA'), row.get('FG_PCT'),
                                        row.get('FG3M'), row.get('FG3A'), row.get('FG3_PCT'),
                                        row.get('FTM'), row.get('FTA'), row.get('FT_PCT'),
                                        row.get('OREB'), row.get('DREB'), row.get('REB'), row.get('AST'),
                                        row.get('TOV'), row.get('STL'), row.get('BLK'), row.get('BLKA'),
                                        row.get('PF'), row.get('PFD'), row.get('PTS'), row.get('PLUS_MINUS'),
                                        row.get('NBA_FANTASY_PTS'), row.get('DD2'), row.get('TD3'), description
                                    ))
                                    new_records += 1

                            if new_records > 0:
                                self.conn.commit()
                                self.logger.info(f"Added {new_records} {description} clutch records for season {season}")
                                total_new_records += new_records

                    except Exception as e:
                        self.logger.error(f"Error collecting {description} clutch performance for season {season}: {e}")
                        continue

                if total_new_records > 0:
                    self.logger.info(f"Total clutch performance records added for season {season}: {total_new_records}")

            except Exception as e:
                self.logger.error(f"Error collecting league dash player clutch for season {season}: {e}")

        self.logger.info("League dash player clutch collection completed")

    def collect_league_dash_player_pt_shot(self):
        """Collect detailed player shot tracking analytics"""
        self.logger.info("Collecting league dash player shot tracking...")

        for season in self.seasons:
            try:
                existing_count = self.conn.execute("SELECT COUNT(*) FROM league_dash_player_pt_shot WHERE season = ?", (season,)).fetchone()[0]
                if existing_count > 0:
                    self.logger.info(f"League dash player shot tracking for season {season} already exists ({existing_count} records), skipping")
                    continue

                # Different shot tracking categories to collect
                shot_categories = [
                    {'general_range': 'Overall', 'description': 'Overall'},
                    {'close_def_dist_range': '0-2 Feet - Very Tight', 'description': 'VeryTight'},
                    {'close_def_dist_range': '2-4 Feet - Tight', 'description': 'Tight'},
                    {'close_def_dist_range': '4-6 Feet - Open', 'description': 'Open'},
                    {'close_def_dist_range': '6+ Feet - Wide Open', 'description': 'WideOpen'},
                    {'shot_dist_range': '5ft Range', 'description': '5ftRange'},
                    {'shot_dist_range': '8ft Range', 'description': '8ftRange'},
                    {'shot_dist_range': '16ft Range', 'description': '16ftRange'},
                    {'shot_dist_range': '24ft Range', 'description': '24ftRange'},
                    {'dribble_range': '0 Dribbles', 'description': '0Dribbles'},
                    {'dribble_range': '1 Dribble', 'description': '1Dribble'},
                    {'dribble_range': '2 Dribbles', 'description': '2Dribbles'},
                    {'dribble_range': '3-6 Dribbles', 'description': '3-6Dribbles'},
                    {'dribble_range': '7+ Dribbles', 'description': '7+Dribbles'},
                    {'touch_time_range': 'Touch < 2 Seconds', 'description': 'Touch<2Sec'},
                    {'touch_time_range': 'Touch 2-6 Seconds', 'description': 'Touch2-6Sec'},
                    {'touch_time_range': 'Touch 6+ Seconds', 'description': 'Touch6+Sec'},
                    {'shot_clock_range': '24-22', 'description': 'ShotClock24-22'},
                    {'shot_clock_range': '18-15', 'description': 'ShotClock18-15'},
                    {'shot_clock_range': '7-4', 'description': 'ShotClock7-4'},
                    {'shot_clock_range': '4-0', 'description': 'ShotClock4-0'},
                ]

                total_new_records = 0

                for category in shot_categories:
                    description = category['description']
                    params = {k: v for k, v in category.items() if k != 'description'}

                    try:
                        self.logger.info(f"Collecting {description} player shot tracking for season {season}")

                        # Use LeagueDashPlayerPtShot endpoint
                        player_shots = self.safe_api_call(
                            leaguedashplayerptshot.LeagueDashPlayerPtShot,
                            league_id=self.wnba_league_id,
                            season=season,
                            season_type_all_star='Regular Season',
                            **params
                        )

                        if player_shots:
                            df = player_shots.get_data_frames()[0]
                            new_records = 0

                            for _, row in df.iterrows():
                                player_id = row.get('PLAYER_ID')

                                if player_id and not self.check_existing_data(
                                    'league_dash_player_pt_shot',
                                    season=season,
                                    player_id=player_id,
                                    shot_category=description
                                ):
                                    self.conn.execute('''
                                        INSERT INTO league_dash_player_pt_shot
                                        (season, player_id, player_name, player_last_team_id, player_last_team_abbreviation,
                                         age, gp, g, fga_frequency, fgm, fga, fg_pct, efg_pct, fg2a_frequency,
                                         fg2m, fg2a, fg2_pct, fg3a_frequency, fg3m, fg3a, fg3_pct, shot_category)
                                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                                    ''', (
                                        season, player_id, row.get('PLAYER_NAME'), row.get('PLAYER_LAST_TEAM_ID'),
                                        row.get('PLAYER_LAST_TEAM_ABBREVIATION'), row.get('AGE'), row.get('GP'), row.get('G'),
                                        row.get('FGA_FREQUENCY'), row.get('FGM'), row.get('FGA'), row.get('FG_PCT'),
                                        row.get('EFG_PCT'), row.get('FG2A_FREQUENCY'), row.get('FG2M'), row.get('FG2A'),
                                        row.get('FG2_PCT'), row.get('FG3A_FREQUENCY'), row.get('FG3M'), row.get('FG3A'),
                                        row.get('FG3_PCT'), description
                                    ))
                                    new_records += 1

                            if new_records > 0:
                                self.conn.commit()
                                self.logger.info(f"Added {new_records} {description} player shot records for season {season}")
                                total_new_records += new_records

                    except Exception as e:
                        self.logger.error(f"Error collecting {description} player shot tracking for season {season}: {e}")
                        continue

                if total_new_records > 0:
                    self.logger.info(f"Total player shot tracking records added for season {season}: {total_new_records}")

            except Exception as e:
                self.logger.error(f"Error collecting league dash player shot tracking for season {season}: {e}")

        self.logger.info("League dash player shot tracking collection completed")

    def collect_league_dash_pt_team_defend(self):
        """Collect team defensive tracking analytics"""
        self.logger.info("Collecting league dash team defensive tracking...")

        for season in self.seasons:
            try:
                existing_count = self.conn.execute("SELECT COUNT(*) FROM league_dash_pt_team_defend WHERE season = ?", (season,)).fetchone()[0]
                if existing_count > 0:
                    self.logger.info(f"League dash team defense for season {season} already exists ({existing_count} records), skipping")
                    continue

                # Different defensive categories to collect
                defense_categories = [
                    'Overall',
                    '3 Pointers',
                    '2 Pointers',
                    'Less Than 6Ft',
                    'Less Than 10Ft',
                    'Greater Than 15Ft'
                ]

                total_new_records = 0

                for defense_category in defense_categories:
                    try:
                        self.logger.info(f"Collecting {defense_category} team defense for season {season}")

                        # Use LeagueDashPtTeamDefend endpoint
                        team_defense = self.safe_api_call(
                            leaguedashptteamdefend.LeagueDashPtTeamDefend,
                            league_id=self.wnba_league_id,
                            season=season,
                            season_type_all_star='Regular Season',
                            defense_category=defense_category
                        )

                        if team_defense:
                            df = team_defense.get_data_frames()[0]
                            new_records = 0

                            for _, row in df.iterrows():
                                team_id = row.get('TEAM_ID')

                                if team_id and not self.check_existing_data(
                                    'league_dash_pt_team_defend',
                                    season=season,
                                    team_id=team_id,
                                    defense_category=defense_category
                                ):
                                    self.conn.execute('''
                                        INSERT INTO league_dash_pt_team_defend
                                        (season, team_id, team_name, team_abbreviation, gp, g, freq,
                                         d_fgm, d_fga, d_fg_pct, normal_fg_pct, pct_plusminus, defense_category)
                                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                                    ''', (
                                        season, team_id, row.get('TEAM_NAME'), row.get('TEAM_ABBREVIATION'),
                                        row.get('GP'), row.get('G'), row.get('FREQ'),
                                        row.get('D_FGM'), row.get('D_FGA'), row.get('D_FG_PCT'),
                                        row.get('NORMAL_FG_PCT'), row.get('PCT_PLUSMINUS'), defense_category
                                    ))
                                    new_records += 1

                            if new_records > 0:
                                self.conn.commit()
                                self.logger.info(f"Added {new_records} {defense_category} team defense records for season {season}")
                                total_new_records += new_records

                    except Exception as e:
                        self.logger.error(f"Error collecting {defense_category} team defense for season {season}: {e}")
                        continue

                if total_new_records > 0:
                    self.logger.info(f"Total team defense records added for season {season}: {total_new_records}")

            except Exception as e:
                self.logger.error(f"Error collecting league dash team defense for season {season}: {e}")

        self.logger.info("League dash team defensive tracking collection completed")

    def collect_boxscore_defensive_v2(self):
        """Collect game-specific defensive matchup data"""
        self.logger.info("Collecting boxscore defensive v2 data...")

        # Get unique game IDs from our game data
        try:
            cursor = self.conn.execute("""
                SELECT DISTINCT game_id
                FROM (
                    SELECT game_id FROM player_game_logs
                    UNION
                    SELECT game_id FROM team_game_logs
                    UNION
                    SELECT game_id FROM league_game_log
                )
                WHERE game_id NOT IN (SELECT DISTINCT game_id FROM boxscore_defensive_v2 WHERE game_id IS NOT NULL)
                LIMIT 15
            """)
            missing_games = [row[0] for row in cursor.fetchall()]
        except Exception:
            missing_games = []

        if not missing_games:
            self.logger.info("All boxscore defensive v2 data already collected or no games available")
            return

        self.logger.info(f"Collecting defensive v2 data for {len(missing_games)} games")

        for i, game_id in enumerate(missing_games):
            if i % 3 == 0:
                self.logger.info(f"Processing game {i+1}/{len(missing_games)}: {game_id}")

            try:
                # Use BoxScoreDefensiveV2 endpoint
                defensive_data = self.safe_api_call(
                    boxscoredefensivev2.BoxScoreDefensiveV2,
                    game_id=game_id
                )

                if defensive_data:
                    data_frames = defensive_data.get_data_frames()
                    new_records = 0

                    # Process player defensive stats
                    if len(data_frames) > 0:
                        players_df = data_frames[0]  # PlayerStats

                        for _, row in players_df.iterrows():
                            person_id = row.get('personId')
                            team_id = row.get('teamId')

                            if person_id and team_id and not self.check_existing_data(
                                'boxscore_defensive_v2',
                                game_id=game_id,
                                team_id=team_id,
                                person_id=person_id,
                                data_type='PlayerStats'
                            ):
                                self.conn.execute('''
                                    INSERT INTO boxscore_defensive_v2
                                    (game_id, team_id, team_city, team_name, team_tricode, team_slug,
                                     person_id, first_name, family_name, name_i, player_slug, position,
                                     comment, jersey_num, matchup_minutes, partial_possessions, switches_on,
                                     player_points, defensive_rebounds, matchup_assists, matchup_turnovers,
                                     steals, blocks, matchup_field_goals_made, matchup_field_goals_attempted,
                                     matchup_field_goal_percentage, matchup_three_pointers_made,
                                     matchup_three_pointers_attempted, matchup_three_pointer_percentage, data_type)
                                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                                ''', (
                                    game_id, team_id, row.get('teamCity'), row.get('teamName'),
                                    row.get('teamTricode'), row.get('teamSlug'), person_id,
                                    row.get('firstName'), row.get('familyName'), row.get('nameI'),
                                    row.get('playerSlug'), row.get('position'), row.get('comment'),
                                    row.get('jerseyNum'), row.get('matchupMinutes'), row.get('partialPossessions'),
                                    row.get('switchesOn'), row.get('playerPoints'), row.get('defensiveRebounds'),
                                    row.get('matchupAssists'), row.get('matchupTurnovers'), row.get('steals'),
                                    row.get('blocks'), row.get('matchupFieldGoalsMade'), row.get('matchupFieldGoalsAttempted'),
                                    row.get('matchupFieldGoalPercentage'), row.get('matchupThreePointersMade'),
                                    row.get('matchupThreePointersAttempted'), row.get('matchupThreePointerPercentage'),
                                    'PlayerStats'
                                ))
                                new_records += 1

                    # Process team defensive stats
                    if len(data_frames) > 1:
                        teams_df = data_frames[1]  # TeamStats

                        for _, row in teams_df.iterrows():
                            team_id = row.get('teamId')

                            if team_id and not self.check_existing_data(
                                'boxscore_defensive_v2',
                                game_id=game_id,
                                team_id=team_id,
                                person_id='TEAM',
                                data_type='TeamStats'
                            ):
                                self.conn.execute('''
                                    INSERT INTO boxscore_defensive_v2
                                    (game_id, team_id, team_city, team_name, team_tricode, team_slug,
                                     person_id, matchup_minutes, data_type)
                                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                                ''', (
                                    game_id, team_id, row.get('teamCity'), row.get('teamName'),
                                    row.get('teamTricode'), row.get('teamSlug'), 'TEAM',
                                    row.get('minutes'), 'TeamStats'
                                ))
                                new_records += 1

                    if new_records > 0:
                        self.conn.commit()
                        self.logger.info(f"Added {new_records} defensive v2 records for game {game_id}")

            except Exception as e:
                self.logger.error(f"Error collecting defensive v2 data for game {game_id}: {e}")

        self.logger.info("Boxscore defensive v2 collection completed")

    def collect_boxscore_four_factors_v2(self):
        """Collect game-specific Four Factors analytics (EFG%, FTA Rate, TOV%, OREB%)"""
        self.logger.info("Collecting boxscore four factors v2 data...")

        # Get unique game IDs from our game data
        try:
            cursor = self.conn.execute("""
                SELECT DISTINCT game_id
                FROM (
                    SELECT game_id FROM player_game_logs
                    UNION
                    SELECT game_id FROM team_game_logs
                    UNION
                    SELECT game_id FROM league_game_log
                )
                WHERE game_id NOT IN (SELECT DISTINCT game_id FROM boxscore_four_factors_v2 WHERE game_id IS NOT NULL)
                LIMIT 15
            """)
            missing_games = [row[0] for row in cursor.fetchall()]
        except Exception:
            missing_games = []

        if not missing_games:
            self.logger.info("All boxscore four factors v2 data already collected or no games available")
            return

        self.logger.info(f"Collecting four factors data for {len(missing_games)} games")

        for i, game_id in enumerate(missing_games):
            if i % 3 == 0:
                self.logger.info(f"Processing game {i+1}/{len(missing_games)}: {game_id}")

            try:
                # Use BoxScoreFourFactorsV2 endpoint
                four_factors_data = self.safe_api_call(
                    boxscorefourfactorsv2.BoxScoreFourFactorsV2,
                    game_id=game_id
                )

                if four_factors_data:
                    data_frames = four_factors_data.get_data_frames()
                    new_records = 0

                    # Process player four factors
                    if len(data_frames) > 0:
                        players_df = data_frames[0]  # sqlPlayersFourFactors

                        for _, row in players_df.iterrows():
                            player_id = row.get('PLAYER_ID')
                            team_id = row.get('TEAM_ID')

                            if player_id and team_id and not self.check_existing_data(
                                'boxscore_four_factors_v2',
                                game_id=game_id,
                                team_id=team_id,
                                player_id=player_id,
                                data_type='PlayerFourFactors'
                            ):
                                self.conn.execute('''
                                    INSERT INTO boxscore_four_factors_v2
                                    (game_id, team_id, team_abbreviation, team_city, player_id, player_name,
                                     start_position, comment, min, efg_pct, fta_rate, tm_tov_pct, oreb_pct,
                                     opp_efg_pct, opp_fta_rate, opp_tov_pct, opp_oreb_pct, data_type)
                                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                                ''', (
                                    game_id, team_id, row.get('TEAM_ABBREVIATION'), row.get('TEAM_CITY'),
                                    player_id, row.get('PLAYER_NAME'), row.get('START_POSITION'),
                                    row.get('COMMENT'), row.get('MIN'), row.get('EFG_PCT'),
                                    row.get('FTA_RATE'), row.get('TM_TOV_PCT'), row.get('OREB_PCT'),
                                    row.get('OPP_EFG_PCT'), row.get('OPP_FTA_RATE'), row.get('OPP_TOV_PCT'),
                                    row.get('OPP_OREB_PCT'), 'PlayerFourFactors'
                                ))
                                new_records += 1

                    # Process team four factors
                    if len(data_frames) > 1:
                        teams_df = data_frames[1]  # sqlTeamsFourFactors

                        for _, row in teams_df.iterrows():
                            team_id = row.get('TEAM_ID')

                            if team_id and not self.check_existing_data(
                                'boxscore_four_factors_v2',
                                game_id=game_id,
                                team_id=team_id,
                                player_id='TEAM',
                                data_type='TeamFourFactors'
                            ):
                                self.conn.execute('''
                                    INSERT INTO boxscore_four_factors_v2
                                    (game_id, team_id, team_name, team_abbreviation, team_city, player_id,
                                     min, efg_pct, fta_rate, tm_tov_pct, oreb_pct, opp_efg_pct, opp_fta_rate,
                                     opp_tov_pct, opp_oreb_pct, data_type)
                                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                                ''', (
                                    game_id, team_id, row.get('TEAM_NAME'), row.get('TEAM_ABBREVIATION'),
                                    row.get('TEAM_CITY'), 'TEAM', row.get('MIN'), row.get('EFG_PCT'),
                                    row.get('FTA_RATE'), row.get('TM_TOV_PCT'), row.get('OREB_PCT'),
                                    row.get('OPP_EFG_PCT'), row.get('OPP_FTA_RATE'), row.get('OPP_TOV_PCT'),
                                    row.get('OPP_OREB_PCT'), 'TeamFourFactors'
                                ))
                                new_records += 1

                    if new_records > 0:
                        self.conn.commit()
                        self.logger.info(f"Added {new_records} four factors records for game {game_id}")

            except Exception as e:
                self.logger.error(f"Error collecting four factors data for game {game_id}: {e}")

        self.logger.info("Boxscore four factors v2 collection completed")

    def collect_team_stats(self):
        """Collect team statistics"""
        self.logger.info("Collecting team stats...")

        missing_seasons = self.get_missing_seasons('team_stats')
        if not missing_seasons:
            self.logger.info("All team stats already collected")
            return

        self.logger.info(f"Collecting team stats for {len(missing_seasons)} missing seasons: {missing_seasons}")

        for season in missing_seasons:
            try:
                existing_count = self.conn.execute("SELECT COUNT(*) FROM team_stats WHERE season = ?", (season,)).fetchone()[0]
                if existing_count > 0:
                    self.logger.info(f"Team stats for season {season} already exists ({existing_count} records), skipping")
                    continue

                # Regular season stats (remove league_id parameter)
                team_stats = self.safe_api_call(
                    leaguedashteamstats.LeagueDashTeamStats,
                    season=season,
                    season_type_all_star='Regular Season'
                )

                if team_stats:
                    df = team_stats.get_data_frames()[0]
                    new_records = 0
                    for _, row in df.iterrows():
                        if not self.check_existing_data('team_stats', team_id=row.get('TEAM_ID'), season=season, season_type='Regular Season'):
                            self.conn.execute('''
                                INSERT INTO team_stats
                                (team_id, season, season_type, gp, w, l, w_pct, min, fgm, fga, fg_pct,
                                 fg3m, fg3a, fg3_pct, ftm, fta, ft_pct, oreb, dreb, reb, ast, tov,
                                 stl, blk, blka, pf, pfd, pts, plus_minus)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                            ''', (
                                row.get('TEAM_ID'), season, 'Regular Season',
                                row.get('GP'), row.get('W'), row.get('L'), row.get('W_PCT'),
                                row.get('MIN'), row.get('FGM'), row.get('FGA'), row.get('FG_PCT'),
                                row.get('FG3M'), row.get('FG3A'), row.get('FG3_PCT'),
                                row.get('FTM'), row.get('FTA'), row.get('FT_PCT'),
                                row.get('OREB'), row.get('DREB'), row.get('REB'),
                                row.get('AST'), row.get('TOV'), row.get('STL'),
                                row.get('BLK'), row.get('BLKA'), row.get('PF'),
                                row.get('PFD'), row.get('PTS'), row.get('PLUS_MINUS')
                            ))
                            new_records += 1

                    self.conn.commit()
                    self.logger.info(f"Added {new_records} new team stat records for season {season}")

            except Exception as e:
                self.logger.error(f"Error collecting team stats for season {season}: {e}")

    def collect_shot_chart_data(self):
        """Collect shot chart data for games"""
        self.logger.info("Collecting shot chart data...")

        # Get games that don't have shot chart data yet
        try:
            cursor = self.conn.execute("""
                SELECT DISTINCT g.game_id
                FROM games g
                LEFT JOIN shot_chart_data scd ON g.game_id = scd.game_id
                WHERE scd.game_id IS NULL AND g.game_id IS NOT NULL
                LIMIT 100
            """)
            missing_games = [row[0] for row in cursor.fetchall()]
        except Exception:
            missing_games = []

        if not missing_games:
            self.logger.info("All shot chart data already collected or no games available")
            return

        self.logger.info(f"Collecting shot chart data for {len(missing_games)} missing games")

        for i, game_id in enumerate(missing_games):
            if i % 10 == 0:
                self.logger.info(f"Processing game {i+1}/{len(missing_games)}: {game_id}")

            try:
                # Get players from the game
                players_cursor = self.conn.execute("SELECT DISTINCT player_id FROM boxscore_traditional WHERE game_id = ?", (game_id,))
                player_ids = [row[0] for row in players_cursor.fetchall()]

                for player_id in player_ids:
                    try:
                        shot_chart = self.safe_api_call(
                            shotchartdetail.ShotChartDetail,
                            team_id=0,
                            player_id=player_id,
                            game_id_nullable=game_id,
                            league_id=self.wnba_league_id
                        )

                        if shot_chart:
                            shot_data = shot_chart.get_data_frames()[0]
                            for _, row in shot_data.iterrows():
                                if not self.check_existing_data('shot_chart_data', game_id=game_id, game_event_id=row.get('GAME_EVENT_ID')):
                                    self.conn.execute('''
                                        INSERT INTO shot_chart_data
                                        (game_id, game_event_id, player_id, player_name, team_id, team_name,
                                         period, minutes_remaining, seconds_remaining, event_type, action_type,
                                         shot_type, shot_zone_basic, shot_zone_area, shot_zone_range,
                                         shot_distance, loc_x, loc_y, shot_attempted_flag, shot_made_flag,
                                         game_date, htm, vtm)
                                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                                    ''', (
                                        game_id, row.get('GAME_EVENT_ID'), row.get('PLAYER_ID'),
                                        row.get('PLAYER_NAME'), row.get('TEAM_ID'), row.get('TEAM_NAME'),
                                        row.get('PERIOD'), row.get('MINUTES_REMAINING'), row.get('SECONDS_REMAINING'),
                                        row.get('EVENT_TYPE'), row.get('ACTION_TYPE'), row.get('SHOT_TYPE'),
                                        row.get('SHOT_ZONE_BASIC'), row.get('SHOT_ZONE_AREA'), row.get('SHOT_ZONE_RANGE'),
                                        row.get('SHOT_DISTANCE'), row.get('LOC_X'), row.get('LOC_Y'),
                                        row.get('SHOT_ATTEMPTED_FLAG'), row.get('SHOT_MADE_FLAG'),
                                        row.get('GAME_DATE'), row.get('HTM'), row.get('VTM')
                                    ))
                    except Exception as e:
                        self.logger.warning(f"Error collecting shot chart for player {player_id} in game {game_id}: {e}")
                        continue

                if i % 10 == 0:
                    self.conn.commit()

            except Exception as e:
                self.logger.error(f"Error collecting shot chart for game {game_id}: {e}")

        self.conn.commit()

    def collect_tracking_stats(self):
        """Collect player tracking statistics"""
        self.logger.info("Collecting tracking stats...")

        missing_seasons = self.get_missing_seasons('tracking_stats')
        if not missing_seasons:
            self.logger.info("All tracking stats already collected")
            return

        self.logger.info(f"Collecting tracking stats for {len(missing_seasons)} missing seasons: {missing_seasons}")

        for season in missing_seasons:
            try:
                existing_count = self.conn.execute("SELECT COUNT(*) FROM tracking_stats WHERE season = ?", (season,)).fetchone()[0]
                if existing_count > 0:
                    self.logger.info(f"Tracking stats for season {season} already exists ({existing_count} records), skipping")
                    continue

                # Try different tracking stat types
                tracking_types = ['SpeedDistance', 'Rebounding', 'Possessions', 'CatchShoot', 'Defense', 'Drives', 'Passing']

                for pt_measure_type in tracking_types:
                    try:
                        tracking_stats = self.safe_api_call(
                            leaguedashptstats.LeagueDashPtStats,
                            season=season,
                            season_type_all_star='Regular Season',
                            pt_measure_type=pt_measure_type
                        )

                        if tracking_stats and pt_measure_type == 'SpeedDistance':  # Use SpeedDistance as primary
                            df = tracking_stats.get_data_frames()[0]
                            new_records = 0
                            for _, row in df.iterrows():
                                if not self.check_existing_data('tracking_stats', player_id=row.get('PLAYER_ID'), season=season):
                                    self.conn.execute('''
                                        INSERT INTO tracking_stats
                                        (player_id, team_id, season, player_name, gp, min, dist_feet, dist_miles,
                                         dist_miles_off, dist_miles_def, avg_speed, avg_speed_off, avg_speed_def)
                                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                                    ''', (
                                        row.get('PLAYER_ID'), row.get('TEAM_ID'), season,
                                        row.get('PLAYER_NAME'), row.get('GP'), row.get('MIN'),
                                        row.get('DIST_FEET'), row.get('DIST_MILES'),
                                        row.get('DIST_MILES_OFF'), row.get('DIST_MILES_DEF'),
                                        row.get('AVG_SPEED'), row.get('AVG_SPEED_OFF'), row.get('AVG_SPEED_DEF')
                                    ))
                                    new_records += 1

                            if new_records > 0:
                                self.conn.commit()
                                self.logger.info(f"Added {new_records} new tracking stat records for season {season}")
                            break  # Only need one successful tracking type

                    except Exception as e:
                        self.logger.warning(f"Error collecting {pt_measure_type} tracking stats for season {season}: {e}")
                        continue

            except Exception as e:
                self.logger.error(f"Error collecting tracking stats for season {season}: {e}")

    def get_collection_progress(self):
        """Get detailed progress report"""
        progress = {}
        
        # Check seasons coverage
        for table in ['teams', 'players', 'games', 'hustle_stats', 'player_career_stats', 'team_stats', 'tracking_stats']:
            try:
                cursor = self.conn.execute(f"SELECT DISTINCT season FROM {table}")
                existing_seasons = set(row[0] for row in cursor.fetchall())
                missing_seasons = [s for s in self.seasons if s not in existing_seasons]
                progress[table] = {
                    'total_seasons': len(self.seasons),
                    'completed_seasons': len(existing_seasons),
                    'missing_seasons': missing_seasons,
                    'completion_pct': (len(existing_seasons) / len(self.seasons)) * 100
                }
            except Exception:
                progress[table] = {
                    'total_seasons': len(self.seasons),
                    'completed_seasons': 0,
                    'missing_seasons': self.seasons,
                    'completion_pct': 0
                }
        
        # Check boxscore coverage
        try:
            total_games = self.conn.execute("SELECT COUNT(DISTINCT game_id) FROM games WHERE game_id IS NOT NULL").fetchone()[0]
            completed_games = self.conn.execute("SELECT COUNT(DISTINCT game_id) FROM boxscore_traditional").fetchone()[0]
            progress['boxscore'] = {
                'total_games': total_games,
                'completed_games': completed_games,
                'completion_pct': (completed_games / total_games * 100) if total_games > 0 else 0
            }
        except Exception:
            progress['boxscore'] = {'total_games': 0, 'completed_games': 0, 'completion_pct': 0}
            
        return progress
        
    def print_progress_report(self):
        """Print detailed progress report"""
        progress = self.get_collection_progress()
        
        print("\n" + "=" * 60)
        print("DATA COLLECTION PROGRESS REPORT")
        print("=" * 60)
        
        for table, data in progress.items():
            if table == 'boxscore':
                print(f"\n{table.upper()}:")
                print(f"  Games with boxscore data: {data['completed_games']:,} / {data['total_games']:,}")
                print(f"  Completion: {data['completion_pct']:.1f}%")
            else:
                print(f"\n{table.upper()}:")
                print(f"  Seasons completed: {data['completed_seasons']} / {data['total_seasons']}")
                print(f"  Completion: {data['completion_pct']:.1f}%")
                if data['missing_seasons']:
                    print(f"  Missing seasons: {', '.join(data['missing_seasons'])}")
        
        print("\n" + "=" * 60)

    def collect_all_data(self):
        """Collect all WNBA data in sequence"""
        self.logger.info("Starting comprehensive WNBA data collection...")

        # Collect basic data first
        self.collect_teams_data()
        self.collect_players_data()
        self.collect_schedule_and_games()

        # Collect detailed game data
        self.collect_boxscore_data()

        # Collect player-specific data
        self.collect_player_career_stats()
        self.collect_player_game_logs()  # NEW: Comprehensive player game logs
        self.collect_player_last_n_games_analytics()  # NEW: Player performance trends

        # Collect team-specific data
        self.collect_team_game_logs()    # NEW: Comprehensive team game logs
        self.collect_league_game_log()   # NEW: League-wide game log data
        self.collect_team_rosters()      # NEW: Team rosters with players and coaches

        # Collect advanced stats
        self.collect_hustle_stats()
        self.collect_team_stats()
        self.collect_defense_hub_stats()  # NEW: Defensive statistics
        self.collect_league_dash_pt_team_defend()  # NEW: Team defensive tracking
        self.collect_lineups_data()       # NEW: Lineup combinations
        self.collect_league_lineup_viz()  # NEW: Advanced lineup analytics
        self.collect_league_dash_lineups()  # NEW: League lineup rankings
        self.collect_league_dash_opp_pt_shot()  # NEW: Opponent shot tracking
        self.collect_league_dash_player_clutch()  # NEW: Clutch performance
        self.collect_league_dash_player_pt_shot()  # NEW: Player shot tracking
        self.collect_team_vs_player_matchups()  # NEW: Matchup analytics
        self.collect_team_game_streaks()  # NEW: Team streak analysis
        self.collect_synergy_play_types()  # NEW: Play type analytics
        self.collect_playoff_picture()    # NEW: Playoff standings and scenarios
        self.collect_boxscore_defensive_v2()  # NEW: Game defensive matchups
        self.collect_boxscore_four_factors_v2()  # NEW: Four Factors analytics
        self.collect_shot_chart_data()
        self.collect_tracking_stats()
        self.collect_game_rotations()     # NEW: Game rotation data

        self.logger.info("Data collection completed!")

    def export_to_csv(self):
        """Export all data to CSV files"""
        self.logger.info("Exporting data to CSV files...")

        # Create data directory if it doesn't exist
        os.makedirs(self.data_dir, exist_ok=True)

        tables = ['teams', 'players', 'games', 'boxscore_traditional',
                 'boxscore_advanced', 'player_career_stats', 'player_game_logs',
                 'player_last_n_games', 'team_game_logs', 'league_game_log', 'team_rosters',
                 'team_coaches', 'team_vs_player_matchups', 'team_game_streaks',
                 'synergy_play_types', 'playoff_picture', 'league_lineup_viz',
                 'league_dash_lineups', 'league_dash_opp_pt_shot', 'league_dash_player_clutch',
                 'league_dash_player_pt_shot', 'league_dash_pt_team_defend', 'boxscore_defensive_v2',
                 'boxscore_four_factors_v2', 'team_stats', 'defense_hub_stats', 'lineups',
                 'game_rotations', 'shot_chart_data', 'hustle_stats', 'tracking_stats']

        for table in tables:
            try:
                df = pd.read_sql_query(f"SELECT * FROM {table}", self.conn)
                if not df.empty:
                    csv_path = os.path.join(self.data_dir, f"{table}.csv")
                    df.to_csv(csv_path, index=False)
                    self.logger.info(f"Exported {len(df)} records to {csv_path}")
                else:
                    self.logger.warning(f"No data found for table {table}")
            except Exception as e:
                self.logger.error(f"Error exporting {table} to CSV: {e}")

    def export_to_json(self):
        """Export all data to JSON files"""
        self.logger.info("Exporting data to JSON files...")

        # Create data directory if it doesn't exist
        os.makedirs(self.data_dir, exist_ok=True)

        tables = ['teams', 'players', 'games', 'boxscore_traditional',
                 'boxscore_advanced', 'player_career_stats', 'team_stats',
                 'shot_chart_data', 'hustle_stats', 'tracking_stats']

        for table in tables:
            try:
                df = pd.read_sql_query(f"SELECT * FROM {table}", self.conn)
                if not df.empty:
                    json_path = os.path.join(self.data_dir, f"{table}.json")
                    df.to_json(json_path, orient='records', indent=2)
                    self.logger.info(f"Exported {len(df)} records to {json_path}")
                else:
                    self.logger.warning(f"No data found for table {table}")
            except Exception as e:
                self.logger.error(f"Error exporting {table} to JSON: {e}")

    def create_summary_report(self):
        """Create a summary report of collected data"""
        self.logger.info("Creating summary report...")

        report_path = os.path.join(self.base_dir, 'wnba_collection_summary.txt')

        with open(report_path, 'w') as f:
            f.write("WNBA Data Collection Summary Report\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"Collection Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Seasons Covered: {', '.join(self.seasons)}\n\n")

            # Get record counts for each table
            tables = ['teams', 'players', 'games', 'boxscore_traditional',
                     'boxscore_advanced', 'hustle_stats']

            for table in tables:
                try:
                    count = self.conn.execute(f"SELECT COUNT(*) FROM {table}").fetchone()[0]
                    f.write(f"{table.replace('_', ' ').title()}: {count:,} records\n")
                except Exception:
                    f.write(f"{table.replace('_', ' ').title()}: 0 records\n")

            f.write(f"\nData files saved to: {self.data_dir}\n")
            f.write(f"Database file: wnba_comprehensive.db\n")

        self.logger.info(f"Summary report saved to {report_path}")
        return report_path

    def get_collection_summary(self):
        """Get a summary of collected data"""
        summary = {}

        tables = ['teams', 'players', 'games', 'boxscore_traditional',
                 'boxscore_advanced', 'hustle_stats']

        for table in tables:
            try:
                count = self.conn.execute(f"SELECT COUNT(*) FROM {table}").fetchone()[0]
                summary[table] = count
            except Exception:
                summary[table] = 0

        return summary

    def close(self):
        """Close database connection"""
        if hasattr(self, 'conn'):
            self.conn.close()
            self.logger.info("Database connection closed")


if __name__ == "__main__":
    collector = WNBAComprehensiveCollector()
    
    try:
        print(f"Starting WNBA data collection...")
        print(f"Data will be organized in: {collector.base_dir}")
        print(f"Database: wnba_comprehensive.db")
        
        # Show initial progress
        collector.print_progress_report()
        
        print("\nStarting collection process...")
        print("-" * 60)
        
        collector.collect_all_data()
        collector.export_to_csv()
        collector.export_to_json()
        
        summary_report = collector.create_summary_report()
        summary = collector.get_collection_summary()
        
        print("\n" + "=" * 60)
        print("COLLECTION COMPLETED SUCCESSFULLY!")
        print("=" * 60)
        
        # Show final progress
        collector.print_progress_report()
        
        print(f"\nFiles organized in: {collector.base_dir}")
        print(f"Summary report: {summary_report}")
            
    except KeyboardInterrupt:
        print("\nCollection interrupted by user")
        collector.print_progress_report()
    except Exception as e:
        print(f"Collection failed: {e}")
        traceback.print_exc()
    finally:
        collector.close()

