#!/usr/bin/env python3
"""
Complete WNBA Endpoint Tester - Test ALL 150+ NBA API endpoints for WNBA compatibility
"""

from nba_api.stats.endpoints import *
import pandas as pd
import time
import json
from datetime import datetime, timedelta
import sqlite3
import logging

class WNBACompleteEndpointTester:
    def __init__(self):
        self.wnba_league_id = '10'
        self.season = '2024'
        self.test_results = {}
        self.working_endpoints = []
        self.failed_endpoints = []
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('wnba_endpoint_test.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        # Setup results database
        self.conn = sqlite3.connect('wnba_endpoint_results.db')
        self.setup_results_db()
        
        # Get sample IDs for testing
        self.sample_ids = self.get_sample_ids()
        
    def setup_results_db(self):
        """Setup database to store test results"""
        self.conn.execute('''
            CREATE TABLE IF NOT EXISTS endpoint_results (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                endpoint_name TEXT,
                status TEXT,
                total_rows INTEGER,
                dataframes INTEGER,
                parameters TEXT,
                columns TEXT,
                error_message TEXT,
                test_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        self.conn.commit()
        
    def get_sample_ids(self):
        """Get sample IDs for testing endpoints that require specific parameters"""
        sample_ids = {}
        
        try:
            # Get sample player ID
            players = commonallplayers.CommonAllPlayers(
                league_id=self.wnba_league_id,
                season=self.season,
                is_only_current_season=1
            )
            players_df = players.get_data_frames()[0]
            if len(players_df) > 0:
                sample_ids['player_id'] = players_df.iloc[0]['PERSON_ID']
                sample_ids['player_name'] = players_df.iloc[0]['DISPLAY_FIRST_LAST']
            
            # Get sample team ID from scoreboard
            test_date = datetime(2024, 5, 15)
            scoreboard = scoreboardv2.ScoreboardV2(
                league_id=self.wnba_league_id,
                game_date=test_date.strftime('%m/%d/%Y')
            )
            data_frames = scoreboard.get_data_frames()
            if len(data_frames) >= 5:
                standings_df = data_frames[4]
                if len(standings_df) > 0:
                    sample_ids['team_id'] = standings_df.iloc[0]['TEAM_ID']
                    sample_ids['team_name'] = standings_df.iloc[0]['TEAM']
            
            # Get sample game ID
            games_finder = leaguegamefinder.LeagueGameFinder()
            games_df = games_finder.get_data_frames()[0]
            wnba_games = games_df[games_df['SEASON_ID'].str.startswith('2')]
            if len(wnba_games) > 0:
                sample_ids['game_id'] = wnba_games.iloc[0]['GAME_ID']
                sample_ids['game_date'] = wnba_games.iloc[0]['GAME_DATE']
            
            self.logger.info(f"Sample IDs obtained: {sample_ids}")
            
        except Exception as e:
            self.logger.error(f"Error getting sample IDs: {e}")
            
        return sample_ids
        
    def safe_test_endpoint(self, endpoint_func, endpoint_name, **kwargs):
        """Safely test an endpoint and record results"""
        print(f"\nTesting {endpoint_name}...")
        print(f"Parameters: {kwargs}")
        
        try:
            time.sleep(0.6)  # Rate limiting
            result = endpoint_func(**kwargs)
            
            if result:
                data_frames = result.get_data_frames()
                total_rows = sum(len(df) for df in data_frames)
                
                endpoint_info = {
                    'status': 'SUCCESS',
                    'dataframes': len(data_frames),
                    'total_rows': total_rows,
                    'parameters': kwargs,
                    'columns': []
                }
                
                print(f"✅ SUCCESS! {len(data_frames)} dataframes, {total_rows} total rows")
                
                for i, df in enumerate(data_frames):
                    cols = list(df.columns) if len(df.columns) > 0 else []
                    endpoint_info['columns'].append(cols)
                    if len(df) > 0 and len(cols) > 0:
                        print(f"  DataFrame {i}: {len(df)} rows, {len(df.columns)} columns")
                        print(f"    Sample columns: {cols[:5]}{'...' if len(cols) > 5 else ''}")
                
                # Store in database
                self.conn.execute('''
                    INSERT INTO endpoint_results 
                    (endpoint_name, status, total_rows, dataframes, parameters, columns, error_message)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    endpoint_name, 'SUCCESS', total_rows, len(data_frames),
                    json.dumps(kwargs), json.dumps(endpoint_info['columns']), None
                ))
                
                self.working_endpoints.append(endpoint_name)
                self.test_results[endpoint_name] = endpoint_info
                return True
            else:
                print("❌ FAILED: No result returned")
                self.failed_endpoints.append(endpoint_name)
                self.test_results[endpoint_name] = {'status': 'FAILED', 'error': 'No result returned'}
                
                # Store in database
                self.conn.execute('''
                    INSERT INTO endpoint_results 
                    (endpoint_name, status, total_rows, dataframes, parameters, columns, error_message)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (endpoint_name, 'FAILED', 0, 0, json.dumps(kwargs), None, 'No result returned'))
                
                return False
                
        except Exception as e:
            print(f"❌ ERROR: {e}")
            self.failed_endpoints.append(endpoint_name)
            self.test_results[endpoint_name] = {'status': 'ERROR', 'error': str(e)}
            
            # Store in database
            self.conn.execute('''
                INSERT INTO endpoint_results 
                (endpoint_name, status, total_rows, dataframes, parameters, columns, error_message)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (endpoint_name, 'ERROR', 0, 0, json.dumps(kwargs), None, str(e)))
            
            return False

    def test_all_endpoints(self):
        """Test ALL available NBA API endpoints for WNBA compatibility"""
        print("🏀 TESTING ALL NBA API ENDPOINTS FOR WNBA COMPATIBILITY 🏀")
        print("=" * 80)
        
        # Define all endpoints with their parameter variations
        all_endpoints = [
            # Basic data endpoints
            (commonallplayers.CommonAllPlayers, "CommonAllPlayers", {"league_id": self.wnba_league_id, "season": self.season}),
            (commonteamyears.CommonTeamYears, "CommonTeamYears", {"league_id": self.wnba_league_id}),
            
            # Game data endpoints
            (leaguegamefinder.LeagueGameFinder, "LeagueGameFinder", {}),
            (leaguegamelog.LeagueGameLog, "LeagueGameLog", {}),
            (scoreboardv2.ScoreboardV2, "ScoreboardV2", {"league_id": self.wnba_league_id, "game_date": "05/15/2024"}),
            
            # Statistics endpoints
            (leagueleaders.LeagueLeaders, "LeagueLeaders", {"league_id": self.wnba_league_id, "season": self.season}),
            (leaguedashteamstats.LeagueDashTeamStats, "LeagueDashTeamStats", {"season": self.season, "season_type_all_star": "Regular Season"}),
            (leaguedashplayerstats.LeagueDashPlayerStats, "LeagueDashPlayerStats", {"season": self.season, "season_type_all_star": "Regular Season"}),
            
            # Clutch stats
            (leaguedashplayerclutch.LeagueDashPlayerClutch, "LeagueDashPlayerClutch", {"season": self.season, "season_type_all_star": "Regular Season"}),
            (leaguedashteamclutch.LeagueDashTeamClutch, "LeagueDashTeamClutch", {"season": self.season, "season_type_all_star": "Regular Season"}),
            
            # Advanced analytics
            (leaguedashlineups.LeagueDashLineups, "LeagueDashLineups", {"season": self.season, "season_type_all_star": "Regular Season"}),
            (leaguedashplayerbiostats.LeagueDashPlayerBioStats, "LeagueDashPlayerBioStats", {"season": self.season, "season_type_all_star": "Regular Season"}),
            (leaguedashplayershotlocations.LeagueDashPlayerShotLocations, "LeagueDashPlayerShotLocations", {"season": self.season, "season_type_all_star": "Regular Season"}),
            (leaguedashteamshotlocations.LeagueDashTeamShotLocations, "LeagueDashTeamShotLocations", {"season": self.season, "season_type_all_star": "Regular Season"}),
            
            # Player tracking
            (leaguedashptstats.LeagueDashPtStats, "LeagueDashPtStats_SpeedDistance", {"season": self.season, "season_type_all_star": "Regular Season", "pt_measure_type": "SpeedDistance"}),
            (leaguedashptstats.LeagueDashPtStats, "LeagueDashPtStats_Rebounding", {"season": self.season, "season_type_all_star": "Regular Season", "pt_measure_type": "Rebounding"}),
            (leaguedashptstats.LeagueDashPtStats, "LeagueDashPtStats_Possessions", {"season": self.season, "season_type_all_star": "Regular Season", "pt_measure_type": "Possessions"}),
            (leaguedashptstats.LeagueDashPtStats, "LeagueDashPtStats_CatchShoot", {"season": self.season, "season_type_all_star": "Regular Season", "pt_measure_type": "CatchShoot"}),
            (leaguedashptstats.LeagueDashPtStats, "LeagueDashPtStats_Defense", {"season": self.season, "season_type_all_star": "Regular Season", "pt_measure_type": "Defense"}),
            (leaguedashptstats.LeagueDashPtStats, "LeagueDashPtStats_Drives", {"season": self.season, "season_type_all_star": "Regular Season", "pt_measure_type": "Drives"}),
            (leaguedashptstats.LeagueDashPtStats, "LeagueDashPtStats_Passing", {"season": self.season, "season_type_all_star": "Regular Season", "pt_measure_type": "Passing"}),
            
            # Defense
            (leaguedashptdefend.LeagueDashPtDefend, "LeagueDashPtDefend", {"season": self.season, "season_type_all_star": "Regular Season"}),
            (leaguedashptteamdefend.LeagueDashPtTeamDefend, "LeagueDashPtTeamDefend", {"season": self.season, "season_type_all_star": "Regular Season"}),
            (defensehub.DefenseHub, "DefenseHub", {"season": self.season, "season_type_all_star": "Regular Season"}),
            
            # Hustle stats
            (leaguehustlestatsplayer.LeagueHustleStatsPlayer, "LeagueHustleStatsPlayer", {"season": self.season, "season_type_all_star": "Regular Season"}),
            (leaguehustlestatsteam.LeagueHustleStatsTeam, "LeagueHustleStatsTeam", {"season": self.season, "season_type_all_star": "Regular Season"}),
            
            # Historical data
            (alltimeleadersgrids.AllTimeLeadersGrids, "AllTimeLeadersGrids", {"league_id": self.wnba_league_id}),
            (franchisehistory.FranchiseHistory, "FranchiseHistory", {"league_id": self.wnba_league_id}),
            (franchiseleaders.FranchiseLeaders, "FranchiseLeaders", {"league_id": self.wnba_league_id}),
            
            # Leaders and assists
            (assistleaders.AssistLeaders, "AssistLeaders", {"league_id": self.wnba_league_id, "season": self.season}),
            (assisttracker.AssistTracker, "AssistTracker", {"league_id": self.wnba_league_id, "season": self.season}),
            (homepageleaders.HomePageLeaders, "HomePageLeaders", {"league_id": self.wnba_league_id, "season": self.season}),
            (leaderstiles.LeadersTiles, "LeadersTiles", {"league_id": self.wnba_league_id, "season": self.season}),
            
            # Standings
            (leaguestandings.LeagueStandings, "LeagueStandings", {"league_id": self.wnba_league_id, "season": self.season}),
            (iststandings.ISTStandings, "ISTStandings", {"league_id": self.wnba_league_id, "season": self.season}),
            
            # Matchups and season data
            (leagueseasonmatchups.LeagueSeasonMatchups, "LeagueSeasonMatchups", {"season": self.season}),
            (matchupsrollup.MatchupsRollup, "MatchupsRollup", {"league_id": self.wnba_league_id, "season": self.season}),
            (leagueplayerondetails.LeaguePlayerOnDetails, "LeaguePlayerOnDetails", {"season": self.season}),
            
            # Shot charts
            (shotchartleaguewide.ShotChartLeagueWide, "ShotChartLeagueWide", {"season": self.season}),
        ]
        
        # Add player-specific endpoints if we have a sample player
        if 'player_id' in self.sample_ids:
            player_endpoints = [
                (playercareerstats.PlayerCareerStats, "PlayerCareerStats", {"player_id": self.sample_ids['player_id']}),
                (commonplayerinfo.CommonPlayerInfo, "CommonPlayerInfo", {"player_id": self.sample_ids['player_id']}),
                (playergamelog.PlayerGameLog, "PlayerGameLog", {"player_id": self.sample_ids['player_id'], "season": self.season}),
                (playerdashboardbygeneralsplits.PlayerDashboardByGeneralSplits, "PlayerDashboardByGeneralSplits", {"player_id": self.sample_ids['player_id']}),
                (playerdashboardbyclutch.PlayerDashboardByClutch, "PlayerDashboardByClutch", {"player_id": self.sample_ids['player_id']}),
                (playerdashboardbyshootingsplits.PlayerDashboardByShootingSplits, "PlayerDashboardByShootingSplits", {"player_id": self.sample_ids['player_id']}),
                (shotchartdetail.ShotChartDetail, "ShotChartDetail", {"team_id": 0, "player_id": self.sample_ids['player_id'], "season_nullable": self.season}),
            ]
            all_endpoints.extend(player_endpoints)
        
        # Add team-specific endpoints if we have a sample team
        if 'team_id' in self.sample_ids:
            team_endpoints = [
                (teamgamelog.TeamGameLog, "TeamGameLog", {"team_id": self.sample_ids['team_id'], "season": self.season}),
                (commonteamroster.CommonTeamRoster, "CommonTeamRoster", {"team_id": self.sample_ids['team_id'], "season": self.season}),
                (teamdashboardbygeneralsplits.TeamDashboardByGeneralSplits, "TeamDashboardByGeneralSplits", {"team_id": self.sample_ids['team_id']}),
                (teamhistoricalleaders.TeamHistoricalLeaders, "TeamHistoricalLeaders", {"team_id": self.sample_ids['team_id']}),
                (teamyearbyyearstats.TeamYearByYearStats, "TeamYearByYearStats", {"team_id": self.sample_ids['team_id']}),
            ]
            all_endpoints.extend(team_endpoints)
        
        # Add game-specific endpoints if we have a sample game
        if 'game_id' in self.sample_ids:
            game_endpoints = [
                (boxscoretraditionalv2.BoxScoreTraditionalV2, "BoxScoreTraditionalV2", {"game_id": self.sample_ids['game_id']}),
                (boxscoreadvancedv2.BoxScoreAdvancedV2, "BoxScoreAdvancedV2", {"game_id": self.sample_ids['game_id']}),
                (boxscorefourfactorsv2.BoxScoreFourFactorsV2, "BoxScoreFourFactorsV2", {"game_id": self.sample_ids['game_id']}),
                (boxscoremiscv2.BoxScoreMiscV2, "BoxScoreMiscV2", {"game_id": self.sample_ids['game_id']}),
                (boxscorescoringv2.BoxScoreScoringV2, "BoxScoreScoringV2", {"game_id": self.sample_ids['game_id']}),
                (boxscoreusagev2.BoxScoreUsageV2, "BoxScoreUsageV2", {"game_id": self.sample_ids['game_id']}),
                (boxscoresummaryv2.BoxScoreSummaryV2, "BoxScoreSummaryV2", {"game_id": self.sample_ids['game_id']}),
                (playbyplayv2.PlayByPlayV2, "PlayByPlayV2", {"game_id": self.sample_ids['game_id']}),
            ]
            all_endpoints.extend(game_endpoints)
        
        # Test all endpoints
        total_endpoints = len(all_endpoints)
        self.logger.info(f"Testing {total_endpoints} endpoint configurations...")
        
        for i, (endpoint_func, name, params) in enumerate(all_endpoints):
            print(f"\n[{i+1}/{total_endpoints}] Testing {name}...")
            self.safe_test_endpoint(endpoint_func, name, **params)
            self.conn.commit()  # Save results after each test
        
        self.print_final_summary()
        self.save_working_endpoints_config()

    def print_final_summary(self):
        """Print comprehensive test summary"""
        print("\n" + "=" * 80)
        print("🎉 COMPLETE WNBA ENDPOINT TEST SUMMARY 🎉")
        print("=" * 80)
        print(f"✅ Working endpoints: {len(self.working_endpoints)}")
        print(f"❌ Failed endpoints: {len(self.failed_endpoints)}")
        print(f"📊 Total tested: {len(self.test_results)}")
        
        print("\n🟢 WORKING ENDPOINTS:")
        for endpoint in self.working_endpoints:
            result = self.test_results[endpoint]
            print(f"  • {endpoint}: {result['total_rows']} rows, {result['dataframes']} dataframes")
        
        print(f"\n🔴 FAILED ENDPOINTS: {len(self.failed_endpoints)}")
        for endpoint in self.failed_endpoints[:10]:  # Show first 10
            result = self.test_results[endpoint]
            print(f"  • {endpoint}: {result.get('error', 'Unknown error')}")
        if len(self.failed_endpoints) > 10:
            print(f"  ... and {len(self.failed_endpoints) - 10} more")

    def save_working_endpoints_config(self):
        """Save working endpoints configuration for the ultimate collector"""
        config = {
            'working_endpoints': self.working_endpoints,
            'test_results': self.test_results,
            'sample_ids': self.sample_ids,
            'test_date': datetime.now().isoformat()
        }
        
        with open('wnba_working_endpoints.json', 'w') as f:
            json.dump(config, f, indent=2, default=str)
        
        print(f"\n📁 Working endpoints configuration saved to: wnba_working_endpoints.json")
        print(f"📁 Detailed results saved to database: wnba_endpoint_results.db")

    def close(self):
        """Close database connection"""
        if hasattr(self, 'conn'):
            self.conn.close()


if __name__ == "__main__":
    tester = WNBACompleteEndpointTester()
    
    try:
        tester.test_all_endpoints()
    except KeyboardInterrupt:
        print("\nTesting interrupted by user")
    except Exception as e:
        print(f"Testing failed: {e}")
        import traceback
        traceback.print_exc()
    finally:
        tester.close()
