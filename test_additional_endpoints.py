#!/usr/bin/env python3
"""
Test additional WNBA endpoints systematically
"""

from nba_api.stats.endpoints import *
import pandas as pd
import time
from datetime import datetime

def safe_test_endpoint(endpoint_func, endpoint_name, **kwargs):
    """Safely test an endpoint and return results"""
    print(f"\nTesting {endpoint_name}...")
    print(f"Parameters: {kwargs}")
    
    try:
        time.sleep(0.6)
        result = endpoint_func(**kwargs)
        
        if result:
            data_frames = result.get_data_frames()
            total_rows = sum(len(df) for df in data_frames)
            print(f"✅ SUCCESS! {len(data_frames)} dataframes, {total_rows} total rows")
            
            if total_rows > 0:
                print(f"Sample columns: {list(data_frames[0].columns)[:5]}...")
                return True, total_rows, list(data_frames[0].columns)
            else:
                print("⚠️  SUCCESS but 0 rows")
                return True, 0, []
        else:
            print("❌ No result returned")
            return False, 0, []
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False, 0, []

def test_boxscore_endpoints():
    """Test boxscore endpoints with sample game"""
    print("\n" + "="*60)
    print("TESTING BOXSCORE ENDPOINTS")
    print("="*60)
    
    # Get a sample game ID from our working LeagueGameFinder
    try:
        games_finder = leaguegamefinder.LeagueGameFinder()
        games_df = games_finder.get_data_frames()[0]
        
        # Filter for WNBA games (league_id 10 or season_id starting with 2)
        wnba_games = games_df[games_df['SEASON_ID'].str.startswith('2')]
        if len(wnba_games) > 0:
            sample_game_id = wnba_games.iloc[0]['GAME_ID']
            print(f"Using sample WNBA game ID: {sample_game_id}")
            
            # Test various boxscore endpoints
            boxscore_endpoints = [
                (boxscoretraditionalv2.BoxScoreTraditionalV2, "BoxScoreTraditionalV2"),
                (boxscoreadvancedv2.BoxScoreAdvancedV2, "BoxScoreAdvancedV2"),
                (boxscorefourfactorsv2.BoxScoreFourFactorsV2, "BoxScoreFourFactorsV2"),
                (boxscoremiscv2.BoxScoreMiscV2, "BoxScoreMiscV2"),
                (boxscorescoringv2.BoxScoreScoringV2, "BoxScoreScoringV2"),
                (boxscoreusagev2.BoxScoreUsageV2, "BoxScoreUsageV2"),
                (boxscoresummaryv2.BoxScoreSummaryV2, "BoxScoreSummaryV2"),
                (boxscoredefensivev2.BoxScoreDefensiveV2, "BoxScoreDefensiveV2"),
                (boxscoreplayertrackv2.BoxScorePlayerTrackV2, "BoxScorePlayerTrackV2"),
            ]
            
            working_boxscore = []
            for endpoint_func, name in boxscore_endpoints:
                success, rows, cols = safe_test_endpoint(endpoint_func, name, game_id=sample_game_id)
                if success and rows > 0:
                    working_boxscore.append((name, rows, cols))
            
            print(f"\n🎉 WORKING BOXSCORE ENDPOINTS: {len(working_boxscore)}")
            for name, rows, cols in working_boxscore:
                print(f"  • {name}: {rows} rows")
            
            return working_boxscore
        else:
            print("No WNBA games found for boxscore testing")
            return []
    except Exception as e:
        print(f"Could not get sample game for boxscore testing: {e}")
        return []

def test_player_endpoints():
    """Test player-specific endpoints"""
    print("\n" + "="*60)
    print("TESTING PLAYER ENDPOINTS")
    print("="*60)
    
    # Get sample player IDs
    try:
        players = commonallplayers.CommonAllPlayers(
            league_id='10',
            season='2024',
            is_only_current_season=1
        )
        players_df = players.get_data_frames()[0]
        
        if len(players_df) > 0:
            sample_player_id = players_df.iloc[0]['PERSON_ID']
            print(f"Using sample player ID: {sample_player_id}")
            
            # Test player endpoints with various parameter combinations
            player_endpoints = [
                (playercareerstats.PlayerCareerStats, "PlayerCareerStats", {"player_id": sample_player_id}),
                (commonplayerinfo.CommonPlayerInfo, "CommonPlayerInfo", {"player_id": sample_player_id}),
                (playergamelog.PlayerGameLog, "PlayerGameLog", {"player_id": sample_player_id, "season": "2024"}),
                (playerdashboardbygeneralsplits.PlayerDashboardByGeneralSplits, "PlayerDashboardByGeneralSplits", {"player_id": sample_player_id}),
                (playerdashboardbyclutch.PlayerDashboardByClutch, "PlayerDashboardByClutch", {"player_id": sample_player_id}),
                (playerdashboardbyshootingsplits.PlayerDashboardByShootingSplits, "PlayerDashboardByShootingSplits", {"player_id": sample_player_id}),
                (playerdashboardbygamesplits.PlayerDashboardByGameSplits, "PlayerDashboardByGameSplits", {"player_id": sample_player_id}),
                (playerdashboardbylastngames.PlayerDashboardByLastNGames, "PlayerDashboardByLastNGames", {"player_id": sample_player_id}),
                (playerdashboardbyyearoveryear.PlayerDashboardByYearOverYear, "PlayerDashboardByYearOverYear", {"player_id": sample_player_id}),
                (playerdashboardbyteamperformance.PlayerDashboardByTeamPerformance, "PlayerDashboardByTeamPerformance", {"player_id": sample_player_id}),
            ]
            
            working_player = []
            for endpoint_func, name, params in player_endpoints:
                success, rows, cols = safe_test_endpoint(endpoint_func, name, **params)
                if success and rows > 0:
                    working_player.append((name, rows, cols))
            
            print(f"\n🎉 WORKING PLAYER ENDPOINTS: {len(working_player)}")
            for name, rows, cols in working_player:
                print(f"  • {name}: {rows} rows")
            
            return working_player
        else:
            print("No players found for testing")
            return []
    except Exception as e:
        print(f"Could not test player endpoints: {e}")
        return []

def test_team_endpoints():
    """Test team-specific endpoints"""
    print("\n" + "="*60)
    print("TESTING TEAM ENDPOINTS")
    print("="*60)
    
    # Get sample team ID from scoreboard
    try:
        test_date = datetime(2024, 5, 15)
        scoreboard = scoreboardv2.ScoreboardV2(
            league_id='10',
            game_date=test_date.strftime('%m/%d/%Y')
        )
        data_frames = scoreboard.get_data_frames()
        
        if len(data_frames) >= 5:
            standings_df = data_frames[4]
            if len(standings_df) > 0:
                sample_team_id = standings_df.iloc[0]['TEAM_ID']
                print(f"Using sample team ID: {sample_team_id}")
                
                # Test team endpoints
                team_endpoints = [
                    (teaminfocommon.TeamInfoCommon, "TeamInfoCommon", {"team_id": sample_team_id}),
                    (teamgamelog.TeamGameLog, "TeamGameLog", {"team_id": sample_team_id, "season": "2024"}),
                    (commonteamroster.CommonTeamRoster, "CommonTeamRoster", {"team_id": sample_team_id, "season": "2024"}),
                    (teamdashboardbygeneralsplits.TeamDashboardByGeneralSplits, "TeamDashboardByGeneralSplits", {"team_id": sample_team_id}),
                    (teamdashboardbyshootingsplits.TeamDashboardByShootingSplits, "TeamDashboardByShootingSplits", {"team_id": sample_team_id}),
                    (teamhistoricalleaders.TeamHistoricalLeaders, "TeamHistoricalLeaders", {"team_id": sample_team_id}),
                    (teamyearbyyearstats.TeamYearByYearStats, "TeamYearByYearStats", {"team_id": sample_team_id}),
                    (teamplayerdashboard.TeamPlayerDashboard, "TeamPlayerDashboard", {"team_id": sample_team_id}),
                ]
                
                working_team = []
                for endpoint_func, name, params in team_endpoints:
                    success, rows, cols = safe_test_endpoint(endpoint_func, name, **params)
                    if success and rows > 0:
                        working_team.append((name, rows, cols))
                
                print(f"\n🎉 WORKING TEAM ENDPOINTS: {len(working_team)}")
                for name, rows, cols in working_team:
                    print(f"  • {name}: {rows} rows")
                
                return working_team
            else:
                print("No teams found in standings")
                return []
        else:
            print("Insufficient data frames for team testing")
            return []
    except Exception as e:
        print(f"Could not test team endpoints: {e}")
        return []

def test_advanced_stats_endpoints():
    """Test advanced statistics endpoints"""
    print("\n" + "="*60)
    print("TESTING ADVANCED STATS ENDPOINTS")
    print("="*60)
    
    season = '2024'
    
    # Test various advanced stats endpoints
    advanced_endpoints = [
        # Clutch stats
        (leaguedashplayerclutch.LeagueDashPlayerClutch, "LeagueDashPlayerClutch", {"season": season, "season_type_all_star": "Regular Season"}),
        (leaguedashteamclutch.LeagueDashTeamClutch, "LeagueDashTeamClutch", {"season": season, "season_type_all_star": "Regular Season"}),
        
        # Lineups
        (leaguedashlineups.LeagueDashLineups, "LeagueDashLineups", {"season": season, "season_type_all_star": "Regular Season"}),
        
        # Shot locations
        (leaguedashplayershotlocations.LeagueDashPlayerShotLocations, "LeagueDashPlayerShotLocations", {"season": season, "season_type_all_star": "Regular Season"}),
        (leaguedashteamshotlocations.LeagueDashTeamShotLocations, "LeagueDashTeamShotLocations", {"season": season, "season_type_all_star": "Regular Season"}),
        
        # Bio stats
        (leaguedashplayerbiostats.LeagueDashPlayerBioStats, "LeagueDashPlayerBioStats", {"season": season, "season_type_all_star": "Regular Season"}),
        
        # Hustle stats
        (leaguehustlestatsplayer.LeagueHustleStatsPlayer, "LeagueHustleStatsPlayer", {"season": season, "season_type_all_star": "Regular Season"}),
        (leaguehustlestatsteam.LeagueHustleStatsTeam, "LeagueHustleStatsTeam", {"season": season, "season_type_all_star": "Regular Season"}),
        
        # Tracking stats
        (leaguedashptstats.LeagueDashPtStats, "LeagueDashPtStats_SpeedDistance", {"season": season, "season_type_all_star": "Regular Season", "pt_measure_type": "SpeedDistance"}),
        (leaguedashptstats.LeagueDashPtStats, "LeagueDashPtStats_Rebounding", {"season": season, "season_type_all_star": "Regular Season", "pt_measure_type": "Rebounding"}),
        (leaguedashptstats.LeagueDashPtStats, "LeagueDashPtStats_Possessions", {"season": season, "season_type_all_star": "Regular Season", "pt_measure_type": "Possessions"}),
        
        # Defense
        (leaguedashptdefend.LeagueDashPtDefend, "LeagueDashPtDefend", {"season": season, "season_type_all_star": "Regular Season"}),
        (leaguedashptteamdefend.LeagueDashPtTeamDefend, "LeagueDashPtTeamDefend", {"season": season, "season_type_all_star": "Regular Season"}),
        
        # Opponent shooting
        (leaguedashoppptshot.LeagueDashOppPtShot, "LeagueDashOppPtShot", {"season": season, "season_type_all_star": "Regular Season"}),
    ]
    
    working_advanced = []
    for endpoint_func, name, params in advanced_endpoints:
        success, rows, cols = safe_test_endpoint(endpoint_func, name, **params)
        if success and rows > 0:
            working_advanced.append((name, rows, cols))
    
    print(f"\n🎉 WORKING ADVANCED STATS ENDPOINTS: {len(working_advanced)}")
    for name, rows, cols in working_advanced:
        print(f"  • {name}: {rows} rows")
    
    return working_advanced

def test_historical_endpoints():
    """Test historical and all-time endpoints"""
    print("\n" + "="*60)
    print("TESTING HISTORICAL ENDPOINTS")
    print("="*60)
    
    # Test historical endpoints
    historical_endpoints = [
        (alltimeleadersgrids.AllTimeLeadersGrids, "AllTimeLeadersGrids", {"league_id": "10"}),
        (franchisehistory.FranchiseHistory, "FranchiseHistory", {"league_id": "10"}),
        (franchiseleaders.FranchiseLeaders, "FranchiseLeaders", {"league_id": "10"}),
        (commonteamyears.CommonTeamYears, "CommonTeamYears", {"league_id": "10"}),
    ]
    
    working_historical = []
    for endpoint_func, name, params in historical_endpoints:
        success, rows, cols = safe_test_endpoint(endpoint_func, name, **params)
        if success and rows > 0:
            working_historical.append((name, rows, cols))
    
    print(f"\n🎉 WORKING HISTORICAL ENDPOINTS: {len(working_historical)}")
    for name, rows, cols in working_historical:
        print(f"  • {name}: {rows} rows")
    
    return working_historical

def main():
    """Run comprehensive endpoint testing"""
    print("🏀 COMPREHENSIVE WNBA ENDPOINT TESTING 🏀")
    print("="*60)
    
    all_working = []
    
    # Test different categories
    all_working.extend(test_boxscore_endpoints())
    all_working.extend(test_player_endpoints())
    all_working.extend(test_team_endpoints())
    all_working.extend(test_advanced_stats_endpoints())
    all_working.extend(test_historical_endpoints())
    
    # Final summary
    print("\n" + "="*60)
    print("🎉 FINAL SUMMARY - ALL WORKING ENDPOINTS 🎉")
    print("="*60)
    print(f"Total working endpoints: {len(all_working)}")
    
    for name, rows, cols in all_working:
        print(f"✅ {name}: {rows:,} rows, {len(cols)} columns")
    
    print("\n" + "="*60)
    print("TESTING COMPLETE!")
    print("="*60)

if __name__ == "__main__":
    main()
