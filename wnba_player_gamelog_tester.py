#!/usr/bin/env python3
"""
WNBA Player Game Log Tester - Comprehensive testing of player game logs
"""

from nba_api.stats.endpoints import *
import pandas as pd
import time
import json
from datetime import datetime
import sqlite3
import logging

class WNBAPlayerGameLogTester:
    def __init__(self):
        self.wnba_league_id = '10'
        self.season = '2024'
        self.test_results = {}
        self.working_endpoints = []
        self.failed_endpoints = []
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('wnba_gamelog_test.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        # Setup results database
        self.conn = sqlite3.connect('wnba_gamelog_results.db')
        self.setup_results_db()
        
        # Get sample player IDs for testing
        self.sample_players = self.get_sample_players()
        
    def setup_results_db(self):
        """Setup database to store test results and game logs"""
        # Test results table
        self.conn.execute('''
            CREATE TABLE IF NOT EXISTS test_results (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                endpoint_name TEXT,
                player_id TEXT,
                player_name TEXT,
                status TEXT,
                total_rows INTEGER,
                error_message TEXT,
                test_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Player game logs table
        self.conn.execute('''
            CREATE TABLE IF NOT EXISTS player_game_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                player_id TEXT,
                player_name TEXT,
                season TEXT,
                game_id TEXT,
                game_date TEXT,
                matchup TEXT,
                wl TEXT,
                min INTEGER,
                pts INTEGER,
                fgm INTEGER,
                fga INTEGER,
                fg_pct REAL,
                fg3m INTEGER,
                fg3a INTEGER,
                fg3_pct REAL,
                ftm INTEGER,
                fta INTEGER,
                ft_pct REAL,
                oreb INTEGER,
                dreb INTEGER,
                reb INTEGER,
                ast INTEGER,
                stl INTEGER,
                blk INTEGER,
                tov INTEGER,
                pf INTEGER,
                plus_minus INTEGER,
                video_available INTEGER,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(player_id, game_id)
            )
        ''')
        
        self.conn.commit()
        
    def get_sample_players(self):
        """Get sample WNBA players for testing"""
        sample_players = []
        
        try:
            # Get current WNBA players
            players = commonallplayers.CommonAllPlayers(
                league_id=self.wnba_league_id,
                season=self.season,
                is_only_current_season=1
            )
            players_df = players.get_data_frames()[0]
            
            if len(players_df) > 0:
                # Get a diverse sample of players
                sample_size = min(10, len(players_df))
                sample_df = players_df.head(sample_size)
                
                for _, row in sample_df.iterrows():
                    sample_players.append({
                        'player_id': int(row['PERSON_ID']),
                        'player_name': str(row['DISPLAY_FIRST_LAST']),
                        'team_id': int(row['TEAM_ID']) if pd.notna(row['TEAM_ID']) else None
                    })
            
            self.logger.info(f"Sample players obtained: {len(sample_players)} players")
            for player in sample_players[:5]:  # Show first 5
                self.logger.info(f"  • {player['player_name']} (ID: {player['player_id']})")
            
        except Exception as e:
            self.logger.error(f"Error getting sample players: {e}")
            
        return sample_players
        
    def safe_test_endpoint(self, endpoint_func, endpoint_name, player_info, **kwargs):
        """Safely test an endpoint and record results"""
        player_id = player_info['player_id']
        player_name = player_info['player_name']
        
        print(f"\nTesting {endpoint_name} for {player_name} (ID: {player_id})...")
        print(f"Parameters: {kwargs}")
        
        try:
            time.sleep(0.6)  # Rate limiting
            result = endpoint_func(**kwargs)
            
            if result:
                data_frames = result.get_data_frames()
                total_rows = sum(len(df) for df in data_frames)
                
                print(f"✅ SUCCESS! {len(data_frames)} dataframes, {total_rows} total rows")
                
                # Store test result
                self.conn.execute('''
                    INSERT INTO test_results 
                    (endpoint_name, player_id, player_name, status, total_rows, error_message)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (endpoint_name, str(player_id), player_name, 'SUCCESS', total_rows, None))
                
                # If this is a game log, store the actual data
                if 'GameLog' in endpoint_name and total_rows > 0:
                    self.store_game_log_data(data_frames[0], player_id, player_name)
                
                self.working_endpoints.append(f"{endpoint_name}_{player_name}")
                return True, total_rows
            else:
                print("❌ FAILED: No result returned")
                self.conn.execute('''
                    INSERT INTO test_results 
                    (endpoint_name, player_id, player_name, status, total_rows, error_message)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (endpoint_name, str(player_id), player_name, 'FAILED', 0, 'No result returned'))
                
                self.failed_endpoints.append(f"{endpoint_name}_{player_name}")
                return False, 0
                
        except Exception as e:
            print(f"❌ ERROR: {e}")
            self.conn.execute('''
                INSERT INTO test_results 
                (endpoint_name, player_id, player_name, status, total_rows, error_message)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (endpoint_name, str(player_id), player_name, 'ERROR', 0, str(e)))
            
            self.failed_endpoints.append(f"{endpoint_name}_{player_name}")
            return False, 0

    def store_game_log_data(self, df, player_id, player_name):
        """Store game log data in database"""
        try:
            new_records = 0
            for _, row in df.iterrows():
                game_id = row.get('Game_ID') or row.get('GAME_ID')
                if game_id:
                    # Check if this game log already exists
                    existing = self.conn.execute(
                        "SELECT COUNT(*) FROM player_game_logs WHERE player_id = ? AND game_id = ?", 
                        (str(player_id), str(game_id))
                    ).fetchone()[0]
                    
                    if existing == 0:
                        self.conn.execute('''
                            INSERT INTO player_game_logs 
                            (player_id, player_name, season, game_id, game_date, matchup, wl, min, pts,
                             fgm, fga, fg_pct, fg3m, fg3a, fg3_pct, ftm, fta, ft_pct,
                             oreb, dreb, reb, ast, stl, blk, tov, pf, plus_minus, video_available)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (
                            str(player_id), player_name, self.season, str(game_id),
                            row.get('GAME_DATE'), row.get('MATCHUP'), row.get('WL'),
                            row.get('MIN'), row.get('PTS'), row.get('FGM'), row.get('FGA'), row.get('FG_PCT'),
                            row.get('FG3M'), row.get('FG3A'), row.get('FG3_PCT'),
                            row.get('FTM'), row.get('FTA'), row.get('FT_PCT'),
                            row.get('OREB'), row.get('DREB'), row.get('REB'),
                            row.get('AST'), row.get('STL'), row.get('BLK'),
                            row.get('TOV'), row.get('PF'), row.get('PLUS_MINUS'),
                            row.get('VIDEO_AVAILABLE', 0)
                        ))
                        new_records += 1
            
            if new_records > 0:
                self.conn.commit()
                print(f"  📊 Stored {new_records} game log records for {player_name}")
            
        except Exception as e:
            self.logger.error(f"Error storing game log data for {player_name}: {e}")

    def test_player_game_logs(self):
        """Test player game log endpoints comprehensively"""
        print("🏀 TESTING WNBA PLAYER GAME LOG ENDPOINTS 🏀")
        print("=" * 70)
        
        if not self.sample_players:
            print("❌ No sample players available for testing")
            return
        
        # Test different game log endpoints
        game_log_endpoints = [
            # Standard player game log
            (playergamelog.PlayerGameLog, "PlayerGameLog_Current", {"season": self.season}),
            (playergamelog.PlayerGameLog, "PlayerGameLog_2023", {"season": "2023"}),
            (playergamelog.PlayerGameLog, "PlayerGameLog_2022", {"season": "2022"}),
            
            # Player game logs (alternative endpoint)
            (playergamelogs.PlayerGameLogs, "PlayerGameLogs_Current", {"season_nullable": self.season}),
            (playergamelogs.PlayerGameLogs, "PlayerGameLogs_2023", {"season_nullable": "2023"}),
            
            # Player game log with season type
            (playergamelog.PlayerGameLog, "PlayerGameLog_Regular", {"season": self.season, "season_type_all_star": "Regular Season"}),
            (playergamelog.PlayerGameLog, "PlayerGameLog_Playoffs", {"season": self.season, "season_type_all_star": "Playoffs"}),
        ]
        
        total_tests = len(self.sample_players) * len(game_log_endpoints)
        current_test = 0
        
        print(f"Testing {len(game_log_endpoints)} endpoints for {len(self.sample_players)} players ({total_tests} total tests)")
        
        for player_info in self.sample_players:
            print(f"\n{'='*50}")
            print(f"Testing player: {player_info['player_name']} (ID: {player_info['player_id']})")
            print(f"{'='*50}")
            
            for endpoint_func, endpoint_name, base_params in game_log_endpoints:
                current_test += 1
                print(f"\n[{current_test}/{total_tests}] {endpoint_name}")
                
                # Add player_id to parameters
                params = base_params.copy()
                params['player_id'] = player_info['player_id']
                
                success, rows = self.safe_test_endpoint(endpoint_func, endpoint_name, player_info, **params)
                
                if success and rows > 0:
                    print(f"  🎯 Found {rows} game log entries!")
                elif success and rows == 0:
                    print(f"  ⚠️  Endpoint works but returned 0 games")
                else:
                    print(f"  ❌ Failed to get game logs")
                
                self.conn.commit()  # Save after each test
        
        self.print_game_log_summary()

    def test_additional_player_endpoints(self):
        """Test additional player-related endpoints"""
        print("\n" + "=" * 70)
        print("🔍 TESTING ADDITIONAL PLAYER ENDPOINTS")
        print("=" * 70)
        
        additional_endpoints = [
            # Player career stats
            (playercareerstats.PlayerCareerStats, "PlayerCareerStats", {}),
            
            # Player info
            (commonplayerinfo.CommonPlayerInfo, "CommonPlayerInfo", {}),
            
            # Player dashboards
            (playerdashboardbygeneralsplits.PlayerDashboardByGeneralSplits, "PlayerDashboardByGeneralSplits", {}),
            (playerdashboardbyclutch.PlayerDashboardByClutch, "PlayerDashboardByClutch", {}),
            (playerdashboardbyshootingsplits.PlayerDashboardByShootingSplits, "PlayerDashboardByShootingSplits", {}),
            (playerdashboardbygamesplits.PlayerDashboardByGameSplits, "PlayerDashboardByGameSplits", {}),
            (playerdashboardbylastngames.PlayerDashboardByLastNGames, "PlayerDashboardByLastNGames", {}),
            (playerdashboardbyteamperformance.PlayerDashboardByTeamPerformance, "PlayerDashboardByTeamPerformance", {}),
            (playerdashboardbyyearoveryear.PlayerDashboardByYearOverYear, "PlayerDashboardByYearOverYear", {}),
        ]
        
        # Test with first 3 players to avoid too many API calls
        test_players = self.sample_players[:3]
        
        for player_info in test_players:
            print(f"\nTesting additional endpoints for: {player_info['player_name']}")
            
            for endpoint_func, endpoint_name, base_params in additional_endpoints:
                params = base_params.copy()
                params['player_id'] = player_info['player_id']
                
                success, rows = self.safe_test_endpoint(endpoint_func, endpoint_name, player_info, **params)
                
                if success and rows > 0:
                    print(f"  ✅ {endpoint_name}: {rows} rows")
                elif success and rows == 0:
                    print(f"  ⚠️  {endpoint_name}: 0 rows")
                else:
                    print(f"  ❌ {endpoint_name}: Failed")

    def print_game_log_summary(self):
        """Print comprehensive game log test summary"""
        print("\n" + "=" * 80)
        print("🎉 PLAYER GAME LOG TEST SUMMARY 🎉")
        print("=" * 80)
        
        # Get summary from database
        total_tests = self.conn.execute("SELECT COUNT(*) FROM test_results").fetchone()[0]
        successful_tests = self.conn.execute("SELECT COUNT(*) FROM test_results WHERE status = 'SUCCESS'").fetchone()[0]
        failed_tests = self.conn.execute("SELECT COUNT(*) FROM test_results WHERE status != 'SUCCESS'").fetchone()[0]
        total_game_logs = self.conn.execute("SELECT COUNT(*) FROM player_game_logs").fetchone()[0]
        
        print(f"📊 OVERALL RESULTS:")
        print(f"  • Total tests: {total_tests}")
        print(f"  • Successful: {successful_tests}")
        print(f"  • Failed: {failed_tests}")
        print(f"  • Success rate: {(successful_tests/total_tests*100):.1f}%")
        print(f"  • Total game logs collected: {total_game_logs}")
        
        # Show successful endpoints with data
        print(f"\n🟢 WORKING GAME LOG ENDPOINTS:")
        cursor = self.conn.execute("""
            SELECT endpoint_name, COUNT(*) as test_count, SUM(total_rows) as total_rows
            FROM test_results 
            WHERE status = 'SUCCESS' AND total_rows > 0
            GROUP BY endpoint_name
            ORDER BY total_rows DESC
        """)
        
        for row in cursor.fetchall():
            endpoint_name, test_count, total_rows = row
            print(f"  • {endpoint_name}: {test_count} tests, {total_rows} total rows")
        
        # Show players with most game logs
        print(f"\n👤 PLAYERS WITH MOST GAME LOGS:")
        cursor = self.conn.execute("""
            SELECT player_name, COUNT(*) as game_count
            FROM player_game_logs
            GROUP BY player_id, player_name
            ORDER BY game_count DESC
            LIMIT 5
        """)
        
        for row in cursor.fetchall():
            player_name, game_count = row
            print(f"  • {player_name}: {game_count} games")
        
        # Show recent games
        print(f"\n📅 RECENT GAME LOGS:")
        cursor = self.conn.execute("""
            SELECT player_name, game_date, matchup, pts, reb, ast
            FROM player_game_logs
            ORDER BY game_date DESC
            LIMIT 5
        """)
        
        for row in cursor.fetchall():
            player_name, game_date, matchup, pts, reb, ast = row
            print(f"  • {player_name}: {game_date} {matchup} - {pts}pts, {reb}reb, {ast}ast")
        
        # Show failed endpoints
        if failed_tests > 0:
            print(f"\n🔴 FAILED ENDPOINTS:")
            cursor = self.conn.execute("""
                SELECT endpoint_name, COUNT(*) as fail_count, error_message
                FROM test_results 
                WHERE status != 'SUCCESS'
                GROUP BY endpoint_name, error_message
                ORDER BY fail_count DESC
                LIMIT 10
            """)
            
            for row in cursor.fetchall():
                endpoint_name, fail_count, error_message = row
                print(f"  • {endpoint_name}: {fail_count} failures - {error_message}")

    def export_results(self):
        """Export results to CSV files"""
        try:
            # Export test results
            test_results_df = pd.read_sql_query("SELECT * FROM test_results", self.conn)
            test_results_df.to_csv('wnba_gamelog_test_results.csv', index=False)
            print(f"\n📁 Test results exported to: wnba_gamelog_test_results.csv")
            
            # Export game logs
            game_logs_df = pd.read_sql_query("SELECT * FROM player_game_logs", self.conn)
            if not game_logs_df.empty:
                game_logs_df.to_csv('wnba_player_game_logs.csv', index=False)
                print(f"📁 Game logs exported to: wnba_player_game_logs.csv")
            
        except Exception as e:
            self.logger.error(f"Error exporting results: {e}")

    def close(self):
        """Close database connection"""
        if hasattr(self, 'conn'):
            self.conn.close()


if __name__ == "__main__":
    tester = WNBAPlayerGameLogTester()
    
    try:
        print("🏀 STARTING COMPREHENSIVE WNBA PLAYER GAME LOG TESTING 🏀")
        print("This will test multiple game log endpoints for multiple players")
        print("=" * 80)
        
        # Test game log endpoints
        tester.test_player_game_logs()
        
        # Test additional player endpoints
        tester.test_additional_player_endpoints()
        
        # Export results
        tester.export_results()
        
        print("\n" + "=" * 80)
        print("🎉 PLAYER GAME LOG TESTING COMPLETED! 🎉")
        print("=" * 80)
        
    except KeyboardInterrupt:
        print("\nTesting interrupted by user")
    except Exception as e:
        print(f"Testing failed: {e}")
        import traceback
        traceback.print_exc()
    finally:
        tester.close()
