#!/usr/bin/env python3
"""
Comprehensive test of all NBA API endpoints for WNBA data collection
"""

from nba_api.stats.endpoints import *
import pandas as pd
import time
import json
from datetime import datetime, timedelta

class WNBAEndpointTester:
    def __init__(self):
        self.wnba_league_id = '10'
        self.season = '2024'
        self.test_results = {}
        self.working_endpoints = []
        self.failed_endpoints = []
        
    def safe_test_endpoint(self, endpoint_func, endpoint_name, **kwargs):
        """Safely test an endpoint and record results"""
        print(f"\nTesting {endpoint_name}...")
        print(f"Parameters: {kwargs}")
        
        try:
            time.sleep(0.6)  # Rate limiting
            result = endpoint_func(**kwargs)
            
            if result:
                data_frames = result.get_data_frames()
                total_rows = sum(len(df) for df in data_frames)
                
                endpoint_info = {
                    'status': 'SUCCESS',
                    'dataframes': len(data_frames),
                    'total_rows': total_rows,
                    'parameters': kwargs,
                    'columns': []
                }
                
                print(f"✅ SUCCESS! {len(data_frames)} dataframes, {total_rows} total rows")
                
                for i, df in enumerate(data_frames):
                    cols = list(df.columns) if len(df.columns) > 0 else []
                    endpoint_info['columns'].append(cols)
                    print(f"  DataFrame {i}: {len(df)} rows, {len(df.columns)} columns")
                    if len(df) > 0 and len(cols) > 0:
                        print(f"    Columns: {cols[:5]}{'...' if len(cols) > 5 else ''}")
                
                self.working_endpoints.append(endpoint_name)
                self.test_results[endpoint_name] = endpoint_info
                return True
            else:
                print("❌ FAILED: No result returned")
                self.failed_endpoints.append(endpoint_name)
                self.test_results[endpoint_name] = {'status': 'FAILED', 'error': 'No result returned'}
                return False
                
        except Exception as e:
            print(f"❌ ERROR: {e}")
            self.failed_endpoints.append(endpoint_name)
            self.test_results[endpoint_name] = {'status': 'ERROR', 'error': str(e)}
            return False

    def test_basic_endpoints(self):
        """Test basic data endpoints"""
        print("\n" + "="*60)
        print("TESTING BASIC DATA ENDPOINTS")
        print("="*60)
        
        # Players
        self.safe_test_endpoint(
            commonallplayers.CommonAllPlayers,
            "CommonAllPlayers",
            league_id=self.wnba_league_id,
            season=self.season,
            is_only_current_season=0
        )
        
        # Teams/Standings
        self.safe_test_endpoint(
            leaguestandings.LeagueStandings,
            "LeagueStandings",
            league_id=self.wnba_league_id,
            season=self.season,
            season_type='Regular Season'
        )
        
        # Team info
        self.safe_test_endpoint(
            commonteamyears.CommonTeamYears,
            "CommonTeamYears",
            league_id=self.wnba_league_id
        )

    def test_schedule_endpoints(self):
        """Test schedule and game endpoints"""
        print("\n" + "="*60)
        print("TESTING SCHEDULE & GAME ENDPOINTS")
        print("="*60)
        
        # Schedule
        self.safe_test_endpoint(
            scheduleleaguev2.ScheduleLeagueV2,
            "ScheduleLeagueV2",
            league_id=self.wnba_league_id,
            season=self.season
        )
        
        # Scoreboard
        test_date = datetime(2024, 5, 15)
        self.safe_test_endpoint(
            scoreboardv2.ScoreboardV2,
            "ScoreboardV2",
            league_id=self.wnba_league_id,
            game_date=test_date.strftime('%m/%d/%Y')
        )

    def test_stats_endpoints(self):
        """Test statistics endpoints"""
        print("\n" + "="*60)
        print("TESTING STATISTICS ENDPOINTS")
        print("="*60)
        
        # Team stats
        self.safe_test_endpoint(
            leaguedashteamstats.LeagueDashTeamStats,
            "LeagueDashTeamStats",
            season=self.season,
            season_type_all_star='Regular Season'
        )
        
        # Player stats
        self.safe_test_endpoint(
            leaguedashplayerstats.LeagueDashPlayerStats,
            "LeagueDashPlayerStats",
            season=self.season,
            season_type_all_star='Regular Season'
        )
        
        # Leaders
        self.safe_test_endpoint(
            leagueleaders.LeagueLeaders,
            "LeagueLeaders",
            season=self.season,
            season_type='Regular Season'
        )

    def test_advanced_stats_endpoints(self):
        """Test advanced statistics endpoints"""
        print("\n" + "="*60)
        print("TESTING ADVANCED STATS ENDPOINTS")
        print("="*60)
        
        # Hustle stats
        self.safe_test_endpoint(
            leaguehustlestatsplayer.LeagueHustleStatsPlayer,
            "LeagueHustleStatsPlayer",
            season=self.season,
            season_type_all_star='Regular Season'
        )
        
        # Tracking stats
        tracking_types = ['SpeedDistance', 'Rebounding', 'Possessions', 'CatchShoot', 'Defense', 'Drives', 'Passing']
        for pt_type in tracking_types:
            self.safe_test_endpoint(
                leaguedashptstats.LeagueDashPtStats,
                f"LeagueDashPtStats_{pt_type}",
                season=self.season,
                season_type_all_star='Regular Season',
                pt_measure_type=pt_type
            )

    def test_boxscore_endpoints(self):
        """Test boxscore endpoints with a sample game"""
        print("\n" + "="*60)
        print("TESTING BOXSCORE ENDPOINTS")
        print("="*60)
        
        # First get a sample game ID from scoreboard
        try:
            test_date = datetime(2024, 5, 15)
            scoreboard = scoreboardv2.ScoreboardV2(
                league_id=self.wnba_league_id,
                game_date=test_date.strftime('%m/%d/%Y')
            )
            games_df = scoreboard.get_data_frames()[0]
            if len(games_df) > 0:
                sample_game_id = games_df.iloc[0]['GAME_ID']
                print(f"Using sample game ID: {sample_game_id}")
                
                # Test various boxscore endpoints
                boxscore_endpoints = [
                    (boxscoretraditionalv2.BoxScoreTraditionalV2, "BoxScoreTraditionalV2"),
                    (boxscoreadvancedv2.BoxScoreAdvancedV2, "BoxScoreAdvancedV2"),
                    (boxscorefourfactorsv2.BoxScoreFourFactorsV2, "BoxScoreFourFactorsV2"),
                    (boxscoremiscv2.BoxScoreMiscV2, "BoxScoreMiscV2"),
                    (boxscorescoringv2.BoxScoreScoringV2, "BoxScoreScoringV2"),
                    (boxscoreusagev2.BoxScoreUsageV2, "BoxScoreUsageV2"),
                ]
                
                for endpoint_func, name in boxscore_endpoints:
                    self.safe_test_endpoint(
                        endpoint_func,
                        name,
                        game_id=sample_game_id
                    )
            else:
                print("No games found for boxscore testing")
        except Exception as e:
            print(f"Could not get sample game for boxscore testing: {e}")

    def test_player_endpoints(self):
        """Test player-specific endpoints"""
        print("\n" + "="*60)
        print("TESTING PLAYER ENDPOINTS")
        print("="*60)

        # Get a sample player ID
        try:
            players = commonallplayers.CommonAllPlayers(
                league_id=self.wnba_league_id,
                season=self.season,
                is_only_current_season=1
            )
            players_df = players.get_data_frames()[0]
            if len(players_df) > 0:
                sample_player_id = players_df.iloc[0]['PERSON_ID']
                print(f"Using sample player ID: {sample_player_id}")

                # Test player endpoints
                self.safe_test_endpoint(
                    playercareerstats.PlayerCareerStats,
                    "PlayerCareerStats",
                    player_id=sample_player_id
                )

                self.safe_test_endpoint(
                    commonplayerinfo.CommonPlayerInfo,
                    "CommonPlayerInfo",
                    player_id=sample_player_id
                )

                self.safe_test_endpoint(
                    playergamelog.PlayerGameLog,
                    "PlayerGameLog",
                    player_id=sample_player_id,
                    season=self.season
                )
            else:
                print("No players found for player endpoint testing")
        except Exception as e:
            print(f"Could not get sample player for testing: {e}")

    def test_team_endpoints(self):
        """Test team-specific endpoints"""
        print("\n" + "="*60)
        print("TESTING TEAM ENDPOINTS")
        print("="*60)

        # Get a sample team ID
        try:
            # Try to get team from scoreboard
            test_date = datetime(2024, 5, 15)
            scoreboard = scoreboardv2.ScoreboardV2(
                league_id=self.wnba_league_id,
                game_date=test_date.strftime('%m/%d/%Y')
            )
            data_frames = scoreboard.get_data_frames()
            if len(data_frames) >= 5:
                standings_df = data_frames[4]  # East standings
                if len(standings_df) > 0:
                    sample_team_id = standings_df.iloc[0]['TEAM_ID']
                    print(f"Using sample team ID: {sample_team_id}")

                    # Test team endpoints
                    self.safe_test_endpoint(
                        teaminfocommon.TeamInfoCommon,
                        "TeamInfoCommon",
                        team_id=sample_team_id
                    )

                    self.safe_test_endpoint(
                        teamgamelog.TeamGameLog,
                        "TeamGameLog",
                        team_id=sample_team_id,
                        season=self.season
                    )

                    self.safe_test_endpoint(
                        commonteamroster.CommonTeamRoster,
                        "CommonTeamRoster",
                        team_id=sample_team_id,
                        season=self.season
                    )
                else:
                    print("No teams found in standings for team endpoint testing")
            else:
                print("Insufficient data frames for team endpoint testing")
        except Exception as e:
            print(f"Could not get sample team for testing: {e}")

    def test_shot_chart_endpoints(self):
        """Test shot chart endpoints"""
        print("\n" + "="*60)
        print("TESTING SHOT CHART ENDPOINTS")
        print("="*60)

        # Get sample player and team IDs
        try:
            players = commonallplayers.CommonAllPlayers(
                league_id=self.wnba_league_id,
                season=self.season,
                is_only_current_season=1
            )
            players_df = players.get_data_frames()[0]
            if len(players_df) > 0:
                sample_player_id = players_df.iloc[0]['PERSON_ID']

                self.safe_test_endpoint(
                    shotchartdetail.ShotChartDetail,
                    "ShotChartDetail",
                    team_id=0,
                    player_id=sample_player_id,
                    season_nullable=self.season
                )

                self.safe_test_endpoint(
                    shotchartleaguewide.ShotChartLeagueWide,
                    "ShotChartLeagueWide",
                    season=self.season,
                    season_type_all_star='Regular Season'
                )
        except Exception as e:
            print(f"Could not test shot chart endpoints: {e}")

    def test_additional_endpoints(self):
        """Test additional useful endpoints"""
        print("\n" + "="*60)
        print("TESTING ADDITIONAL ENDPOINTS")
        print("="*60)

        # Game finder
        self.safe_test_endpoint(
            leaguegamefinder.LeagueGameFinder,
            "LeagueGameFinder",
            season_nullable=self.season,
            league_id_nullable=self.wnba_league_id
        )

        # Lineups
        self.safe_test_endpoint(
            leaguedashlineups.LeagueDashLineups,
            "LeagueDashLineups",
            season=self.season,
            season_type_all_star='Regular Season'
        )

        # Clutch stats
        self.safe_test_endpoint(
            leaguedashplayerclutch.LeagueDashPlayerClutch,
            "LeagueDashPlayerClutch",
            season=self.season,
            season_type_all_star='Regular Season'
        )

        self.safe_test_endpoint(
            leaguedashteamclutch.LeagueDashTeamClutch,
            "LeagueDashTeamClutch",
            season=self.season,
            season_type_all_star='Regular Season'
        )

    def run_comprehensive_test(self):
        """Run all endpoint tests"""
        print("COMPREHENSIVE WNBA ENDPOINT TEST")
        print("="*60)
        print(f"League ID: {self.wnba_league_id}")
        print(f"Season: {self.season}")
        print(f"Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        self.test_basic_endpoints()
        self.test_schedule_endpoints()
        self.test_stats_endpoints()
        self.test_advanced_stats_endpoints()
        self.test_boxscore_endpoints()
        self.test_player_endpoints()
        self.test_team_endpoints()
        self.test_shot_chart_endpoints()
        self.test_additional_endpoints()

        self.print_summary()
        self.save_results()

    def print_summary(self):
        """Print test summary"""
        print("\n" + "="*60)
        print("TEST SUMMARY")
        print("="*60)
        print(f"✅ Working endpoints: {len(self.working_endpoints)}")
        print(f"❌ Failed endpoints: {len(self.failed_endpoints)}")
        print(f"📊 Total tested: {len(self.test_results)}")
        
        print("\n🟢 WORKING ENDPOINTS:")
        for endpoint in self.working_endpoints:
            result = self.test_results[endpoint]
            print(f"  • {endpoint}: {result['total_rows']} rows, {result['dataframes']} dataframes")
        
        print("\n🔴 FAILED ENDPOINTS:")
        for endpoint in self.failed_endpoints:
            result = self.test_results[endpoint]
            print(f"  • {endpoint}: {result.get('error', 'Unknown error')}")

    def save_results(self):
        """Save test results to file"""
        with open('wnba_endpoint_test_results.json', 'w') as f:
            json.dump(self.test_results, f, indent=2, default=str)
        print(f"\n📁 Results saved to: wnba_endpoint_test_results.json")

if __name__ == "__main__":
    tester = WNBAEndpointTester()
    tester.run_comprehensive_test()
