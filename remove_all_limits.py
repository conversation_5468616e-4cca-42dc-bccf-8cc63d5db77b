#!/usr/bin/env python3
"""
Remove All Artificial Limits from WNBA Data Collector
This script removes all LIMIT clauses that are artificially restricting data collection
"""

import re

def remove_limits_from_file():
    """Remove all artificial LIMIT clauses from the data collector"""
    
    print("REMOVING ALL ARTIFICIAL LIMITS FROM WNBA DATA COLLECTOR")
    print("=" * 80)
    
    # Read the file
    with open('wnba_data_collector.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_content = content
    
    # Find all LIMIT clauses and their context
    limit_pattern = r'LIMIT \d+'
    matches = list(re.finditer(limit_pattern, content))
    
    print(f"Found {len(matches)} LIMIT clauses to remove:")
    
    # Remove limits from specific contexts where they're artificial restrictions
    replacements = [
        # Player game logs - remove 100 limit
        (r'ORDER BY p\.season DESC, p\.player_name\s+LIMIT 100', 'ORDER BY p.season DESC, p.player_name'),
        (r'ORDER BY season DESC LIMIT 100', 'ORDER BY season DESC'),
        
        # Team game logs - remove 50 limit  
        (r'ORDER BY t\.season DESC, t\.team_name\s+LIMIT 50', 'ORDER BY t.season DESC, t.team_name'),
        (r'ORDER BY season DESC LIMIT 50', 'ORDER BY season DESC'),
        
        # Team rosters - remove 30 limit
        (r'ORDER BY t\.season DESC, t\.team_name\s+LIMIT 30', 'ORDER BY t.season DESC, t.team_name'),
        (r'ORDER BY season DESC LIMIT 30', 'ORDER BY season DESC'),
        
        # Player analytics - remove 20 limit
        (r'ORDER BY pgl\.season DESC, COUNT\(\*\) DESC\s+LIMIT 20', 'ORDER BY pgl.season DESC, COUNT(*) DESC'),
        (r'ORDER BY season DESC\s+LIMIT 20', 'ORDER BY season DESC'),
        
        # Team vs player matchups - remove limits
        (r'ORDER BY AVG\(pts\) DESC\s+LIMIT 20', 'ORDER BY AVG(pts) DESC'),
        (r'ORDER BY t\.season DESC\s+LIMIT 30', 'ORDER BY t.season DESC'),
        (r'ORDER BY t\.season DESC\s+LIMIT 10', 'ORDER BY t.season DESC'),
        
        # Game rotations - remove 20 limit
        (r'LIMIT 20\s+"""\)', 'LIMIT 50\n            """)'),  # Keep some limit for game-level data to avoid overwhelming API
        
        # Boxscore data - keep smaller limits for game-level data but increase them
        (r'LIMIT 15\s+"""\)', 'LIMIT 50\n            """)'),
        (r'LIMIT 10\s+"""\)', 'LIMIT 30\n            """)'),
        
        # Shot chart data - remove 100 limit
        (r'LIMIT 100\s+"""\)', '""")'),
    ]
    
    changes_made = 0
    
    for pattern, replacement in replacements:
        new_content = re.sub(pattern, replacement, content)
        if new_content != content:
            changes_made += 1
            content = new_content
            print(f"  ✅ Removed limit: {pattern[:50]}...")
    
    # Write the updated file
    if changes_made > 0:
        with open('wnba_data_collector.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"\n✅ SUCCESSFULLY REMOVED {changes_made} ARTIFICIAL LIMITS")
        print("📊 Data collection will now be comprehensive!")
        
        # Show what limits remain (should be minimal game-level limits only)
        remaining_limits = re.findall(limit_pattern, content)
        if remaining_limits:
            print(f"\n📋 Remaining limits (for API protection): {len(remaining_limits)}")
            for limit in set(remaining_limits):
                print(f"  • {limit}")
        else:
            print("\n🎉 ALL LIMITS REMOVED!")
            
    else:
        print("❌ No changes made - limits may have already been removed")
    
    print(f"\n📈 EXPECTED IMPROVEMENTS:")
    print(f"  • Player game logs: ALL players (was limited to 100)")
    print(f"  • Team game logs: ALL teams (was limited to 50)")  
    print(f"  • Team rosters: ALL teams (was limited to 30)")
    print(f"  • Player analytics: ALL eligible players (was limited to 20)")
    print(f"  • Matchup data: ALL combinations (was limited to 10-30)")
    print(f"  • Shot chart data: ALL games (was limited to 100)")
    print(f"  • Game-level data: Increased limits for API safety")

def verify_changes():
    """Verify that the changes were applied correctly"""
    print(f"\n🔍 VERIFYING CHANGES...")
    
    with open('wnba_data_collector.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check for problematic limits that should be removed
    problematic_patterns = [
        r'LIMIT 100.*player',
        r'LIMIT 50.*team', 
        r'LIMIT 30.*roster',
        r'LIMIT 20.*analytics',
        r'LIMIT 10.*matchup'
    ]
    
    issues_found = 0
    for pattern in problematic_patterns:
        if re.search(pattern, content, re.IGNORECASE):
            issues_found += 1
            print(f"  ⚠️ Still found: {pattern}")
    
    if issues_found == 0:
        print("  ✅ All problematic limits successfully removed!")
    else:
        print(f"  ❌ Found {issues_found} remaining issues")
    
    # Count total remaining limits
    remaining_limits = len(re.findall(r'LIMIT \d+', content))
    print(f"  📊 Total remaining LIMIT clauses: {remaining_limits}")

if __name__ == "__main__":
    remove_limits_from_file()
    verify_changes()
