#!/usr/bin/env python3
"""
Test Complete WNBA Analytics Platform
The most comprehensive WNBA data collection and analytics system
"""

from wnba_data_collector import WNBAComprehensiveCollector
import pandas as pd
import os

def test_complete_wnba_analytics_platform():
    """Test the complete WNBA analytics platform with all 20+ endpoints"""
    print("🏀 TESTING COMPLETE WNBA ANALYTICS PLATFORM 🏀")
    print("=" * 100)
    print("🚀 The Most Comprehensive WNBA Data Collection System Available")
    print("=" * 100)
    
    # Create collector instance
    collector = WNBAComprehensiveCollector()
    
    try:
        # Focus on 2024 for comprehensive testing
        collector.seasons = ['2024']
        
        print("\n📊 PHASE 1: FOUNDATION DATA COLLECTION")
        print("-" * 80)
        
        print("1. Collecting core data (teams, players, games)...")
        collector.collect_teams_data()
        collector.collect_players_data()
        
        # Check foundation
        team_count = collector.conn.execute("SELECT COUNT(*) FROM teams WHERE season = '2024'").fetchone()[0]
        player_count = collector.conn.execute("SELECT COUNT(*) FROM players WHERE season = '2024'").fetchone()[0]
        print(f"   ✅ Foundation: {team_count} teams | {player_count} players")
        
        if team_count == 0 or player_count == 0:
            print("❌ Foundation data missing. Cannot proceed with comprehensive testing.")
            return
        
        print("\n🎯 PHASE 2: GAME DATA & PERFORMANCE ANALYTICS")
        print("-" * 80)
        
        print("2. Collecting comprehensive game data...")
        collector.collect_player_game_logs()
        collector.collect_team_game_logs()
        collector.collect_league_game_log()
        
        print("3. Collecting performance trends...")
        collector.collect_player_last_n_games_analytics()
        
        # Check game data
        player_logs = collector.conn.execute("SELECT COUNT(*) FROM player_game_logs").fetchone()[0]
        team_logs = collector.conn.execute("SELECT COUNT(*) FROM team_game_logs").fetchone()[0]
        league_logs = collector.conn.execute("SELECT COUNT(*) FROM league_game_log").fetchone()[0]
        player_trends = collector.conn.execute("SELECT COUNT(*) FROM player_last_n_games").fetchone()[0]
        
        print(f"   ✅ Game Data: {player_logs} player logs | {team_logs} team logs | {league_logs} league logs")
        print(f"   ✅ Trends: {player_trends} performance trend records")
        
        print("\n👥 PHASE 3: ROSTER & ORGANIZATIONAL DATA")
        print("-" * 80)
        
        print("4. Collecting team rosters and coaching staff...")
        collector.collect_team_rosters()
        
        roster_players = collector.conn.execute("SELECT COUNT(*) FROM team_rosters").fetchone()[0]
        coaches = collector.conn.execute("SELECT COUNT(*) FROM team_coaches").fetchone()[0]
        print(f"   ✅ Organization: {roster_players} roster entries | {coaches} coaches")
        
        print("\n🔥 PHASE 4: ADVANCED SHOT ANALYTICS")
        print("-" * 80)
        
        print("5. Collecting shot tracking and defensive analytics...")
        collector.collect_league_dash_player_pt_shot()
        collector.collect_league_dash_opp_pt_shot()
        
        player_shots = collector.conn.execute("SELECT COUNT(*) FROM league_dash_player_pt_shot").fetchone()[0]
        opp_shots = collector.conn.execute("SELECT COUNT(*) FROM league_dash_opp_pt_shot").fetchone()[0]
        print(f"   ✅ Shot Analytics: {player_shots} player shot records | {opp_shots} opponent shot records")
        
        print("\n⚡ PHASE 5: CLUTCH & SITUATIONAL ANALYTICS")
        print("-" * 80)
        
        print("6. Collecting clutch performance and matchup analytics...")
        collector.collect_league_dash_player_clutch()
        collector.collect_team_vs_player_matchups()
        
        clutch_stats = collector.conn.execute("SELECT COUNT(*) FROM league_dash_player_clutch").fetchone()[0]
        matchups = collector.conn.execute("SELECT COUNT(*) FROM team_vs_player_matchups").fetchone()[0]
        print(f"   ✅ Situational: {clutch_stats} clutch records | {matchups} matchup records")
        
        print("\n🏆 PHASE 6: TEAM STRATEGY & LINEUP ANALYTICS")
        print("-" * 80)
        
        print("7. Collecting lineup and tactical analytics...")
        collector.collect_league_dash_lineups()
        collector.collect_league_lineup_viz()
        collector.collect_defense_hub_stats()
        collector.collect_synergy_play_types()
        
        lineups = collector.conn.execute("SELECT COUNT(*) FROM league_dash_lineups").fetchone()[0]
        lineup_viz = collector.conn.execute("SELECT COUNT(*) FROM league_lineup_viz").fetchone()[0]
        defense_stats = collector.conn.execute("SELECT COUNT(*) FROM defense_hub_stats").fetchone()[0]
        play_types = collector.conn.execute("SELECT COUNT(*) FROM synergy_play_types").fetchone()[0]
        
        print(f"   ✅ Strategy: {lineups} lineups | {lineup_viz} lineup viz | {defense_stats} defense | {play_types} play types")
        
        print("\n📈 PHASE 7: SEASON TRACKING & PLAYOFF ANALYTICS")
        print("-" * 80)
        
        print("8. Collecting season progression and playoff data...")
        collector.collect_team_game_streaks()
        collector.collect_playoff_picture()
        
        streaks = collector.conn.execute("SELECT COUNT(*) FROM team_game_streaks").fetchone()[0]
        playoff_data = collector.conn.execute("SELECT COUNT(*) FROM playoff_picture").fetchone()[0]
        print(f"   ✅ Season: {streaks} streak records | {playoff_data} playoff records")
        
        print("\n🎮 PHASE 8: GAME FLOW & ROTATION ANALYTICS")
        print("-" * 80)
        
        print("9. Collecting game rotations and flow data...")
        collector.collect_game_rotations()
        
        rotations = collector.conn.execute("SELECT COUNT(*) FROM game_rotations").fetchone()[0]
        print(f"   ✅ Game Flow: {rotations} rotation records")
        
        print("\n📊 COMPREHENSIVE DATA ANALYSIS")
        print("=" * 100)
        
        # Comprehensive analytics showcase
        print("10. Analyzing collected data across all dimensions...")
        
        # Player performance with shot analytics
        if player_logs > 0 and player_shots > 0:
            cursor = collector.conn.execute("""
                SELECT 
                    pgl.player_name,
                    COUNT(DISTINCT pgl.game_id) as games,
                    AVG(pgl.pts) as avg_pts,
                    AVG(pgl.fg_pct) as avg_fg_pct,
                    COUNT(DISTINCT lps.shot_category) as shot_categories_tracked
                FROM player_game_logs pgl
                LEFT JOIN league_dash_player_pt_shot lps ON pgl.player_id = lps.player_id AND pgl.season = lps.season
                WHERE pgl.season = '2024'
                GROUP BY pgl.player_id, pgl.player_name
                HAVING games >= 5
                ORDER BY avg_pts DESC
                LIMIT 5
            """)
            
            print("\n   🏆 TOP PERFORMERS WITH SHOT ANALYTICS:")
            for player_name, games, avg_pts, avg_fg_pct, shot_cats in cursor.fetchall():
                fg_pct_str = f"{avg_fg_pct:.1%}" if avg_fg_pct else "N/A"
                print(f"     • {player_name}: {avg_pts:.1f} ppg, {fg_pct_str} FG%, {shot_cats} shot categories tracked ({games} games)")
        
        # Team strategy analysis
        if lineups > 0 and defense_stats > 0:
            cursor = collector.conn.execute("""
                SELECT 
                    t.team_name,
                    COUNT(DISTINCT ldl.group_id) as lineups_used,
                    COUNT(DISTINCT dhs.stat_type) as defense_metrics,
                    AVG(ldl.net_rating) as avg_net_rating
                FROM teams t
                LEFT JOIN league_dash_lineups ldl ON t.team_id = ldl.team_id AND t.season = ldl.season
                LEFT JOIN defense_hub_stats dhs ON t.team_id = dhs.team_id AND t.season = dhs.season
                WHERE t.season = '2024'
                GROUP BY t.team_id, t.team_name
                ORDER BY lineups_used DESC
                LIMIT 5
            """)
            
            print("\n   🎯 TEAM STRATEGY ANALYSIS:")
            for team_name, lineups_used, defense_metrics, net_rating in cursor.fetchall():
                net_str = f"{net_rating:.1f}" if net_rating else "N/A"
                print(f"     • {team_name}: {lineups_used} lineups, {defense_metrics} defense metrics, {net_str} net rating")
        
        # Clutch performance analysis
        if clutch_stats > 0:
            cursor = collector.conn.execute("""
                SELECT 
                    player_name,
                    clutch_scenario,
                    AVG(fg_pct) as clutch_fg_pct,
                    AVG(pts) as clutch_pts,
                    COUNT(*) as clutch_situations
                FROM league_dash_player_clutch
                WHERE season = '2024' AND gp >= 3
                GROUP BY player_id, player_name, clutch_scenario
                ORDER BY clutch_fg_pct DESC
                LIMIT 5
            """)
            
            print("\n   ⚡ CLUTCH PERFORMERS:")
            for player_name, scenario, fg_pct, pts, situations in cursor.fetchall():
                fg_str = f"{fg_pct:.1%}" if fg_pct else "N/A"
                pts_str = f"{pts:.1f}" if pts else "N/A"
                print(f"     • {player_name} ({scenario}): {fg_str} FG%, {pts_str} ppg in clutch")
        
        print("\n📁 PHASE 9: DATA EXPORT & VALIDATION")
        print("-" * 80)
        
        print("11. Exporting comprehensive dataset...")
        collector.export_to_csv()
        
        # Validate exports
        all_tables = [
            'teams', 'players', 'player_game_logs', 'team_game_logs', 'league_game_log',
            'team_rosters', 'team_coaches', 'player_last_n_games', 'league_dash_player_pt_shot',
            'league_dash_opp_pt_shot', 'league_dash_player_clutch', 'team_vs_player_matchups',
            'league_dash_lineups', 'league_lineup_viz', 'defense_hub_stats', 'synergy_play_types',
            'team_game_streaks', 'playoff_picture', 'game_rotations'
        ]
        
        exported_count = 0
        total_exported_rows = 0
        
        for table in all_tables:
            csv_path = os.path.join(collector.data_dir, f"{table}.csv")
            if os.path.exists(csv_path):
                df = pd.read_csv(csv_path)
                if len(df) > 0:
                    exported_count += 1
                    total_exported_rows += len(df)
        
        print(f"   ✅ Export Success: {exported_count}/{len(all_tables)} files, {total_exported_rows:,} total rows")
        
        print("\n🎉 FINAL PLATFORM ASSESSMENT")
        print("=" * 100)
        
        # Calculate comprehensive metrics
        table_counts = {}
        total_records = 0
        working_endpoints = 0
        
        for table in all_tables:
            try:
                count = collector.conn.execute(f"SELECT COUNT(*) FROM {table}").fetchone()[0]
                table_counts[table] = count
                total_records += count
                if count > 0:
                    working_endpoints += 1
            except:
                table_counts[table] = 0
        
        # Platform health assessment
        foundation_health = (table_counts['teams'] >= 12 and table_counts['players'] >= 100)
        game_data_health = (table_counts['player_game_logs'] > 0 and table_counts['team_game_logs'] > 0)
        shot_analytics_health = (table_counts['league_dash_player_pt_shot'] > 0)
        clutch_analytics_health = (table_counts['league_dash_player_clutch'] > 0)
        lineup_analytics_health = (table_counts['league_dash_lineups'] > 0)
        
        print(f"📊 PLATFORM METRICS:")
        print(f"   • Total records collected: {total_records:,}")
        print(f"   • Working endpoints: {working_endpoints}/{len(all_tables)} ({working_endpoints/len(all_tables)*100:.0f}%)")
        print(f"   • CSV files exported: {exported_count}")
        
        print(f"\n🔍 SYSTEM HEALTH CHECK:")
        print(f"   {'✅' if foundation_health else '❌'} Foundation Data (Teams & Players)")
        print(f"   {'✅' if game_data_health else '❌'} Game Data Collection")
        print(f"   {'✅' if shot_analytics_health else '❌'} Shot Analytics")
        print(f"   {'✅' if clutch_analytics_health else '❌'} Clutch Performance")
        print(f"   {'✅' if lineup_analytics_health else '❌'} Lineup Analytics")
        
        # Overall platform rating
        health_components = [foundation_health, game_data_health, shot_analytics_health, 
                           clutch_analytics_health, lineup_analytics_health]
        health_score = sum(health_components)
        
        if health_score == 5:
            print(f"\n🎉 OUTSTANDING! Complete WNBA Analytics Platform is fully operational!")
            print(f"   • All major components working perfectly")
            print(f"   • Professional-grade analytics capabilities")
            print(f"   • Ready for advanced WNBA insights and reporting")
        elif health_score >= 4:
            print(f"\n✅ EXCELLENT! Platform is highly functional")
            print(f"   • Most advanced features operational")
            print(f"   • Comprehensive analytics available")
        elif health_score >= 3:
            print(f"\n⚠️  GOOD! Core platform working well")
            print(f"   • Essential analytics functional")
            print(f"   • Some advanced features may need attention")
        else:
            print(f"\n🔧 NEEDS ATTENTION! Platform requires optimization")
        
        print(f"\n🚀 PLATFORM CAPABILITIES:")
        print(f"   • 🎯 Player Performance Tracking & Trend Analysis")
        print(f"   • 📊 Advanced Shot Analytics & Defensive Metrics")
        print(f"   • ⚡ Clutch Performance & Situational Analysis")
        print(f"   • 👥 Team Strategy & Lineup Optimization")
        print(f"   • 🏆 Playoff Race & Season Progression Tracking")
        print(f"   • 🎮 Game Flow & Rotation Analysis")
        print(f"   • 📈 Historical Trends & Comparative Analytics")
        print(f"   • 📁 Comprehensive Data Export for External Tools")
        
        print(f"\n🏀 READY FOR:")
        print(f"   • Professional WNBA Analysis & Reporting")
        print(f"   • Fantasy Basketball Applications")
        print(f"   • Sports Betting & Predictive Analytics")
        print(f"   • Media & Broadcasting Insights")
        print(f"   • Academic Research & Statistical Analysis")
        print(f"   • Fan Engagement & Interactive Platforms")
        
    except Exception as e:
        print(f"❌ Platform test failed: {e}")
        import traceback
        traceback.print_exc()
    finally:
        collector.close()

if __name__ == "__main__":
    test_complete_wnba_analytics_platform()
