#!/usr/bin/env python3
"""
Test API Parameter Fix
Quick test to verify the shot tracking API parameters work
"""

from wnba_data_collector import WNBAComprehensiveCollector
import traceback

def test_shot_tracking_api():
    """Test the shot tracking API with correct parameters"""
    print("TESTING SHOT TRACKING API PARAMETERS")
    print("=" * 60)
    
    collector = WNBAComprehensiveCollector()
    
    try:
        # Test basic shot tracking call
        print("Testing basic shot tracking for 2024...")
        
        # Import the endpoint
        from nba_api.stats.endpoints import leaguedashplayerptshot
        
        # Test with minimal parameters first
        try:
            player_shots = collector.safe_api_call(
                leaguedashplayerptshot.LeagueDashPlayerPtShot,
                league_id=collector.wnba_league_id,
                season='2024',
                season_type_all_star='Regular Season'
            )
            
            if player_shots:
                df = player_shots.get_data_frames()[0]
                print(f"✅ Basic API call successful: {len(df)} records")
                print(f"   Sample columns: {list(df.columns)[:5]}")
            else:
                print("❌ API call returned no data")
                
        except Exception as e:
            print(f"❌ Basic API call failed: {e}")
            traceback.print_exc()
        
        # Test with specific parameters
        print("\nTesting with close defense parameter...")
        try:
            player_shots = collector.safe_api_call(
                leaguedashplayerptshot.LeagueDashPlayerPtShot,
                league_id=collector.wnba_league_id,
                season='2024',
                season_type_all_star='Regular Season',
                close_def_dist_range_nullable='0-2 Feet - Very Tight'
            )
            
            if player_shots:
                df = player_shots.get_data_frames()[0]
                print(f"✅ Close defense API call successful: {len(df)} records")
            else:
                print("❌ Close defense API call returned no data")
                
        except Exception as e:
            print(f"❌ Close defense API call failed: {e}")
            print("   Trying without 'nullable' suffix...")
            
            try:
                player_shots = collector.safe_api_call(
                    leaguedashplayerptshot.LeagueDashPlayerPtShot,
                    league_id=collector.wnba_league_id,
                    season='2024',
                    season_type_all_star='Regular Season',
                    close_def_dist_range='0-2 Feet - Very Tight'
                )
                
                if player_shots:
                    df = player_shots.get_data_frames()[0]
                    print(f"✅ Close defense (no nullable) API call successful: {len(df)} records")
                else:
                    print("❌ Close defense (no nullable) API call returned no data")
                    
            except Exception as e2:
                print(f"❌ Close defense (no nullable) API call also failed: {e2}")
        
        # Test team defense API
        print("\nTesting team defense API...")
        try:
            from nba_api.stats.endpoints import leaguedashptteamdefend
            
            team_defense = collector.safe_api_call(
                leaguedashptteamdefend.LeagueDashPtTeamDefend,
                league_id=collector.wnba_league_id,
                season='2024',
                season_type_all_star='Regular Season',
                defense_category='Overall'
            )
            
            if team_defense:
                df = team_defense.get_data_frames()[0]
                print(f"✅ Team defense API call successful: {len(df)} records")
            else:
                print("❌ Team defense API call returned no data")
                
        except Exception as e:
            print(f"❌ Team defense API call failed: {e}")
        
        print(f"\n📊 CURRENT DATA STATUS:")
        
        # Check current data
        teams = collector.conn.execute("SELECT COUNT(*) FROM teams").fetchone()[0]
        players = collector.conn.execute("SELECT COUNT(*) FROM players").fetchone()[0]
        team_logs = collector.conn.execute("SELECT COUNT(*) FROM team_game_logs").fetchone()[0]
        league_logs = collector.conn.execute("SELECT COUNT(*) FROM league_game_log").fetchone()[0]
        rosters = collector.conn.execute("SELECT COUNT(*) FROM team_rosters").fetchone()[0]
        
        print(f"   • Teams: {teams}")
        print(f"   • Players: {players}")
        print(f"   • Team Game Logs: {team_logs}")
        print(f"   • League Game Logs: {league_logs}")
        print(f"   • Roster Entries: {rosters}")
        
        # Check season coverage
        cursor = collector.conn.execute("""
            SELECT season, COUNT(*) 
            FROM league_game_log 
            GROUP BY season 
            ORDER BY season
        """)
        
        print(f"\n📅 SEASON COVERAGE (League Games):")
        for season, count in cursor.fetchall():
            print(f"   • {season}: {count} game records")
        
        print(f"\n✅ API TEST COMPLETED")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        traceback.print_exc()
    finally:
        collector.close()

if __name__ == "__main__":
    test_shot_tracking_api()
