#!/usr/bin/env python3
"""
WNBA Data Collection with Auto-Save
Ensures data is saved every step of the way with recovery capabilities
"""

from wnba_data_collector import WNBAComprehensiveCollector
import signal
import sys
import time
import os
from datetime import datetime

class AutoSaveWNBACollector:
    def __init__(self):
        self.collector = None
        self.start_time = datetime.now()
        self.last_save_time = datetime.now()
        self.save_interval = 300  # Save every 5 minutes
        
        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
    def signal_handler(self, signum, frame):
        """Handle interruption signals gracefully"""
        print(f"\n🛑 INTERRUPTION DETECTED (Signal {signum})")
        print("💾 Saving all data before exit...")
        
        if self.collector:
            self.collector.force_save()
            self.collector.close()
        
        print("✅ Data saved successfully")
        print("👋 Exiting gracefully...")
        sys.exit(0)
    
    def auto_save_check(self):
        """Check if it's time for an automatic save"""
        current_time = datetime.now()
        if (current_time - self.last_save_time).seconds >= self.save_interval:
            if self.collector:
                print(f"💾 Auto-saving data... ({current_time.strftime('%H:%M:%S')})")
                self.collector.force_save()
                self.last_save_time = current_time
    
    def collect_with_autosave(self):
        """Run collection with automatic saving"""
        print("🏀 WNBA DATA COLLECTION WITH AUTO-SAVE")
        print("=" * 80)
        print("💾 Data will be automatically saved every 5 minutes")
        print("🛑 Press Ctrl+C for graceful shutdown with data save")
        print("=" * 80)
        
        try:
            # Initialize collector
            self.collector = WNBAComprehensiveCollector()
            
            print(f"🚀 Starting comprehensive collection for seasons: {self.collector.seasons}")
            print(f"📅 Start time: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
            
            # Phase 1: Foundation Data
            print(f"\n📋 PHASE 1: FOUNDATION DATA")
            print("-" * 60)
            
            print("1. Collecting teams...")
            self.collector.collect_teams_data()
            self.collector.force_save()
            self.auto_save_check()
            
            print("2. Collecting players...")
            self.collector.collect_players_data()
            self.collector.force_save()
            self.auto_save_check()
            
            # Check foundation
            teams = self.collector.conn.execute("SELECT COUNT(*) FROM teams").fetchone()[0]
            players = self.collector.conn.execute("SELECT COUNT(*) FROM players").fetchone()[0]
            print(f"   ✅ Foundation: {teams:,} teams | {players:,} players")
            
            # Phase 2: Game Data
            print(f"\n🎮 PHASE 2: GAME DATA")
            print("-" * 60)
            
            print("3. Collecting player game logs...")
            self.collector.collect_player_game_logs()
            self.collector.force_save()
            self.auto_save_check()
            
            print("4. Collecting team game logs...")
            self.collector.collect_team_game_logs()
            self.collector.force_save()
            self.auto_save_check()
            
            print("5. Collecting league game logs...")
            self.collector.collect_league_game_log()
            self.collector.force_save()
            self.auto_save_check()
            
            # Check game data
            player_logs = self.collector.conn.execute("SELECT COUNT(*) FROM player_game_logs").fetchone()[0]
            team_logs = self.collector.conn.execute("SELECT COUNT(*) FROM team_game_logs").fetchone()[0]
            league_logs = self.collector.conn.execute("SELECT COUNT(*) FROM league_game_log").fetchone()[0]
            print(f"   ✅ Game Data: {player_logs:,} player | {team_logs:,} team | {league_logs:,} league")
            
            # Phase 3: Roster Data
            print(f"\n👥 PHASE 3: ROSTER DATA")
            print("-" * 60)
            
            print("6. Collecting team rosters...")
            self.collector.collect_team_rosters()
            self.collector.force_save()
            self.auto_save_check()
            
            rosters = self.collector.conn.execute("SELECT COUNT(*) FROM team_rosters").fetchone()[0]
            coaches = self.collector.conn.execute("SELECT COUNT(*) FROM team_coaches").fetchone()[0]
            print(f"   ✅ Rosters: {rosters:,} players | {coaches:,} coaches")
            
            # Phase 4: Advanced Analytics
            print(f"\n📊 PHASE 4: ADVANCED ANALYTICS")
            print("-" * 60)
            
            print("7. Collecting player trends...")
            self.collector.collect_player_last_n_games_analytics()
            self.collector.force_save()
            self.auto_save_check()
            
            print("8. Collecting shot analytics...")
            self.collector.collect_league_dash_player_pt_shot()
            self.collector.force_save()
            self.auto_save_check()
            
            print("9. Collecting clutch performance...")
            self.collector.collect_league_dash_player_clutch()
            self.collector.force_save()
            self.auto_save_check()
            
            print("10. Collecting defensive analytics...")
            self.collector.collect_defense_hub_stats()
            self.collector.collect_league_dash_pt_team_defend()
            self.collector.force_save()
            self.auto_save_check()
            
            # Check analytics
            trends = self.collector.conn.execute("SELECT COUNT(*) FROM player_last_n_games").fetchone()[0]
            shots = self.collector.conn.execute("SELECT COUNT(*) FROM league_dash_player_pt_shot").fetchone()[0]
            clutch = self.collector.conn.execute("SELECT COUNT(*) FROM league_dash_player_clutch").fetchone()[0]
            defense = self.collector.conn.execute("SELECT COUNT(*) FROM defense_hub_stats").fetchone()[0]
            print(f"   ✅ Analytics: {trends:,} trends | {shots:,} shots | {clutch:,} clutch | {defense:,} defense")
            
            # Phase 5: Team Strategy
            print(f"\n🎯 PHASE 5: TEAM STRATEGY")
            print("-" * 60)
            
            print("11. Collecting lineups...")
            self.collector.collect_league_dash_lineups()
            self.collector.collect_league_lineup_viz()
            self.collector.force_save()
            self.auto_save_check()
            
            print("12. Collecting matchups...")
            self.collector.collect_team_vs_player_matchups()
            self.collector.force_save()
            self.auto_save_check()
            
            print("13. Collecting play types...")
            self.collector.collect_synergy_play_types()
            self.collector.force_save()
            self.auto_save_check()
            
            lineups = self.collector.conn.execute("SELECT COUNT(*) FROM league_dash_lineups").fetchone()[0]
            matchups = self.collector.conn.execute("SELECT COUNT(*) FROM team_vs_player_matchups").fetchone()[0]
            play_types = self.collector.conn.execute("SELECT COUNT(*) FROM synergy_play_types").fetchone()[0]
            print(f"   ✅ Strategy: {lineups:,} lineups | {matchups:,} matchups | {play_types:,} play types")
            
            # Phase 6: Season Data
            print(f"\n🏆 PHASE 6: SEASON DATA")
            print("-" * 60)
            
            print("14. Collecting streaks...")
            self.collector.collect_team_game_streaks()
            self.collector.force_save()
            self.auto_save_check()
            
            print("15. Collecting playoff data...")
            self.collector.collect_playoff_picture()
            self.collector.force_save()
            self.auto_save_check()
            
            streaks = self.collector.conn.execute("SELECT COUNT(*) FROM team_game_streaks").fetchone()[0]
            playoffs = self.collector.conn.execute("SELECT COUNT(*) FROM playoff_picture").fetchone()[0]
            print(f"   ✅ Season: {streaks:,} streaks | {playoffs:,} playoff records")
            
            # Phase 7: Game-Level Analytics
            print(f"\n⚡ PHASE 7: GAME-LEVEL ANALYTICS")
            print("-" * 60)
            
            print("16. Collecting defensive matchups...")
            self.collector.collect_boxscore_defensive_v2()
            self.collector.force_save()
            self.auto_save_check()
            
            print("17. Collecting Four Factors...")
            self.collector.collect_boxscore_four_factors_v2()
            self.collector.force_save()
            self.auto_save_check()
            
            print("18. Collecting usage rates...")
            self.collector.collect_boxscore_usage_v2()
            self.collector.force_save()
            self.auto_save_check()
            
            print("19. Collecting player matchups...")
            self.collector.collect_boxscore_matchups_v3()
            self.collector.force_save()
            self.auto_save_check()
            
            print("20. Collecting rotations...")
            self.collector.collect_game_rotations()
            self.collector.force_save()
            self.auto_save_check()
            
            # Final Statistics
            end_time = datetime.now()
            duration = end_time - self.start_time
            
            print(f"\n🎉 COLLECTION COMPLETED!")
            print("=" * 80)
            
            # Get final counts
            all_tables = [
                'teams', 'players', 'player_game_logs', 'team_game_logs', 'league_game_log',
                'team_rosters', 'team_coaches', 'player_last_n_games', 'league_dash_player_pt_shot',
                'league_dash_player_clutch', 'defense_hub_stats', 'league_dash_lineups',
                'team_vs_player_matchups', 'synergy_play_types', 'team_game_streaks',
                'playoff_picture', 'boxscore_defensive_v2', 'boxscore_four_factors_v2',
                'boxscore_usage_v2', 'boxscore_matchups_v3', 'game_rotations'
            ]
            
            total_records = 0
            for table in all_tables:
                try:
                    count = self.collector.conn.execute(f"SELECT COUNT(*) FROM {table}").fetchone()[0]
                    total_records += count
                    print(f"   • {table.replace('_', ' ').title()}: {count:,}")
                except:
                    print(f"   • {table.replace('_', ' ').title()}: 0")
            
            print(f"\n📊 FINAL SUMMARY:")
            print(f"   • Total Records: {total_records:,}")
            print(f"   • Duration: {duration}")
            print(f"   • Database Size: {os.path.getsize('wnba_comprehensive.db') / (1024*1024):.1f} MB")
            
            # Final save and export
            print(f"\n💾 FINAL SAVE AND EXPORT")
            print("-" * 60)
            
            print("21. Final data save...")
            self.collector.force_save()
            
            print("22. Exporting to CSV...")
            self.collector.export_to_csv()
            
            print(f"\n✅ COMPLETE WNBA DATABASE READY!")
            print(f"   🗄️ Database: wnba_comprehensive.db")
            print(f"   📁 CSV Files: wnba_data/ directory")
            print(f"   📅 Coverage: 2015-2025 (11 seasons)")
            print(f"   🏀 Every player, every team, every game!")
            
        except KeyboardInterrupt:
            print(f"\n🛑 Collection interrupted by user")
            if self.collector:
                self.collector.force_save()
        except Exception as e:
            print(f"\n❌ Collection error: {e}")
            if self.collector:
                self.collector.force_save()
            import traceback
            traceback.print_exc()
        finally:
            if self.collector:
                self.collector.close()

def main():
    """Main execution"""
    auto_collector = AutoSaveWNBACollector()
    auto_collector.collect_with_autosave()

if __name__ == "__main__":
    main()
