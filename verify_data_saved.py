#!/usr/bin/env python3
"""
Verify WNBA Data is Properly Saved
Check database integrity and data persistence
"""

import sqlite3
import os
from datetime import datetime
import pandas as pd

def verify_database():
    """Verify the database exists and has data"""
    db_path = 'wnba_comprehensive.db'
    
    print("🔍 VERIFYING WNBA DATABASE")
    print("=" * 60)
    
    # Check if database exists
    if not os.path.exists(db_path):
        print("❌ Database file not found!")
        return False
    
    # Check database size
    db_size = os.path.getsize(db_path) / (1024 * 1024)
    print(f"💾 Database Size: {db_size:.2f} MB")
    
    if db_size < 0.1:
        print("⚠️ Database is very small - may be empty")
    
    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        
        # Check database integrity
        print("\n🔧 CHECKING DATABASE INTEGRITY...")
        integrity_result = conn.execute("PRAGMA integrity_check").fetchone()[0]
        if integrity_result == "ok":
            print("✅ Database integrity: OK")
        else:
            print(f"❌ Database integrity issues: {integrity_result}")
            return False
        
        # Check WAL mode
        journal_mode = conn.execute("PRAGMA journal_mode").fetchone()[0]
        print(f"📝 Journal mode: {journal_mode}")
        
        # List all tables
        print("\n📋 CHECKING TABLES...")
        tables = conn.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name NOT LIKE 'sqlite_%'
            ORDER BY name
        """).fetchall()
        
        if not tables:
            print("❌ No tables found in database!")
            return False
        
        print(f"✅ Found {len(tables)} tables")
        
        # Check each table for data
        print("\n📊 CHECKING TABLE DATA...")
        total_records = 0
        tables_with_data = 0
        
        for (table_name,) in tables:
            try:
                count = conn.execute(f"SELECT COUNT(*) FROM {table_name}").fetchone()[0]
                total_records += count
                
                if count > 0:
                    tables_with_data += 1
                    status = "✅"
                else:
                    status = "⚪"
                
                print(f"   {status} {table_name}: {count:,} records")
                
            except Exception as e:
                print(f"   ❌ {table_name}: Error - {e}")
        
        print(f"\n📈 SUMMARY:")
        print(f"   • Total Tables: {len(tables)}")
        print(f"   • Tables with Data: {tables_with_data}")
        print(f"   • Total Records: {total_records:,}")
        
        # Check season coverage
        print(f"\n📅 SEASON COVERAGE:")
        try:
            # Check teams table for season coverage
            seasons = conn.execute("""
                SELECT DISTINCT season 
                FROM teams 
                ORDER BY season
            """).fetchall()
            
            if seasons:
                season_list = [s[0] for s in seasons]
                print(f"   • Seasons in teams table: {', '.join(season_list)}")
                print(f"   • Season range: {min(season_list)} - {max(season_list)}")
                print(f"   • Total seasons: {len(season_list)}")
            else:
                print("   ⚠️ No season data found in teams table")
                
        except Exception as e:
            print(f"   ❌ Error checking seasons: {e}")
        
        # Check recent data
        print(f"\n⏰ RECENT DATA:")
        try:
            recent_tables = ['teams', 'players', 'player_game_logs']
            for table in recent_tables:
                try:
                    recent = conn.execute(f"""
                        SELECT COUNT(*) 
                        FROM {table} 
                        WHERE collected_at > datetime('now', '-24 hours')
                    """).fetchone()[0]
                    
                    if recent > 0:
                        print(f"   • {table}: {recent:,} records in last 24 hours")
                except:
                    pass
        except Exception as e:
            print(f"   ⚠️ Could not check recent data: {e}")
        
        # Sample data verification
        print(f"\n🔍 SAMPLE DATA VERIFICATION:")
        try:
            # Check if we have actual player names (not just IDs)
            sample_players = conn.execute("""
                SELECT player_name 
                FROM players 
                WHERE player_name IS NOT NULL 
                AND player_name != '' 
                LIMIT 5
            """).fetchall()
            
            if sample_players:
                print("   ✅ Sample players found:")
                for (name,) in sample_players:
                    print(f"     • {name}")
            else:
                print("   ⚠️ No player names found")
                
            # Check if we have actual team names
            sample_teams = conn.execute("""
                SELECT DISTINCT team_name 
                FROM teams 
                WHERE team_name IS NOT NULL 
                AND team_name != '' 
                LIMIT 5
            """).fetchall()
            
            if sample_teams:
                print("   ✅ Sample teams found:")
                for (name,) in sample_teams:
                    print(f"     • {name}")
            else:
                print("   ⚠️ No team names found")
                
        except Exception as e:
            print(f"   ❌ Error checking sample data: {e}")
        
        conn.close()
        
        # Overall assessment
        print(f"\n🎯 OVERALL ASSESSMENT:")
        if total_records > 1000 and tables_with_data > 5:
            print("   ✅ Database appears healthy with substantial data")
            return True
        elif total_records > 100:
            print("   🟡 Database has some data but may be incomplete")
            return True
        else:
            print("   ❌ Database appears to have insufficient data")
            return False
            
    except Exception as e:
        print(f"❌ Error accessing database: {e}")
        return False

def verify_csv_exports():
    """Verify CSV exports exist and have data"""
    print(f"\n📁 VERIFYING CSV EXPORTS")
    print("=" * 60)
    
    data_dir = 'wnba_data'
    
    if not os.path.exists(data_dir):
        print(f"❌ Data directory '{data_dir}' not found")
        return False
    
    csv_files = [f for f in os.listdir(data_dir) if f.endswith('.csv')]
    
    if not csv_files:
        print(f"❌ No CSV files found in '{data_dir}'")
        return False
    
    print(f"✅ Found {len(csv_files)} CSV files")
    
    total_csv_records = 0
    
    for csv_file in sorted(csv_files):
        try:
            file_path = os.path.join(data_dir, csv_file)
            df = pd.read_csv(file_path)
            total_csv_records += len(df)
            
            if len(df) > 0:
                status = "✅"
            else:
                status = "⚪"
            
            file_size = os.path.getsize(file_path) / 1024  # KB
            print(f"   {status} {csv_file}: {len(df):,} rows ({file_size:.1f} KB)")
            
        except Exception as e:
            print(f"   ❌ {csv_file}: Error - {e}")
    
    print(f"\n📊 CSV SUMMARY:")
    print(f"   • Total CSV files: {len(csv_files)}")
    print(f"   • Total CSV records: {total_csv_records:,}")
    
    return total_csv_records > 0

def main():
    """Main verification function"""
    print("🏀 WNBA DATA VERIFICATION TOOL")
    print("=" * 80)
    print(f"📅 Verification Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    # Verify database
    db_ok = verify_database()
    
    # Verify CSV exports
    csv_ok = verify_csv_exports()
    
    # Final assessment
    print(f"\n🎯 FINAL VERIFICATION RESULT")
    print("=" * 80)
    
    if db_ok and csv_ok:
        print("✅ VERIFICATION PASSED")
        print("   • Database is healthy with data")
        print("   • CSV exports are available")
        print("   • WNBA data collection appears successful!")
    elif db_ok:
        print("🟡 PARTIAL SUCCESS")
        print("   • Database has data")
        print("   • CSV exports may be missing or incomplete")
    elif csv_ok:
        print("🟡 PARTIAL SUCCESS")
        print("   • CSV exports available")
        print("   • Database may have issues")
    else:
        print("❌ VERIFICATION FAILED")
        print("   • No substantial data found")
        print("   • Collection may need to be rerun")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
