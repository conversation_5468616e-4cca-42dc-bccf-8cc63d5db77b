{"CommonAllPlayers": {"status": "SUCCESS", "dataframes": 1, "total_rows": 1140, "parameters": {"league_id": "10", "season": "2024", "is_only_current_season": 0}, "columns": [["PERSON_ID", "DISPLAY_LAST_COMMA_FIRST", "DISPLAY_FIRST_LAST", "ROSTERSTATUS", "FROM_YEAR", "TO_YEAR", "PLAYERCODE", "PLAYER_SLUG", "TEAM_ID", "TEAM_CITY", "TEAM_NAME", "TEAM_ABBREVIATION", "TEAM_CODE", "TEAM_SLUG", "IS_NBA_ASSIGNED", "NBA_ASSIGNED_TEAM_ID", "GAMES_PLAYED_FLAG"]]}, "LeagueStandings": {"status": "SUCCESS", "dataframes": 1, "total_rows": 0, "parameters": {"league_id": "10", "season": "2024", "season_type": "Regular Season"}, "columns": [[]]}, "CommonTeamYears": {"status": "SUCCESS", "dataframes": 1, "total_rows": 19, "parameters": {"league_id": "10"}, "columns": [["LEAGUE_ID", "TEAM_ID", "MIN_YEAR", "MAX_YEAR", "ABBREVIATION"]]}, "ScheduleLeagueV2": {"status": "ERROR", "error": "list index out of range"}, "ScoreboardV2": {"status": "SUCCESS", "dataframes": 10, "total_rows": 28, "parameters": {"league_id": "10", "game_date": "05/15/2024"}, "columns": [["GAME_DATE_EST", "GAME_SEQUENCE", "GAME_ID", "GAME_STATUS_ID", "GAME_STATUS_TEXT", "GAMECODE", "HOME_TEAM_ID", "VISITOR_TEAM_ID", "SEASON", "LIVE_PERIOD", "LIVE_PC_TIME", "NATL_TV_BROADCASTER_ABBREVIATION", "HOME_TV_BROADCASTER_ABBREVIATION", "AWAY_TV_BROADCASTER_ABBREVIATION", "LIVE_PERIOD_TIME_BCAST", "ARENA_NAME", "WH_STATUS", "WNBA_COMMISSIONER_FLAG"], ["GAME_DATE_EST", "GAME_SEQUENCE", "GAME_ID", "TEAM_ID", "TEAM_ABBREVIATION", "TEAM_CITY_NAME", "TEAM_NAME", "TEAM_WINS_LOSSES", "PTS_QTR1", "PTS_QTR2", "PTS_QTR3", "PTS_QTR4", "PTS_OT1", "PTS_OT2", "PTS_OT3", "PTS_OT4", "PTS_OT5", "PTS_OT6", "PTS_OT7", "PTS_OT8", "PTS_OT9", "PTS_OT10", "PTS", "FG_PCT", "FT_PCT", "FG3_PCT", "AST", "REB", "TOV"], ["GAME_ID", "HOME_TEAM_ID", "VISITOR_TEAM_ID", "GAME_DATE_EST", "HOME_TEAM_WINS", "HOME_TEAM_LOSSES", "SERIES_LEADER"], ["GAME_ID", "LAST_GAME_ID", "LAST_GAME_DATE_EST", "LAST_GAME_HOME_TEAM_ID", "LAST_GAME_HOME_TEAM_CITY", "LAST_GAME_HOME_TEAM_NAME", "LAST_GAME_HOME_TEAM_ABBREVIATION", "LAST_GAME_HOME_TEAM_POINTS", "LAST_GAME_VISITOR_TEAM_ID", "LAST_GAME_VISITOR_TEAM_CITY", "LAST_GAME_VISITOR_TEAM_NAME", "LAST_GAME_VISITOR_TEAM_CITY1", "LAST_GAME_VISITOR_TEAM_POINTS"], ["TEAM_ID", "LEAGUE_ID", "SEASON_ID", "STANDINGSDATE", "CONFERENCE", "TEAM", "G", "W", "L", "W_PCT", "HOME_RECORD", "ROAD_RECORD"], ["TEAM_ID", "LEAGUE_ID", "SEASON_ID", "STANDINGSDATE", "CONFERENCE", "TEAM", "G", "W", "L", "W_PCT", "HOME_RECORD", "ROAD_RECORD"], ["GAME_ID", "PT_AVAILABLE"], ["GAME_ID", "TEAM_ID", "TEAM_CITY", "TEAM_NICKNAME", "TEAM_ABBREVIATION", "PTS_PLAYER_ID", "PTS_PLAYER_NAME", "PTS", "REB_PLAYER_ID", "REB_PLAYER_NAME", "REB", "AST_PLAYER_ID", "AST_PLAYER_NAME", "AST"], ["GAME_ID", "LEAG_TIX"], []]}, "LeagueDashTeamStats": {"status": "SUCCESS", "dataframes": 1, "total_rows": 76, "parameters": {"season": "2024", "season_type_all_star": "Regular Season"}, "columns": [["TEAM_ID", "TEAM_NAME", "GP", "W", "L", "W_PCT", "MIN", "FGM", "FGA", "FG_PCT", "FG3M", "FG3A", "FG3_PCT", "FTM", "FTA", "FT_PCT", "OREB", "DREB", "REB", "AST", "TOV", "STL", "BLK", "BLKA", "PF", "PFD", "PTS", "PLUS_MINUS", "GP_RANK", "W_RANK", "L_RANK", "W_PCT_RANK", "MIN_RANK", "FGM_RANK", "FGA_RANK", "FG_PCT_RANK", "FG3M_RANK", "FG3A_RANK", "FG3_PCT_RANK", "FTM_RANK", "FTA_RANK", "FT_PCT_RANK", "OREB_RANK", "DREB_RANK", "REB_RANK", "AST_RANK", "TOV_RANK", "STL_RANK", "BLK_RANK", "BLKA_RANK", "PF_RANK", "PFD_RANK", "PTS_RANK", "PLUS_MINUS_RANK"]]}, "LeagueDashPlayerStats": {"status": "SUCCESS", "dataframes": 1, "total_rows": 1585, "parameters": {"season": "2024", "season_type_all_star": "Regular Season"}, "columns": [["PLAYER_ID", "PLAYER_NAME", "NICKNAME", "TEAM_ID", "TEAM_ABBREVIATION", "AGE", "GP", "W", "L", "W_PCT", "MIN", "FGM", "FGA", "FG_PCT", "FG3M", "FG3A", "FG3_PCT", "FTM", "FTA", "FT_PCT", "OREB", "DREB", "REB", "AST", "TOV", "STL", "BLK", "BLKA", "PF", "PFD", "PTS", "PLUS_MINUS", "NBA_FANTASY_PTS", "DD2", "TD3", "WNBA_FANTASY_PTS", "GP_RANK", "W_RANK", "L_RANK", "W_PCT_RANK", "MIN_RANK", "FGM_RANK", "FGA_RANK", "FG_PCT_RANK", "FG3M_RANK", "FG3A_RANK", "FG3_PCT_RANK", "FTM_RANK", "FTA_RANK", "FT_PCT_RANK", "OREB_RANK", "DREB_RANK", "REB_RANK", "AST_RANK", "TOV_RANK", "STL_RANK", "BLK_RANK", "BLKA_RANK", "PF_RANK", "PFD_RANK", "PTS_RANK", "PLUS_MINUS_RANK", "NBA_FANTASY_PTS_RANK", "DD2_RANK", "TD3_RANK", "WNBA_FANTASY_PTS_RANK", "TEAM_COUNT"]]}, "LeagueLeaders": {"status": "ERROR", "error": "LeagueLeaders.__init__() got an unexpected keyword argument 'season_type'"}, "LeagueHustleStatsPlayer": {"status": "SUCCESS", "dataframes": 1, "total_rows": 0, "parameters": {"season": "2024", "season_type_all_star": "Regular Season"}, "columns": [["PLAYER_ID", "PLAYER_NAME", "TEAM_ID", "TEAM_ABBREVIATION", "AGE", "G", "MIN", "CONTESTED_SHOTS", "CONTESTED_SHOTS_2PT", "CONTESTED_SHOTS_3PT", "DEFLECTIONS", "CHARGES_DRAWN", "SCREEN_ASSISTS", "SCREEN_AST_PTS", "OFF_LOOSE_BALLS_RECOVERED", "DEF_LOOSE_BALLS_RECOVERED", "LOOSE_BALLS_RECOVERED", "PCT_LOOSE_BALLS_RECOVERED_OFF", "PCT_LOOSE_BALLS_RECOVERED_DEF", "OFF_BOXOUTS", "DEF_BOXOUTS", "BOX_OUT_PLAYER_TEAM_REBS", "BOX_OUT_PLAYER_REBS", "BOX_OUTS", "PCT_BOX_OUTS_OFF", "PCT_BOX_OUTS_DEF", "PCT_BOX_OUTS_TEAM_REB", "PCT_BOX_OUTS_REB"]]}, "LeagueDashPtStats_SpeedDistance": {"status": "SUCCESS", "dataframes": 1, "total_rows": 0, "parameters": {"season": "2024", "season_type_all_star": "Regular Season", "pt_measure_type": "SpeedDistance"}, "columns": [["TEAM_ID", "TEAM_ABBREVIATION", "TEAM_NAME", "GP", "W", "L", "MIN", "DIST_FEET", "DIST_MILES", "DIST_MILES_OFF", "DIST_MILES_DEF", "AVG_SPEED", "AVG_SPEED_OFF", "AVG_SPEED_DEF"]]}, "LeagueDashPtStats_Rebounding": {"status": "SUCCESS", "dataframes": 1, "total_rows": 0, "parameters": {"season": "2024", "season_type_all_star": "Regular Season", "pt_measure_type": "Rebounding"}, "columns": [["TEAM_ID", "TEAM_ABBREVIATION", "TEAM_NAME", "GP", "W", "L", "MIN", "OREB", "OREB_CONTEST", "OREB_UNCONTEST", "OREB_CONTEST_PCT", "OREB_CHANCES", "OREB_CHANCE_PCT", "OREB_CHANCE_DEFER", "OREB_CHANCE_PCT_ADJ", "AVG_OREB_DIST", "DREB", "DREB_CONTEST", "DREB_UNCONTEST", "DREB_CONTEST_PCT", "DREB_CHANCES", "DREB_CHANCE_PCT", "DREB_CHANCE_DEFER", "DREB_CHANCE_PCT_ADJ", "AVG_DREB_DIST", "REB", "REB_CONTEST", "REB_UNCONTEST", "REB_CONTEST_PCT", "REB_CHANCES", "REB_CHANCE_PCT", "REB_CHANCE_DEFER", "REB_CHANCE_PCT_ADJ", "AVG_REB_DIST"]]}, "LeagueDashPtStats_Possessions": {"status": "SUCCESS", "dataframes": 1, "total_rows": 0, "parameters": {"season": "2024", "season_type_all_star": "Regular Season", "pt_measure_type": "Possessions"}, "columns": [["TEAM_ID", "TEAM_ABBREVIATION", "TEAM_NAME", "GP", "W", "L", "MIN", "POINTS", "TOUCHES", "FRONT_CT_TOUCHES", "TIME_OF_POSS", "AVG_SEC_PER_TOUCH", "AVG_DRIB_PER_TOUCH", "PTS_PER_TOUCH", "ELBOW_TOUCHES", "POST_TOUCHES", "PAINT_TOUCHES", "PTS_PER_ELBOW_TOUCH", "PTS_PER_POST_TOUCH", "PTS_PER_PAINT_TOUCH"]]}, "LeagueDashPtStats_CatchShoot": {"status": "SUCCESS", "dataframes": 1, "total_rows": 0, "parameters": {"season": "2024", "season_type_all_star": "Regular Season", "pt_measure_type": "CatchShoot"}, "columns": [["TEAM_ID", "TEAM_ABBREVIATION", "TEAM_NAME", "GP", "W", "L", "MIN", "CATCH_SHOOT_FGM", "CATCH_SHOOT_FGA", "CATCH_SHOOT_FG_PCT", "CATCH_SHOOT_PTS", "CATCH_SHOOT_FG3M", "CATCH_SHOOT_FG3A", "CATCH_SHOOT_FG3_PCT", "CATCH_SHOOT_EFG_PCT"]]}, "LeagueDashPtStats_Defense": {"status": "SUCCESS", "dataframes": 1, "total_rows": 0, "parameters": {"season": "2024", "season_type_all_star": "Regular Season", "pt_measure_type": "Defense"}, "columns": [["TEAM_ID", "TEAM_ABBREVIATION", "TEAM_NAME", "GP", "W", "L", "MIN", "STL", "BLK", "DREB", "DEF_RIM_FGM", "DEF_RIM_FGA", "DEF_RIM_FG_PCT"]]}, "LeagueDashPtStats_Drives": {"status": "SUCCESS", "dataframes": 1, "total_rows": 0, "parameters": {"season": "2024", "season_type_all_star": "Regular Season", "pt_measure_type": "Drives"}, "columns": [["TEAM_ID", "TEAM_ABBREVIATION", "TEAM_NAME", "GP", "W", "L", "MIN", "DRIVES", "DRIVE_FGM", "DRIVE_FGA", "DRIVE_FG_PCT", "DRIVE_FTM", "DRIVE_FTA", "DRIVE_FT_PCT", "DRIVE_PTS", "DRIVE_PTS_PCT", "DRIVE_PASSES", "DRIVE_PASSES_PCT", "DRIVE_AST", "DRIVE_AST_PCT", "DRIVE_TOV", "DRIVE_TOV_PCT", "DRIVE_PF", "DRIVE_PF_PCT"]]}, "LeagueDashPtStats_Passing": {"status": "SUCCESS", "dataframes": 1, "total_rows": 0, "parameters": {"season": "2024", "season_type_all_star": "Regular Season", "pt_measure_type": "Passing"}, "columns": [["TEAM_ID", "TEAM_ABBREVIATION", "TEAM_NAME", "GP", "W", "L", "MIN", "PASSES_MADE", "PASSES_RECEIVED", "AST", "FT_AST", "SECONDARY_AST", "POTENTIAL_AST", "AST_PTS_CREATED", "AST_ADJ", "AST_TO_PASS_PCT", "AST_TO_PASS_PCT_ADJ"]]}, "BoxScoreTraditionalV2": {"status": "SUCCESS", "dataframes": 3, "total_rows": 26, "parameters": {"game_id": "1022400005"}, "columns": [["GAME_ID", "TEAM_ID", "TEAM_ABBREVIATION", "TEAM_CITY", "PLAYER_ID", "PLAYER_NAME", "NICKNAME", "START_POSITION", "COMMENT", "MIN", "FGM", "FGA", "FG_PCT", "FG3M", "FG3A", "FG3_PCT", "FTM", "FTA", "FT_PCT", "OREB", "DREB", "REB", "AST", "STL", "BLK", "TO", "PF", "PTS", "PLUS_MINUS"], ["GAME_ID", "TEAM_ID", "TEAM_NAME", "TEAM_ABBREVIATION", "TEAM_CITY", "MIN", "FGM", "FGA", "FG_PCT", "FG3M", "FG3A", "FG3_PCT", "FTM", "FTA", "FT_PCT", "OREB", "DREB", "REB", "AST", "STL", "BLK", "TO", "PF", "PTS", "PLUS_MINUS"], ["GAME_ID", "TEAM_ID", "TEAM_NAME", "TEAM_ABBREVIATION", "TEAM_CITY", "STARTERS_BENCH", "MIN", "FGM", "FGA", "FG_PCT", "FG3M", "FG3A", "FG3_PCT", "FTM", "FTA", "FT_PCT", "OREB", "DREB", "REB", "AST", "STL", "BLK", "TO", "PF", "PTS"]]}, "BoxScoreAdvancedV2": {"status": "SUCCESS", "dataframes": 2, "total_rows": 22, "parameters": {"game_id": "1022400005"}, "columns": [["GAME_ID", "TEAM_ID", "TEAM_ABBREVIATION", "TEAM_CITY", "PLAYER_ID", "PLAYER_NAME", "NICKNAME", "START_POSITION", "COMMENT", "MIN", "E_OFF_RATING", "OFF_RATING", "E_DEF_RATING", "DEF_RATING", "E_NET_RATING", "NET_RATING", "AST_PCT", "AST_TOV", "AST_RATIO", "OREB_PCT", "DREB_PCT", "REB_PCT", "TM_TOV_PCT", "EFG_PCT", "TS_PCT", "USG_PCT", "E_USG_PCT", "E_PACE", "PACE", "PACE_PER40", "POSS", "PIE"], ["GAME_ID", "TEAM_ID", "TEAM_NAME", "TEAM_ABBREVIATION", "TEAM_CITY", "MIN", "E_OFF_RATING", "OFF_RATING", "E_DEF_RATING", "DEF_RATING", "E_NET_RATING", "NET_RATING", "AST_PCT", "AST_TOV", "AST_RATIO", "OREB_PCT", "DREB_PCT", "REB_PCT", "E_TM_TOV_PCT", "TM_TOV_PCT", "EFG_PCT", "TS_PCT", "USG_PCT", "E_USG_PCT", "E_PACE", "PACE", "PACE_PER40", "POSS", "PIE"]]}, "BoxScoreFourFactorsV2": {"status": "SUCCESS", "dataframes": 2, "total_rows": 22, "parameters": {"game_id": "1022400005"}, "columns": [["GAME_ID", "TEAM_ID", "TEAM_ABBREVIATION", "TEAM_CITY", "PLAYER_ID", "PLAYER_NAME", "NICKNAME", "START_POSITION", "COMMENT", "MIN", "EFG_PCT", "FTA_RATE", "TM_TOV_PCT", "OREB_PCT", "OPP_EFG_PCT", "OPP_FTA_RATE", "OPP_TOV_PCT", "OPP_OREB_PCT"], ["GAME_ID", "TEAM_ID", "TEAM_NAME", "TEAM_ABBREVIATION", "TEAM_CITY", "MIN", "EFG_PCT", "FTA_RATE", "TM_TOV_PCT", "OREB_PCT", "OPP_EFG_PCT", "OPP_FTA_RATE", "OPP_TOV_PCT", "OPP_OREB_PCT"]]}, "BoxScoreMiscV2": {"status": "SUCCESS", "dataframes": 2, "total_rows": 22, "parameters": {"game_id": "1022400005"}, "columns": [["GAME_ID", "TEAM_ID", "TEAM_ABBREVIATION", "TEAM_CITY", "PLAYER_ID", "PLAYER_NAME", "NICKNAME", "START_POSITION", "COMMENT", "MIN", "PTS_OFF_TOV", "PTS_2ND_CHANCE", "PTS_FB", "PTS_PAINT", "OPP_PTS_OFF_TOV", "OPP_PTS_2ND_CHANCE", "OPP_PTS_FB", "OPP_PTS_PAINT", "BLK", "BLKA", "PF", "PFD"], ["GAME_ID", "TEAM_ID", "TEAM_NAME", "TEAM_ABBREVIATION", "TEAM_CITY", "MIN", "PTS_OFF_TOV", "PTS_2ND_CHANCE", "PTS_FB", "PTS_PAINT", "OPP_PTS_OFF_TOV", "OPP_PTS_2ND_CHANCE", "OPP_PTS_FB", "OPP_PTS_PAINT", "BLK", "BLKA", "PF", "PFD"]]}, "BoxScoreScoringV2": {"status": "SUCCESS", "dataframes": 2, "total_rows": 22, "parameters": {"game_id": "1022400005"}, "columns": [["GAME_ID", "TEAM_ID", "TEAM_ABBREVIATION", "TEAM_CITY", "PLAYER_ID", "PLAYER_NAME", "NICKNAME", "START_POSITION", "COMMENT", "MIN", "PCT_FGA_2PT", "PCT_FGA_3PT", "PCT_PTS_2PT", "PCT_PTS_2PT_MR", "PCT_PTS_3PT", "PCT_PTS_FB", "PCT_PTS_FT", "PCT_PTS_OFF_TOV", "PCT_PTS_PAINT", "PCT_AST_2PM", "PCT_UAST_2PM", "PCT_AST_3PM", "PCT_UAST_3PM", "PCT_AST_FGM", "PCT_UAST_FGM"], ["GAME_ID", "TEAM_ID", "TEAM_NAME", "TEAM_ABBREVIATION", "TEAM_CITY", "MIN", "PCT_FGA_2PT", "PCT_FGA_3PT", "PCT_PTS_2PT", "PCT_PTS_2PT_MR", "PCT_PTS_3PT", "PCT_PTS_FB", "PCT_PTS_FT", "PCT_PTS_OFF_TOV", "PCT_PTS_PAINT", "PCT_AST_2PM", "PCT_UAST_2PM", "PCT_AST_3PM", "PCT_UAST_3PM", "PCT_AST_FGM", "PCT_UAST_FGM"]]}, "BoxScoreUsageV2": {"status": "SUCCESS", "dataframes": 2, "total_rows": 22, "parameters": {"game_id": "1022400005"}, "columns": [["GAME_ID", "TEAM_ID", "TEAM_ABBREVIATION", "TEAM_CITY", "PLAYER_ID", "PLAYER_NAME", "NICKNAME", "START_POSITION", "COMMENT", "MIN", "USG_PCT", "PCT_FGM", "PCT_FGA", "PCT_FG3M", "PCT_FG3A", "PCT_FTM", "PCT_FTA", "PCT_OREB", "PCT_DREB", "PCT_REB", "PCT_AST", "PCT_TOV", "PCT_STL", "PCT_BLK", "PCT_BLKA", "PCT_PF", "PCT_PFD", "PCT_PTS"], ["GAME_ID", "TEAM_ID", "TEAM_NAME", "TEAM_ABBREVIATION", "TEAM_CITY", "MIN", "USG_PCT", "PCT_FGM", "PCT_FGA", "PCT_FG3M", "PCT_FG3A", "PCT_FTM", "PCT_FTA", "PCT_OREB", "PCT_DREB", "PCT_REB", "PCT_AST", "PCT_TOV", "PCT_STL", "PCT_BLK", "PCT_BLKA", "PCT_PF", "PCT_PFD", "PCT_PTS"]]}, "PlayerCareerStats": {"status": "SUCCESS", "dataframes": 12, "total_rows": 0, "parameters": {"player_id": "1641656"}, "columns": [["PLAYER_ID", "SEASON_ID", "LEAGUE_ID", "TEAM_ID", "TEAM_ABBREVIATION", "PLAYER_AGE", "GP", "GS", "MIN", "FGM", "FGA", "FG_PCT", "FG3M", "FG3A", "FG3_PCT", "FTM", "FTA", "FT_PCT", "OREB", "DREB", "REB", "AST", "STL", "BLK", "TOV", "PF", "PTS"], ["PLAYER_ID", "LEAGUE_ID", "Team_ID", "GP", "GS", "MIN", "FGM", "FGA", "FG_PCT", "FG3M", "FG3A", "FG3_PCT", "FTM", "FTA", "FT_PCT", "OREB", "DREB", "REB", "AST", "STL", "BLK", "TOV", "PF", "PTS"], ["PLAYER_ID", "SEASON_ID", "LEAGUE_ID", "TEAM_ID", "TEAM_ABBREVIATION", "PLAYER_AGE", "GP", "GS", "MIN", "FGM", "FGA", "FG_PCT", "FG3M", "FG3A", "FG3_PCT", "FTM", "FTA", "FT_PCT", "OREB", "DREB", "REB", "AST", "STL", "BLK", "TOV", "PF", "PTS"], ["PLAYER_ID", "LEAGUE_ID", "Team_ID", "GP", "GS", "MIN", "FGM", "FGA", "FG_PCT", "FG3M", "FG3A", "FG3_PCT", "FTM", "FTA", "FT_PCT", "OREB", "DREB", "REB", "AST", "STL", "BLK", "TOV", "PF", "PTS"], ["PLAYER_ID", "SEASON_ID", "LEAGUE_ID", "TEAM_ID", "TEAM_ABBREVIATION", "PLAYER_AGE", "GP", "GS", "MIN", "FGM", "FGA", "FG_PCT", "FG3M", "FG3A", "FG3_PCT", "FTM", "FTA", "FT_PCT", "OREB", "DREB", "REB", "AST", "STL", "BLK", "TOV", "PF", "PTS"], ["PLAYER_ID", "LEAGUE_ID", "Team_ID", "GP", "GS", "MIN", "FGM", "FGA", "FG_PCT", "FG3M", "FG3A", "FG3_PCT", "FTM", "FTA", "FT_PCT", "OREB", "DREB", "REB", "AST", "STL", "BLK", "TOV", "PF", "PTS"], ["PLAYER_ID", "SEASON_ID", "LEAGUE_ID", "ORGANIZATION_ID", "SCHOOL_NAME", "PLAYER_AGE", "GP", "GS", "MIN", "FGM", "FGA", "FG_PCT", "FG3M", "FG3A", "FG3_PCT", "FTM", "FTA", "FT_PCT", "OREB", "DREB", "REB", "AST", "STL", "BLK", "TOV", "PF", "PTS"], ["PLAYER_ID", "LEAGUE_ID", "ORGANIZATION_ID", "GP", "GS", "MIN", "FGM", "FGA", "FG_PCT", "FG3M", "FG3A", "FG3_PCT", "FTM", "FTA", "FT_PCT", "OREB", "DREB", "REB", "AST", "STL", "BLK", "TOV", "PF", "PTS"], ["PLAYER_ID", "SEASON_ID", "LEAGUE_ID", "TEAM_ID", "TEAM_ABBREVIATION", "PLAYER_AGE", "GP", "GS", "MIN", "FGM", "FGA", "FG_PCT", "FG3M", "FG3A", "FG3_PCT", "FTM", "FTA", "FT_PCT", "OREB", "DREB", "REB", "AST", "STL", "BLK", "TOV", "PF", "PTS"], ["PLAYER_ID", "LEAGUE_ID", "Team_ID", "GP", "GS", "MIN", "FGM", "FGA", "FG_PCT", "FG3M", "FG3A", "FG3_PCT", "FTM", "FTA", "FT_PCT", "OREB", "DREB", "REB", "AST", "STL", "BLK", "TOV", "PF", "PTS"], ["PLAYER_ID", "SEASON_ID", "LEAGUE_ID", "TEAM_ID", "TEAM_ABBREVIATION", "PLAYER_AGE", "GP", "GS", "RANK_MIN", "RANK_FGM", "RANK_FGA", "RANK_FG_PCT", "RANK_FG3M", "RANK_FG3A", "RANK_FG3_PCT", "RANK_FTM", "RANK_FTA", "RANK_FT_PCT", "RANK_OREB", "RANK_DREB", "RANK_REB", "RANK_AST", "RANK_STL", "RANK_BLK", "RANK_TOV", "RANK_PTS", "RANK_EFF"], ["PLAYER_ID", "SEASON_ID", "LEAGUE_ID", "TEAM_ID", "TEAM_ABBREVIATION", "PLAYER_AGE", "GP", "GS", "RANK_MIN", "RANK_FGM", "RANK_FGA", "RANK_FG_PCT", "RANK_FG3M", "RANK_FG3A", "RANK_FG3_PCT", "RANK_FTM", "RANK_FTA", "RANK_FT_PCT", "RANK_OREB", "RANK_DREB", "RANK_REB", "RANK_AST", "RANK_STL", "RANK_BLK", "RANK_TOV", "RANK_PTS", "RANK_EFF"]]}, "CommonPlayerInfo": {"status": "SUCCESS", "dataframes": 3, "total_rows": 1, "parameters": {"player_id": "1641656"}, "columns": [["PERSON_ID", "FIRST_NAME", "LAST_NAME", "DISPLAY_FIRST_LAST", "DISPLAY_LAST_COMMA_FIRST", "DISPLAY_FI_LAST", "PLAYER_SLUG", "BIRTHDATE", "SCHOOL", "COUNTRY", "LAST_AFFILIATION", "HEIGHT", "WEIGHT", "SEASON_EXP", "JERSEY", "POSITION", "ROSTERSTATUS", "GAMES_PLAYED_CURRENT_SEASON_FLAG", "TEAM_ID", "TEAM_NAME", "TEAM_ABBREVIATION", "TEAM_CODE", "TEAM_CITY", "PLAYERCODE", "FROM_YEAR", "TO_YEAR", "DLEAGUE_FLAG", "NBA_FLAG", "GAMES_PLAYED_FLAG", "DRAFT_YEAR", "DRAFT_ROUND", "DRAFT_NUMBER", "GREATEST_75_FLAG"], ["PLAYER_ID", "PLAYER_NAME", "TimeFrame", "PTS", "AST", "REB", "PIE"], ["SEASON_ID"]]}, "PlayerGameLog": {"status": "SUCCESS", "dataframes": 1, "total_rows": 0, "parameters": {"player_id": "1641656", "season": "2024"}, "columns": [["SEASON_ID", "Player_ID", "Game_ID", "GAME_DATE", "MATCHUP", "WL", "MIN", "FGM", "FGA", "FG_PCT", "FG3M", "FG3A", "FG3_PCT", "FTM", "FTA", "FT_PCT", "OREB", "DREB", "REB", "AST", "STL", "BLK", "TOV", "PF", "PTS", "PLUS_MINUS", "VIDEO_AVAILABLE"]]}, "TeamInfoCommon": {"status": "ERROR", "error": "Expecting value: line 1 column 1 (char 0)"}, "TeamGameLog": {"status": "SUCCESS", "dataframes": 1, "total_rows": 0, "parameters": {"team_id": "1611661330", "season": "2024"}, "columns": [["Team_ID", "Game_ID", "GAME_DATE", "MATCHUP", "WL", "W", "L", "W_PCT", "MIN", "FGM", "FGA", "FG_PCT", "FG3M", "FG3A", "FG3_PCT", "FTM", "FTA", "FT_PCT", "OREB", "DREB", "REB", "AST", "STL", "BLK", "TOV", "PF", "PTS"]]}, "CommonTeamRoster": {"status": "ERROR", "error": "'Coaches'"}, "ShotChartDetail": {"status": "SUCCESS", "dataframes": 2, "total_rows": 20, "parameters": {"team_id": 0, "player_id": "1641656", "season_nullable": "2024"}, "columns": [["GRID_TYPE", "GAME_ID", "GAME_EVENT_ID", "PLAYER_ID", "PLAYER_NAME", "TEAM_ID", "TEAM_NAME", "PERIOD", "MINUTES_REMAINING", "SECONDS_REMAINING", "EVENT_TYPE", "ACTION_TYPE", "SHOT_TYPE", "SHOT_ZONE_BASIC", "SHOT_ZONE_AREA", "SHOT_ZONE_RANGE", "SHOT_DISTANCE", "LOC_X", "LOC_Y", "SHOT_ATTEMPTED_FLAG", "SHOT_MADE_FLAG", "GAME_DATE", "HTM", "VTM"], ["GRID_TYPE", "SHOT_ZONE_BASIC", "SHOT_ZONE_AREA", "SHOT_ZONE_RANGE", "FGA", "FGM", "FG_PCT"]]}, "ShotChartLeagueWide": {"status": "ERROR", "error": "ShotChartLeagueWide.__init__() got an unexpected keyword argument 'season_type_all_star'"}, "LeagueGameFinder": {"status": "SUCCESS", "dataframes": 1, "total_rows": 550, "parameters": {"season_nullable": "2024", "league_id_nullable": "10"}, "columns": [["SEASON_ID", "TEAM_ID", "TEAM_ABBREVIATION", "TEAM_NAME", "GAME_ID", "GAME_DATE", "MATCHUP", "WL", "MIN", "PTS", "FGM", "FGA", "FG_PCT", "FG3M", "FG3A", "FG3_PCT", "FTM", "FTA", "FT_PCT", "OREB", "DREB", "REB", "AST", "STL", "BLK", "TOV", "PF", "PLUS_MINUS"]]}, "LeagueDashLineups": {"status": "SUCCESS", "dataframes": 1, "total_rows": 0, "parameters": {"season": "2024", "season_type_all_star": "Regular Season"}, "columns": [["GROUP_SET", "GROUP_ID", "GROUP_NAME", "TEAM_ID", "TEAM_ABBREVIATION", "GP", "W", "L", "W_PCT", "MIN", "FGM", "FGA", "FG_PCT", "FG3M", "FG3A", "FG3_PCT", "FTM", "FTA", "FT_PCT", "OREB", "DREB", "REB", "AST", "TOV", "STL", "BLK", "BLKA", "PF", "PFD", "PTS", "PLUS_MINUS", "GP_RANK", "W_RANK", "L_RANK", "W_PCT_RANK", "MIN_RANK", "FGM_RANK", "FGA_RANK", "FG_PCT_RANK", "FG3M_RANK", "FG3A_RANK", "FG3_PCT_RANK", "FTM_RANK", "FTA_RANK", "FT_PCT_RANK", "OREB_RANK", "DREB_RANK", "REB_RANK", "AST_RANK", "TOV_RANK", "STL_RANK", "BLK_RANK", "BLKA_RANK", "PF_RANK", "PFD_RANK", "PTS_RANK", "PLUS_MINUS_RANK"]]}, "LeagueDashPlayerClutch": {"status": "SUCCESS", "dataframes": 1, "total_rows": 1313, "parameters": {"season": "2024", "season_type_all_star": "Regular Season"}, "columns": [["GROUP_SET", "PLAYER_ID", "PLAYER_NAME", "NICKNAME", "TEAM_ID", "TEAM_ABBREVIATION", "AGE", "GP", "W", "L", "W_PCT", "MIN", "FGM", "FGA", "FG_PCT", "FG3M", "FG3A", "FG3_PCT", "FTM", "FTA", "FT_PCT", "OREB", "DREB", "REB", "AST", "TOV", "STL", "BLK", "BLKA", "PF", "PFD", "PTS", "PLUS_MINUS", "NBA_FANTASY_PTS", "DD2", "TD3", "WNBA_FANTASY_PTS", "GP_RANK", "W_RANK", "L_RANK", "W_PCT_RANK", "MIN_RANK", "FGM_RANK", "FGA_RANK", "FG_PCT_RANK", "FG3M_RANK", "FG3A_RANK", "FG3_PCT_RANK", "FTM_RANK", "FTA_RANK", "FT_PCT_RANK", "OREB_RANK", "DREB_RANK", "REB_RANK", "AST_RANK", "TOV_RANK", "STL_RANK", "BLK_RANK", "BLKA_RANK", "PF_RANK", "PFD_RANK", "PTS_RANK", "PLUS_MINUS_RANK", "NBA_FANTASY_PTS_RANK", "DD2_RANK", "TD3_RANK", "WNBA_FANTASY_PTS_RANK", "TEAM_COUNT"]]}, "LeagueDashTeamClutch": {"status": "SUCCESS", "dataframes": 1, "total_rows": 75, "parameters": {"season": "2024", "season_type_all_star": "Regular Season"}, "columns": [["TEAM_ID", "TEAM_NAME", "GP", "W", "L", "W_PCT", "MIN", "FGM", "FGA", "FG_PCT", "FG3M", "FG3A", "FG3_PCT", "FTM", "FTA", "FT_PCT", "OREB", "DREB", "REB", "AST", "TOV", "STL", "BLK", "BLKA", "PF", "PFD", "PTS", "PLUS_MINUS", "GP_RANK", "W_RANK", "L_RANK", "W_PCT_RANK", "MIN_RANK", "FGM_RANK", "FGA_RANK", "FG_PCT_RANK", "FG3M_RANK", "FG3A_RANK", "FG3_PCT_RANK", "FTM_RANK", "FTA_RANK", "FT_PCT_RANK", "OREB_RANK", "DREB_RANK", "REB_RANK", "AST_RANK", "TOV_RANK", "STL_RANK", "BLK_RANK", "BLKA_RANK", "PF_RANK", "PFD_RANK", "PTS_RANK", "PLUS_MINUS_RANK"]]}}