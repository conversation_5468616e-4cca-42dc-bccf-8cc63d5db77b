#!/usr/bin/env python3
"""
Test WNBA Player Game Log Collection
"""

from wnba_data_collector import WNBAComprehensiveCollector
import pandas as pd

def test_player_game_log_collection():
    """Test the player game log collection functionality"""
    print("🏀 Testing WNBA Player Game Log Collection 🏀")
    print("=" * 60)
    
    # Create collector instance
    collector = WNBAComprehensiveCollector()
    
    try:
        # First, collect some basic data (players)
        print("1. Collecting players data for 2024...")
        collector.seasons = ['2024']  # Limit to 2024 for testing
        collector.collect_players_data()
        
        # Check how many players we have
        player_count = collector.conn.execute("SELECT COUNT(*) FROM players WHERE season = '2024'").fetchone()[0]
        print(f"   Found {player_count} players for 2024 season")
        
        if player_count == 0:
            print("❌ No players found. Cannot test game log collection.")
            return
        
        # Get a sample of players
        cursor = collector.conn.execute("""
            SELECT player_id, player_name 
            FROM players 
            WHERE season = '2024' 
            LIMIT 5
        """)
        sample_players = cursor.fetchall()
        
        print(f"\n2. Testing game log collection for {len(sample_players)} sample players:")
        for player_id, player_name in sample_players:
            print(f"   • {player_name} (ID: {player_id})")
        
        # Test the game log collection
        print(f"\n3. Collecting player game logs...")
        collector.collect_player_game_logs()
        
        # Check results
        total_game_logs = collector.conn.execute("SELECT COUNT(*) FROM player_game_logs").fetchone()[0]
        print(f"   Total game logs collected: {total_game_logs}")
        
        if total_game_logs > 0:
            # Show some sample data
            print(f"\n4. Sample game log data:")
            cursor = collector.conn.execute("""
                SELECT player_name, game_date, matchup, pts, reb, ast, min
                FROM player_game_logs 
                ORDER BY game_date DESC 
                LIMIT 10
            """)
            
            print(f"   {'Player':<20} {'Date':<12} {'Matchup':<15} {'Pts':<4} {'Reb':<4} {'Ast':<4} {'Min':<4}")
            print(f"   {'-'*20} {'-'*12} {'-'*15} {'-'*4} {'-'*4} {'-'*4} {'-'*4}")
            
            for row in cursor.fetchall():
                player_name, game_date, matchup, pts, reb, ast, minutes = row
                print(f"   {player_name[:19]:<20} {game_date:<12} {matchup[:14]:<15} {pts or 0:<4} {reb or 0:<4} {ast or 0:<4} {minutes or 0:<4}")
            
            # Show player statistics
            print(f"\n5. Player game log statistics:")
            cursor = collector.conn.execute("""
                SELECT 
                    player_name,
                    COUNT(*) as games_played,
                    AVG(pts) as avg_pts,
                    MAX(pts) as max_pts,
                    AVG(reb) as avg_reb,
                    AVG(ast) as avg_ast
                FROM player_game_logs 
                GROUP BY player_id, player_name
                ORDER BY games_played DESC
                LIMIT 5
            """)
            
            print(f"   {'Player':<20} {'Games':<6} {'Avg Pts':<8} {'Max Pts':<8} {'Avg Reb':<8} {'Avg Ast':<8}")
            print(f"   {'-'*20} {'-'*6} {'-'*8} {'-'*8} {'-'*8} {'-'*8}")
            
            for row in cursor.fetchall():
                player_name, games, avg_pts, max_pts, avg_reb, avg_ast = row
                print(f"   {player_name[:19]:<20} {games:<6} {avg_pts:.1f:<8} {max_pts or 0:<8} {avg_reb:.1f:<8} {avg_ast:.1f:<8}")
            
            print(f"\n✅ Player game log collection test SUCCESSFUL!")
            print(f"   • Collected {total_game_logs} game log entries")
            print(f"   • Data includes points, rebounds, assists, and other stats")
            print(f"   • Game logs are properly linked to players and games")
            
        else:
            print(f"\n⚠️  Game log collection completed but no data was found")
            print(f"   This could be due to:")
            print(f"   • No games played yet in 2024 season")
            print(f"   • API access issues")
            print(f"   • Players not having game log data available")
        
        # Test export functionality
        print(f"\n6. Testing CSV export...")
        collector.export_to_csv()
        
        # Check if CSV was created
        import os
        csv_path = os.path.join(collector.data_dir, 'player_game_logs.csv')
        if os.path.exists(csv_path):
            df = pd.read_csv(csv_path)
            print(f"   ✅ CSV exported successfully: {len(df)} rows in {csv_path}")
        else:
            print(f"   ⚠️  CSV file not found (may be empty)")
        
        print(f"\n🎉 Test completed successfully!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
    finally:
        collector.close()

def test_player_game_log_endpoint_directly():
    """Test the PlayerGameLog endpoint directly"""
    print("\n" + "=" * 60)
    print("🔍 DIRECT ENDPOINT TEST")
    print("=" * 60)
    
    from nba_api.stats.endpoints import playergamelog, commonallplayers
    import time
    
    try:
        # Get a sample WNBA player
        print("Getting sample WNBA player...")
        players = commonallplayers.CommonAllPlayers(
            league_id='10',  # WNBA
            season='2024',
            is_only_current_season=1
        )
        players_df = players.get_data_frames()[0]
        
        if len(players_df) > 0:
            sample_player = players_df.iloc[0]
            player_id = sample_player['PERSON_ID']
            player_name = sample_player['DISPLAY_FIRST_LAST']
            
            print(f"Testing with player: {player_name} (ID: {player_id})")
            
            # Test PlayerGameLog endpoint
            time.sleep(1)  # Rate limiting
            game_logs = playergamelog.PlayerGameLog(
                player_id=player_id,
                season='2024',
                season_type_all_star='Regular Season'
            )
            
            df = game_logs.get_data_frames()[0]
            print(f"✅ PlayerGameLog endpoint works!")
            print(f"   • Returned {len(df)} game log entries")
            
            if len(df) > 0:
                print(f"   • Columns: {list(df.columns)}")
                print(f"   • Sample data:")
                for i, (_, row) in enumerate(df.head(3).iterrows()):
                    print(f"     Game {i+1}: {row.get('GAME_DATE')} vs {row.get('MATCHUP')} - {row.get('PTS')} pts")
            else:
                print(f"   • No game data found (player may not have played games yet)")
                
        else:
            print("❌ No WNBA players found")
            
    except Exception as e:
        print(f"❌ Direct endpoint test failed: {e}")

if __name__ == "__main__":
    test_player_game_log_collection()
    test_player_game_log_endpoint_directly()
