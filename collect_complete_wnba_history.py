#!/usr/bin/env python3
"""
Collect Complete WNBA History: 2015-2025
Every Player, Every Team, Every Game, Every Statistic
"""

from wnba_data_collector import WNBAComprehensiveCollector
import pandas as pd
import os
import time
from datetime import datetime

def collect_complete_wnba_history():
    """Collect comprehensive WNBA data from 2015-2025"""
    print("🏀 COLLECTING COMPLETE WNBA HISTORY: 2015-2025 🏀")
    print("=" * 100)
    print("📊 EVERY PLAYER • EVERY TEAM • EVERY GAME • EVERY STATISTIC")
    print("🎯 THE MOST COMPREHENSIVE WNBA DATABASE EVER CREATED")
    print("=" * 100)
    
    start_time = datetime.now()
    
    # Create collector instance
    collector = WNBAComprehensiveCollector()
    
    try:
        print(f"\n🚀 INITIALIZING COMPREHENSIVE COLLECTION")
        print(f"   • Seasons: {len(collector.seasons)} ({min(collector.seasons)}-{max(collector.seasons)})")
        print(f"   • Database: {collector.conn}")
        print(f"   • Start time: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Phase 1: Foundation Data (Teams & Players)
        print(f"\n📋 PHASE 1: FOUNDATION DATA COLLECTION")
        print("-" * 80)
        
        print("1. Collecting teams data across all seasons...")
        collector.collect_teams_data()
        
        print("2. Collecting players data across all seasons...")
        collector.collect_players_data()
        
        # Check foundation progress
        total_teams = collector.conn.execute("SELECT COUNT(*) FROM teams").fetchone()[0]
        total_players = collector.conn.execute("SELECT COUNT(*) FROM players").fetchone()[0]
        
        print(f"   ✅ Foundation Complete: {total_teams} team records | {total_players} player records")
        
        # Show season breakdown
        cursor = collector.conn.execute("""
            SELECT season, COUNT(DISTINCT team_id) as teams, COUNT(DISTINCT player_id) as players
            FROM (
                SELECT season, team_id, NULL as player_id FROM teams
                UNION ALL
                SELECT season, NULL as team_id, player_id FROM players
            )
            GROUP BY season
            ORDER BY season
        """)
        
        print("   📊 Season Breakdown:")
        for season, teams, players in cursor.fetchall():
            print(f"     • {season}: {teams if teams else 0} teams, {players if players else 0} players")
        
        # Phase 2: Game Data Collection
        print(f"\n🎮 PHASE 2: COMPREHENSIVE GAME DATA")
        print("-" * 80)
        
        print("3. Collecting player game logs...")
        collector.collect_player_game_logs()
        
        print("4. Collecting team game logs...")
        collector.collect_team_game_logs()
        
        print("5. Collecting league-wide game logs...")
        collector.collect_league_game_log()
        
        # Check game data progress
        player_logs = collector.conn.execute("SELECT COUNT(*) FROM player_game_logs").fetchone()[0]
        team_logs = collector.conn.execute("SELECT COUNT(*) FROM team_game_logs").fetchone()[0]
        league_logs = collector.conn.execute("SELECT COUNT(*) FROM league_game_log").fetchone()[0]
        
        print(f"   ✅ Game Data: {player_logs:,} player logs | {team_logs:,} team logs | {league_logs:,} league logs")
        
        # Phase 3: Roster & Organization Data
        print(f"\n👥 PHASE 3: ROSTER & ORGANIZATIONAL DATA")
        print("-" * 80)
        
        print("6. Collecting team rosters and coaching staff...")
        collector.collect_team_rosters()
        
        roster_players = collector.conn.execute("SELECT COUNT(*) FROM team_rosters").fetchone()[0]
        coaches = collector.conn.execute("SELECT COUNT(*) FROM team_coaches").fetchone()[0]
        
        print(f"   ✅ Organization: {roster_players:,} roster entries | {coaches:,} coaches")
        
        # Phase 4: Advanced Analytics
        print(f"\n📊 PHASE 4: ADVANCED ANALYTICS COLLECTION")
        print("-" * 80)
        
        print("7. Collecting player performance trends...")
        collector.collect_player_last_n_games_analytics()
        
        print("8. Collecting shot tracking analytics...")
        collector.collect_league_dash_player_pt_shot()
        collector.collect_league_dash_opp_pt_shot()
        
        print("9. Collecting clutch performance data...")
        collector.collect_league_dash_player_clutch()
        
        print("10. Collecting defensive analytics...")
        collector.collect_defense_hub_stats()
        collector.collect_league_dash_pt_team_defend()
        
        # Check advanced analytics
        player_trends = collector.conn.execute("SELECT COUNT(*) FROM player_last_n_games").fetchone()[0]
        player_shots = collector.conn.execute("SELECT COUNT(*) FROM league_dash_player_pt_shot").fetchone()[0]
        clutch_stats = collector.conn.execute("SELECT COUNT(*) FROM league_dash_player_clutch").fetchone()[0]
        defense_stats = collector.conn.execute("SELECT COUNT(*) FROM defense_hub_stats").fetchone()[0]
        
        print(f"   ✅ Analytics: {player_trends:,} trends | {player_shots:,} shot records | {clutch_stats:,} clutch | {defense_stats:,} defense")
        
        # Phase 5: Team Strategy & Lineup Analytics
        print(f"\n🎯 PHASE 5: TEAM STRATEGY & LINEUP ANALYTICS")
        print("-" * 80)
        
        print("11. Collecting lineup analytics...")
        collector.collect_league_dash_lineups()
        collector.collect_league_lineup_viz()
        
        print("12. Collecting matchup analytics...")
        collector.collect_team_vs_player_matchups()
        
        print("13. Collecting play type analytics...")
        collector.collect_synergy_play_types()
        
        lineups = collector.conn.execute("SELECT COUNT(*) FROM league_dash_lineups").fetchone()[0]
        lineup_viz = collector.conn.execute("SELECT COUNT(*) FROM league_lineup_viz").fetchone()[0]
        matchups = collector.conn.execute("SELECT COUNT(*) FROM team_vs_player_matchups").fetchone()[0]
        play_types = collector.conn.execute("SELECT COUNT(*) FROM synergy_play_types").fetchone()[0]
        
        print(f"   ✅ Strategy: {lineups:,} lineups | {lineup_viz:,} lineup viz | {matchups:,} matchups | {play_types:,} play types")
        
        # Phase 6: Season Progression & Playoff Data
        print(f"\n🏆 PHASE 6: SEASON PROGRESSION & PLAYOFF DATA")
        print("-" * 80)
        
        print("14. Collecting team streaks and patterns...")
        collector.collect_team_game_streaks()
        
        print("15. Collecting playoff picture data...")
        collector.collect_playoff_picture()
        
        streaks = collector.conn.execute("SELECT COUNT(*) FROM team_game_streaks").fetchone()[0]
        playoff_data = collector.conn.execute("SELECT COUNT(*) FROM playoff_picture").fetchone()[0]
        
        print(f"   ✅ Season Data: {streaks:,} streak records | {playoff_data:,} playoff records")
        
        # Phase 7: Game-Level Advanced Analytics
        print(f"\n⚡ PHASE 7: GAME-LEVEL ADVANCED ANALYTICS")
        print("-" * 80)
        
        print("16. Collecting game defensive matchups...")
        collector.collect_boxscore_defensive_v2()
        
        print("17. Collecting Four Factors analytics...")
        collector.collect_boxscore_four_factors_v2()
        
        print("18. Collecting usage rate analytics...")
        collector.collect_boxscore_usage_v2()
        
        print("19. Collecting player matchup analytics...")
        collector.collect_boxscore_matchups_v3()
        
        print("20. Collecting game rotations...")
        collector.collect_game_rotations()
        
        defensive_v2 = collector.conn.execute("SELECT COUNT(*) FROM boxscore_defensive_v2").fetchone()[0]
        four_factors = collector.conn.execute("SELECT COUNT(*) FROM boxscore_four_factors_v2").fetchone()[0]
        usage_data = collector.conn.execute("SELECT COUNT(*) FROM boxscore_usage_v2").fetchone()[0]
        matchup_data = collector.conn.execute("SELECT COUNT(*) FROM boxscore_matchups_v3").fetchone()[0]
        rotations = collector.conn.execute("SELECT COUNT(*) FROM game_rotations").fetchone()[0]
        
        print(f"   ✅ Game Analytics: {defensive_v2:,} defensive | {four_factors:,} four factors | {usage_data:,} usage | {matchup_data:,} matchups | {rotations:,} rotations")
        
        # Phase 8: Data Export and Validation
        print(f"\n📁 PHASE 8: DATA EXPORT & VALIDATION")
        print("-" * 80)
        
        print("21. Exporting comprehensive dataset...")
        collector.export_to_csv()
        
        # Calculate final statistics
        end_time = datetime.now()
        duration = end_time - start_time
        
        # Get comprehensive statistics
        all_tables = [
            'teams', 'players', 'player_game_logs', 'team_game_logs', 'league_game_log',
            'team_rosters', 'team_coaches', 'player_last_n_games', 'league_dash_player_pt_shot',
            'league_dash_opp_pt_shot', 'league_dash_player_clutch', 'defense_hub_stats',
            'league_dash_lineups', 'league_lineup_viz', 'team_vs_player_matchups',
            'synergy_play_types', 'team_game_streaks', 'playoff_picture',
            'boxscore_defensive_v2', 'boxscore_four_factors_v2', 'boxscore_usage_v2',
            'boxscore_matchups_v3', 'game_rotations'
        ]
        
        total_records = 0
        working_tables = 0
        
        print("\n📊 COMPREHENSIVE COLLECTION RESULTS:")
        print("=" * 100)
        
        for table in all_tables:
            try:
                count = collector.conn.execute(f"SELECT COUNT(*) FROM {table}").fetchone()[0]
                total_records += count
                if count > 0:
                    working_tables += 1
                print(f"   • {table.replace('_', ' ').title()}: {count:,} records")
            except:
                print(f"   • {table.replace('_', ' ').title()}: 0 records")
        
        # Season coverage analysis
        cursor = collector.conn.execute("""
            SELECT season, 
                   COUNT(DISTINCT CASE WHEN table_name = 'teams' THEN team_id END) as teams,
                   COUNT(DISTINCT CASE WHEN table_name = 'players' THEN player_id END) as players,
                   COUNT(DISTINCT CASE WHEN table_name = 'player_game_logs' THEN game_id END) as games
            FROM (
                SELECT season, team_id, NULL as player_id, NULL as game_id, 'teams' as table_name FROM teams
                UNION ALL
                SELECT season, NULL as team_id, player_id, NULL as game_id, 'players' as table_name FROM players
                UNION ALL
                SELECT season, NULL as team_id, NULL as player_id, game_id, 'player_game_logs' as table_name FROM player_game_logs
            )
            GROUP BY season
            ORDER BY season
        """)
        
        print(f"\n📈 SEASON COVERAGE ANALYSIS:")
        total_seasons_covered = 0
        for season, teams, players, games in cursor.fetchall():
            if teams > 0 or players > 0 or games > 0:
                total_seasons_covered += 1
                print(f"   • {season}: {teams} teams, {players} players, {games} games")
        
        print(f"\n🎉 COLLECTION COMPLETE!")
        print("=" * 100)
        print(f"   📊 Total Records: {total_records:,}")
        print(f"   📋 Working Tables: {working_tables}/{len(all_tables)} ({working_tables/len(all_tables)*100:.0f}%)")
        print(f"   📅 Seasons Covered: {total_seasons_covered}/11 (2015-2025)")
        print(f"   ⏱️  Duration: {duration}")
        print(f"   🏀 Database Size: {os.path.getsize('wnba_comprehensive.db') / (1024*1024):.1f} MB")
        
        print(f"\n🚀 WNBA ANALYTICS PLATFORM READY!")
        print(f"   • Complete historical data from 2015-2025")
        print(f"   • Every player, every team, every game")
        print(f"   • Advanced analytics and insights")
        print(f"   • Professional-grade data quality")
        print(f"   • Ready for analysis, reporting, and applications")
        
    except Exception as e:
        print(f"❌ Collection failed: {e}")
        import traceback
        traceback.print_exc()
    finally:
        collector.close()

if __name__ == "__main__":
    collect_complete_wnba_history()
