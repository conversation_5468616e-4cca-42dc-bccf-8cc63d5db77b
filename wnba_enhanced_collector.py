from nba_api.stats.endpoints import *
from nba_api.stats.static import teams, players
import pandas as pd
import sqlite3
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import json
import os
from concurrent.futures import ThreadPoolExecutor, as_completed
import traceback

# Import parameter classes
from nba_api.stats.library.parameters import (
    LeagueID, Season, SeasonType, SeasonTypeAllStar, PerModeDetailed,
    MeasureTypeDetailedDefense, PlayerOrTeam, Conference, Division,
    GameSegment, Period, LastNGames, Location, Outcome, SeasonSegment,
    DateFrom, DateTo, PlayerExperience, PlayerPosition, StarterBench,
    ShotClockRange, DefenseCategory, PtMeasureType, ContextMeasureDetailed,
    ClutchTime, AheadBehind, PointDiff, GameScope, Historical, Active
)

class WNBAEnhancedCollector:
    def __init__(self):
        self.setup_logging()
        self.setup_database()
        self.wnba_league_id = LeagueID.wnba
        self.seasons = self.generate_wnba_seasons(2015, 2025)
        self.season_types = [SeasonType.regular, SeasonType.preseason]
        if hasattr(SeasonTypeAllStar, 'playoffs'):
            self.season_types.append(SeasonTypeAllStar.playoffs)
        
    def setup_logging(self):
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('wnba_enhanced.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def setup_database(self):
        self.conn = sqlite3.connect('wnba_enhanced.db', check_same_thread=False)
        self.create_enhanced_tables()
        
    def create_enhanced_tables(self):
        tables = {
            'teams_comprehensive': '''CREATE TABLE IF NOT EXISTS teams_comprehensive (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                team_id TEXT,
                season TEXT,
                season_type TEXT,
                team_name TEXT,
                team_abbreviation TEXT,
                team_city TEXT,
                team_state TEXT,
                team_year_founded INTEGER,
                conference TEXT,
                division TEXT,
                wins INTEGER,
                losses INTEGER,
                win_pct REAL,
                conf_rank INTEGER,
                div_rank INTEGER,
                playoff_rank INTEGER,
                conf_wins INTEGER,
                conf_losses INTEGER,
                home_wins INTEGER,
                home_losses INTEGER,
                road_wins INTEGER,
                road_losses INTEGER,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(team_id, season, season_type)
            )''',
            
            'players_comprehensive': '''CREATE TABLE IF NOT EXISTS players_comprehensive (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                player_id TEXT,
                season TEXT,
                team_id TEXT,
                player_name TEXT,
                first_name TEXT,
                last_name TEXT,
                display_first_last TEXT,
                display_last_comma_first TEXT,
                display_fi_last TEXT,
                player_slug TEXT,
                birthdate TEXT,
                school TEXT,
                country TEXT,
                last_affiliation TEXT,
                height TEXT,
                weight TEXT,
                season_exp INTEGER,
                jersey TEXT,
                position TEXT,
                rosterstatus TEXT,
                games_played_current_season_flag TEXT,
                team_name TEXT,
                team_abbreviation TEXT,
                team_code TEXT,
                team_city TEXT,
                playercode TEXT,
                from_year INTEGER,
                to_year INTEGER,
                dleague_flag TEXT,
                nba_flag TEXT,
                games_played_flag TEXT,
                draft_year TEXT,
                draft_round TEXT,
                draft_number TEXT,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(player_id, season)
            )''',
            
            'games_comprehensive': '''CREATE