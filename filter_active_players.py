#!/usr/bin/env python3
"""
Filter Active Players Only
Identify and collect data only for players who actually played games
"""

import sqlite3
from wnba_data_collector import WN<PERSON><PERSON>omprehensiveCollector

def analyze_player_activity():
    """Analyze which players actually have game activity"""
    print("ANALYZING PLAYER ACTIVITY")
    print("=" * 60)
    
    collector = WNBAComprehensiveCollector()
    
    try:
        # Get total players
        total_players = collector.conn.execute("SELECT COUNT(*) FROM players").fetchone()[0]
        print(f"Total players in database: {total_players:,}")
        
        # Check how many players have game logs
        players_with_logs = collector.conn.execute("""
            SELECT COUNT(DISTINCT player_id) 
            FROM player_game_logs
        """).fetchone()[0]
        print(f"Players with game logs: {players_with_logs:,}")
        
        # Check players by season with activity
        cursor = collector.conn.execute("""
            SELECT p.season, 
                   COUNT(DISTINCT p.player_id) as total_players,
                   COUNT(DISTINCT pgl.player_id) as active_players
            FROM players p
            LEFT JOIN player_game_logs pgl ON p.player_id = pgl.player_id AND p.season = pgl.season
            GROUP BY p.season
            ORDER BY p.season
        """)
        
        print(f"\nPLAYER ACTIVITY BY SEASON:")
        total_active = 0
        total_inactive = 0
        
        for season, total, active in cursor.fetchall():
            inactive = total - (active or 0)
            total_active += (active or 0)
            total_inactive += inactive
            activity_rate = (active or 0) / total * 100 if total > 0 else 0
            print(f"  {season}: {active or 0:>3}/{total:>4} active ({activity_rate:>5.1f}%) | {inactive:>4} inactive")
        
        print(f"\nOVERALL SUMMARY:")
        print(f"  Total active players: {total_active:,}")
        print(f"  Total inactive players: {total_inactive:,}")
        print(f"  Activity rate: {total_active/(total_active+total_inactive)*100:.1f}%")
        
        # Sample inactive players
        cursor = collector.conn.execute("""
            SELECT p.player_name, p.season
            FROM players p
            LEFT JOIN player_game_logs pgl ON p.player_id = pgl.player_id AND p.season = pgl.season
            WHERE pgl.player_id IS NULL
            ORDER BY p.season DESC, p.player_name
            LIMIT 10
        """)
        
        print(f"\nSAMPLE INACTIVE PLAYERS:")
        for name, season in cursor.fetchall():
            print(f"  {season}: {name}")
        
        return total_active, total_inactive
        
    finally:
        collector.close()

def create_active_players_filter():
    """Create a method to filter only active players"""
    print(f"\nCREATING ACTIVE PLAYER FILTER")
    print("=" * 60)
    
    filter_code = '''
def get_active_players_only(self):
    """Get only players who have actual game activity or are likely to be active"""
    
    # Strategy 1: Players who already have game logs
    cursor = self.conn.execute("""
        SELECT DISTINCT p.player_id, p.player_name, p.season
        FROM players p
        INNER JOIN player_game_logs pgl ON p.player_id = pgl.player_id AND p.season = pgl.season
        ORDER BY p.season DESC, p.player_name
    """)
    active_players = cursor.fetchall()
    
    # Strategy 2: For current seasons (2024-2025), include all players as they may become active
    cursor = self.conn.execute("""
        SELECT DISTINCT p.player_id, p.player_name, p.season
        FROM players p
        WHERE p.season IN ('2024', '2025')
        AND p.player_id NOT IN (
            SELECT DISTINCT player_id FROM player_game_logs 
            WHERE season IN ('2024', '2025')
        )
        ORDER BY p.season DESC, p.player_name
    """)
    current_players = cursor.fetchall()
    
    # Strategy 3: For historical seasons, include players from team rosters
    cursor = self.conn.execute("""
        SELECT DISTINCT p.player_id, p.player_name, p.season
        FROM players p
        INNER JOIN team_rosters tr ON p.player_id = tr.player_id AND p.season = tr.season
        WHERE p.season NOT IN ('2024', '2025')
        AND p.player_id NOT IN (
            SELECT DISTINCT player_id FROM player_game_logs 
            WHERE season = p.season
        )
        ORDER BY p.season DESC, p.player_name
    """)
    roster_players = cursor.fetchall()
    
    # Combine all strategies
    all_active = list(set(active_players + current_players + roster_players))
    
    self.logger.info(f"Active player filter: {len(active_players)} with logs + {len(current_players)} current + {len(roster_players)} on rosters = {len(all_active)} total")
    
    return all_active
'''
    
    print("Filter strategy:")
    print("1. Players who already have game logs (proven active)")
    print("2. All players from current seasons (2024-2025) - may become active")
    print("3. Historical players who appear on team rosters")
    
    return filter_code

def update_collection_method():
    """Update the player game log collection to use active filter"""
    print(f"\nUPDATING COLLECTION METHOD")
    print("=" * 60)
    
    # Read current file
    with open('wnba_data_collector.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Find the player game logs method
    start_marker = "def collect_player_game_logs(self):"
    end_marker = "def collect_team_game_logs(self):"
    
    start_idx = content.find(start_marker)
    end_idx = content.find(end_marker)
    
    if start_idx == -1 or end_idx == -1:
        print("❌ Could not find player game logs method")
        return False
    
    # Extract the method
    method_content = content[start_idx:end_idx]
    
    # Replace the player selection logic
    old_logic = '''        # Get ALL players from the database (no limit!)
        try:
            cursor = self.conn.execute("""
                SELECT DISTINCT p.player_id, p.player_name, p.season
                FROM players p
                LEFT JOIN player_game_logs pgl ON p.player_id = pgl.player_id AND p.season = pgl.season
                WHERE pgl.player_id IS NULL
                ORDER BY p.season DESC, p.player_name
            """)
            missing_players = cursor.fetchall()
        except Exception:
            # Fallback: get all players (no limit!)
            cursor = self.conn.execute("SELECT DISTINCT player_id, player_name, season FROM players ORDER BY season DESC")
            missing_players = cursor.fetchall()'''
    
    new_logic = '''        # Get only ACTIVE players (smart filtering)
        try:
            # Get players who need game logs but filter for likely active players
            cursor = self.conn.execute("""
                SELECT DISTINCT p.player_id, p.player_name, p.season
                FROM players p
                LEFT JOIN player_game_logs pgl ON p.player_id = pgl.player_id AND p.season = pgl.season
                WHERE pgl.player_id IS NULL
                AND (
                    -- Current seasons: include all players
                    p.season IN ('2024', '2025')
                    OR
                    -- Historical seasons: only players on team rosters
                    EXISTS (
                        SELECT 1 FROM team_rosters tr 
                        WHERE tr.player_id = p.player_id AND tr.season = p.season
                    )
                    OR
                    -- Players who have logs in other seasons (career players)
                    EXISTS (
                        SELECT 1 FROM player_game_logs pgl2 
                        WHERE pgl2.player_id = p.player_id
                    )
                )
                ORDER BY p.season DESC, p.player_name
            """)
            missing_players = cursor.fetchall()
        except Exception:
            # Fallback: get current season players only
            cursor = self.conn.execute("""
                SELECT DISTINCT player_id, player_name, season 
                FROM players 
                WHERE season IN ('2024', '2025')
                ORDER BY season DESC
            """)
            missing_players = cursor.fetchall()'''
    
    if old_logic in method_content:
        new_method = method_content.replace(old_logic, new_logic)
        new_content = content[:start_idx] + new_method + content[end_idx:]
        
        # Write updated file
        with open('wnba_data_collector.py', 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print("✅ Updated player game log collection method")
        print("   Now filtering for active players only")
        return True
    else:
        print("❌ Could not find the exact logic to replace")
        return False

def estimate_improvement():
    """Estimate the improvement from filtering"""
    print(f"\nESTIMATING IMPROVEMENT")
    print("=" * 60)
    
    collector = WNBAComprehensiveCollector()
    
    try:
        # Count players that would be filtered out
        inactive_players = collector.conn.execute("""
            SELECT COUNT(*)
            FROM players p
            WHERE NOT EXISTS (
                SELECT 1 FROM player_game_logs pgl 
                WHERE pgl.player_id = p.player_id AND pgl.season = p.season
            )
            AND NOT (
                p.season IN ('2024', '2025')
                OR EXISTS (
                    SELECT 1 FROM team_rosters tr 
                    WHERE tr.player_id = p.player_id AND tr.season = p.season
                )
                OR EXISTS (
                    SELECT 1 FROM player_game_logs pgl2 
                    WHERE pgl2.player_id = p.player_id
                )
            )
        """).fetchone()[0]
        
        # Count players that would be included
        active_players = collector.conn.execute("""
            SELECT COUNT(*)
            FROM players p
            WHERE p.season IN ('2024', '2025')
            OR EXISTS (
                SELECT 1 FROM team_rosters tr 
                WHERE tr.player_id = p.player_id AND tr.season = p.season
            )
            OR EXISTS (
                SELECT 1 FROM player_game_logs pgl2 
                WHERE pgl2.player_id = p.player_id
            )
        """).fetchone()[0]
        
        total_players = collector.conn.execute("SELECT COUNT(*) FROM players").fetchone()[0]
        
        print(f"Current approach: {total_players:,} players")
        print(f"Filtered approach: {active_players:,} players")
        print(f"Players filtered out: {inactive_players:,}")
        print(f"Efficiency gain: {inactive_players/total_players*100:.1f}% reduction")
        print(f"Time savings: ~{inactive_players*6/3600:.1f} hours (assuming 6 sec per player)")
        
    finally:
        collector.close()

def main():
    """Main execution"""
    active, inactive = analyze_player_activity()
    create_active_players_filter()
    
    if update_collection_method():
        estimate_improvement()
        print(f"\n✅ PLAYER FILTERING IMPLEMENTED")
        print(f"   Ready to collect data for active players only")
        print(f"   Restart collection to see the improvement")
    else:
        print(f"\n❌ MANUAL UPDATE REQUIRED")
        print(f"   Please manually update the collection method")

if __name__ == "__main__":
    main()
