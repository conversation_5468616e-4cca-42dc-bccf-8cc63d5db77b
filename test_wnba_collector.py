#!/usr/bin/env python3
"""
Test script for WNBA data collector
"""

from wnba_data_collector import WNBAComprehensiveCollector
import os

def test_collector():
    """Test the WNBA collector with a small sample"""
    print("Testing WNBA Data Collector...")
    
    # Create collector instance
    collector = WNBAComprehensiveCollector()
    
    try:
        # Test basic functionality
        print(f"Seasons to collect: {collector.seasons}")
        print(f"WNBA League ID: {collector.wnba_league_id}")
        print(f"Base directory: {collector.base_dir}")
        print(f"Data directory: {collector.data_dir}")
        
        # Show initial progress
        print("\nInitial Progress Report:")
        collector.print_progress_report()
        
        # Test collecting teams data for just one recent season
        print("\nTesting teams data collection for 2024...")
        collector.seasons = ['2024']  # Limit to just 2024 for testing

        # Test basic data collection
        print("Collecting teams data...")
        collector.collect_teams_data()

        print("Collecting players data...")
        collector.collect_players_data()

        print("Collecting schedule and games...")
        collector.collect_schedule_and_games()
        
        # Show progress after basic data collection
        print("\nProgress after basic data collection:")
        collector.print_progress_report()

        # Test database queries
        print("\nTesting database queries...")

        # Check teams
        teams_count = collector.conn.execute("SELECT COUNT(*) FROM teams").fetchone()[0]
        print(f"Teams in database: {teams_count}")

        # Check players
        players_count = collector.conn.execute("SELECT COUNT(*) FROM players").fetchone()[0]
        print(f"Players in database: {players_count}")

        # Check games
        games_count = collector.conn.execute("SELECT COUNT(*) FROM games").fetchone()[0]
        print(f"Games in database: {games_count}")

        if teams_count > 0:
            # Show sample team data
            sample_teams = collector.conn.execute("SELECT team_name, season, w, l FROM teams LIMIT 5").fetchall()
            print("\nSample team data:")
            for team in sample_teams:
                print(f"  {team[0]} ({team[1]}): {team[2]}-{team[3]}")

        if players_count > 0:
            # Show sample player data
            sample_players = collector.conn.execute("SELECT player_name, team_id, position FROM players LIMIT 5").fetchall()
            print("\nSample player data:")
            for player in sample_players:
                print(f"  {player[0]} ({player[1]}) - {player[2]}")

        print("\nTest completed successfully!")
        
    except Exception as e:
        print(f"Test failed: {e}")
        import traceback
        traceback.print_exc()
    finally:
        collector.close()

if __name__ == "__main__":
    test_collector()
