#!/usr/bin/env python3
"""
Test script for WNBA data collector
"""

from wnba_data_collector import WNBAComprehensiveCollector
import os

def test_collector():
    """Test the WNBA collector with a small sample"""
    print("Testing WNBA Data Collector...")
    
    # Create collector instance
    collector = WNBAComprehensiveCollector()
    
    try:
        # Test basic functionality
        print(f"Seasons to collect: {collector.seasons}")
        print(f"WNBA League ID: {collector.wnba_league_id}")
        print(f"Base directory: {collector.base_dir}")
        print(f"Data directory: {collector.data_dir}")
        
        # Show initial progress
        print("\nInitial Progress Report:")
        collector.print_progress_report()
        
        # Test collecting teams data for just one recent season
        print("\nTesting teams data collection for 2024...")
        collector.seasons = ['2024']  # Limit to just 2024 for testing
        collector.collect_teams_data()
        
        # Show progress after teams collection
        print("\nProgress after teams collection:")
        collector.print_progress_report()
        
        # Test database queries
        print("\nTesting database queries...")
        teams_count = collector.conn.execute("SELECT COUNT(*) FROM teams").fetchone()[0]
        print(f"Teams in database: {teams_count}")
        
        if teams_count > 0:
            # Show sample team data
            sample_teams = collector.conn.execute("SELECT team_name, season, w, l FROM teams LIMIT 5").fetchall()
            print("\nSample team data:")
            for team in sample_teams:
                print(f"  {team[0]} ({team[1]}): {team[2]}-{team[3]}")
        
        print("\nTest completed successfully!")
        
    except Exception as e:
        print(f"Test failed: {e}")
        import traceback
        traceback.print_exc()
    finally:
        collector.close()

if __name__ == "__main__":
    test_collector()
