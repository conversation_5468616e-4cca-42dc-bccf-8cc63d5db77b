#!/usr/bin/env python3
"""
Test Complete WNBA Data Collection System
Including game logs, rosters, defense stats, lineups, and rotations
"""

from wnba_data_collector import WNBAComprehensiveCollector
import pandas as pd
import os

def test_complete_wnba_system():
    """Test the complete WNBA data collection system"""
    print("🏀 TESTING COMPLETE WNBA DATA COLLECTION SYSTEM 🏀")
    print("=" * 80)
    
    # Create collector instance
    collector = WNBAComprehensiveCollector()
    
    try:
        # Focus on 2024 for comprehensive testing
        collector.seasons = ['2024']
        
        print("PHASE 1: BASIC DATA COLLECTION")
        print("-" * 50)
        
        print("1. Collecting teams and players...")
        collector.collect_teams_data()
        collector.collect_players_data()
        
        # Check basic data
        team_count = collector.conn.execute("SELECT COUNT(*) FROM teams WHERE season = '2024'").fetchone()[0]
        player_count = collector.conn.execute("SELECT COUNT(*) FROM players WHERE season = '2024'").fetchone()[0]
        print(f"   ✅ Found {team_count} teams and {player_count} players for 2024")
        
        if team_count == 0 or player_count == 0:
            print("❌ Insufficient basic data. Cannot proceed with comprehensive testing.")
            return
        
        print("\nPHASE 2: GAME LOG COLLECTION")
        print("-" * 50)
        
        print("2. Collecting player game logs...")
        collector.collect_player_game_logs()
        
        print("3. Collecting team game logs...")
        collector.collect_team_game_logs()
        
        # Check game log data
        player_logs = collector.conn.execute("SELECT COUNT(*) FROM player_game_logs").fetchone()[0]
        team_logs = collector.conn.execute("SELECT COUNT(*) FROM team_game_logs").fetchone()[0]
        print(f"   ✅ Collected {player_logs} player game logs and {team_logs} team game logs")
        
        print("\nPHASE 3: ROSTER COLLECTION")
        print("-" * 50)
        
        print("4. Collecting team rosters (players and coaches)...")
        collector.collect_team_rosters()
        
        # Check roster data
        roster_players = collector.conn.execute("SELECT COUNT(*) FROM team_rosters").fetchone()[0]
        coaches = collector.conn.execute("SELECT COUNT(*) FROM team_coaches").fetchone()[0]
        print(f"   ✅ Collected {roster_players} roster entries and {coaches} coaches")
        
        if roster_players > 0:
            # Show sample roster data
            cursor = collector.conn.execute("""
                SELECT tr.team_id, t.team_name, COUNT(*) as player_count
                FROM team_rosters tr
                JOIN teams t ON tr.team_id = t.team_id AND tr.season = t.season
                WHERE tr.season = '2024'
                GROUP BY tr.team_id, t.team_name
                ORDER BY player_count DESC
                LIMIT 5
            """)
            print("   Top teams by roster size:")
            for team_id, team_name, player_count in cursor.fetchall():
                print(f"     • {team_name}: {player_count} players")
        
        if coaches > 0:
            # Show sample coach data
            cursor = collector.conn.execute("""
                SELECT tc.team_id, t.team_name, COUNT(*) as coach_count,
                       COUNT(CASE WHEN tc.is_assistant = 0 THEN 1 END) as head_coaches
                FROM team_coaches tc
                JOIN teams t ON tc.team_id = t.team_id AND tc.season = t.season
                WHERE tc.season = '2024'
                GROUP BY tc.team_id, t.team_name
                ORDER BY coach_count DESC
                LIMIT 5
            """)
            print("   Teams with coaching staff:")
            for team_id, team_name, coach_count, head_coaches in cursor.fetchall():
                print(f"     • {team_name}: {coach_count} coaches ({head_coaches} head)")
        
        print("\nPHASE 4: ADVANCED ANALYTICS")
        print("-" * 50)
        
        print("5. Collecting defense hub stats...")
        collector.collect_defense_hub_stats()
        
        print("6. Collecting lineups data...")
        collector.collect_lineups_data()
        
        print("7. Collecting game rotations...")
        collector.collect_game_rotations()
        
        # Check advanced data
        defense_stats = collector.conn.execute("SELECT COUNT(*) FROM defense_hub_stats").fetchone()[0]
        lineups = collector.conn.execute("SELECT COUNT(*) FROM lineups").fetchone()[0]
        rotations = collector.conn.execute("SELECT COUNT(*) FROM game_rotations").fetchone()[0]
        
        print(f"   ✅ Collected {defense_stats} defense stats, {lineups} lineups, {rotations} rotations")
        
        print("\nPHASE 5: DATA ANALYSIS")
        print("-" * 50)
        
        # Comprehensive data analysis
        print("8. Analyzing collected data...")
        
        # Player performance analysis
        if player_logs > 0:
            cursor = collector.conn.execute("""
                SELECT 
                    player_name,
                    COUNT(*) as games_played,
                    AVG(pts) as avg_points,
                    MAX(pts) as max_points,
                    AVG(reb) as avg_rebounds,
                    AVG(ast) as avg_assists
                FROM player_game_logs 
                WHERE season = '2024'
                GROUP BY player_id, player_name
                HAVING games_played >= 5
                ORDER BY avg_points DESC
                LIMIT 5
            """)
            
            print("\n   🏆 TOP PERFORMERS (min 5 games):")
            for player_name, games, avg_pts, max_pts, avg_reb, avg_ast in cursor.fetchall():
                print(f"     • {player_name}: {avg_pts:.1f} ppg, {avg_reb:.1f} rpg, {avg_ast:.1f} apg ({games} games)")
        
        # Team performance analysis
        if team_logs > 0:
            cursor = collector.conn.execute("""
                SELECT 
                    team_name,
                    COUNT(*) as games_played,
                    AVG(pts) as avg_points,
                    SUM(CASE WHEN wl = 'W' THEN 1 ELSE 0 END) as wins,
                    COUNT(*) - SUM(CASE WHEN wl = 'W' THEN 1 ELSE 0 END) as losses
                FROM team_game_logs 
                WHERE season = '2024'
                GROUP BY team_id, team_name
                ORDER BY wins DESC, avg_points DESC
                LIMIT 5
            """)
            
            print("\n   🏆 TOP TEAMS:")
            for team_name, games, avg_pts, wins, losses in cursor.fetchall():
                win_pct = wins / games if games > 0 else 0
                print(f"     • {team_name}: {wins}-{losses} ({win_pct:.1%}), {avg_pts:.1f} ppg")
        
        # Cross-reference analysis
        if player_logs > 0 and roster_players > 0:
            cursor = collector.conn.execute("""
                SELECT 
                    t.team_name,
                    COUNT(DISTINCT tr.player_id) as roster_size,
                    COUNT(DISTINCT pgl.player_id) as active_players,
                    AVG(pgl.pts) as team_avg_pts
                FROM teams t
                LEFT JOIN team_rosters tr ON t.team_id = tr.team_id AND t.season = tr.season
                LEFT JOIN player_game_logs pgl ON tr.player_id = pgl.player_id AND tr.season = pgl.season
                WHERE t.season = '2024'
                GROUP BY t.team_id, t.team_name
                HAVING roster_size > 0
                ORDER BY active_players DESC
                LIMIT 5
            """)
            
            print("\n   📊 TEAM ACTIVITY ANALYSIS:")
            for team_name, roster_size, active_players, team_avg_pts in cursor.fetchall():
                activity_rate = (active_players / roster_size * 100) if roster_size > 0 else 0
                print(f"     • {team_name}: {active_players}/{roster_size} active ({activity_rate:.0f}%), {team_avg_pts:.1f} avg pts")
        
        print("\nPHASE 6: EXPORT AND SUMMARY")
        print("-" * 50)
        
        print("9. Exporting to CSV files...")
        collector.export_to_csv()
        
        # Check exported files
        expected_files = [
            'teams.csv', 'players.csv', 'player_game_logs.csv', 'team_game_logs.csv',
            'team_rosters.csv', 'team_coaches.csv', 'defense_hub_stats.csv',
            'lineups.csv', 'game_rotations.csv'
        ]
        
        exported_files = []
        for csv_file in expected_files:
            csv_path = os.path.join(collector.data_dir, csv_file)
            if os.path.exists(csv_path):
                df = pd.read_csv(csv_path)
                if len(df) > 0:
                    exported_files.append((csv_file, len(df)))
                    print(f"   ✅ {csv_file}: {len(df)} rows")
                else:
                    print(f"   ⚠️  {csv_file}: Empty file")
            else:
                print(f"   ❌ {csv_file}: Not found")
        
        print("\nFINAL ASSESSMENT")
        print("=" * 80)
        
        # Calculate total data collected
        total_counts = {}
        all_tables = ['teams', 'players', 'player_game_logs', 'team_game_logs', 
                     'team_rosters', 'team_coaches', 'defense_hub_stats', 
                     'lineups', 'game_rotations']
        
        for table in all_tables:
            try:
                count = collector.conn.execute(f"SELECT COUNT(*) FROM {table}").fetchone()[0]
                total_counts[table] = count
            except:
                total_counts[table] = 0
        
        total_records = sum(total_counts.values())
        working_components = sum(1 for count in total_counts.values() if count > 0)
        
        print(f"📊 COMPREHENSIVE RESULTS:")
        print(f"   • Total records collected: {total_records:,}")
        print(f"   • Working components: {working_components}/{len(all_tables)}")
        print(f"   • CSV files exported: {len(exported_files)}")
        
        print(f"\n📈 DETAILED BREAKDOWN:")
        for table, count in total_counts.items():
            status = "✅" if count > 0 else "❌"
            print(f"   {status} {table.replace('_', ' ').title()}: {count:,}")
        
        # Overall assessment
        if working_components >= 7:
            print(f"\n🎉 EXCELLENT! Comprehensive WNBA data collection system is working!")
            print(f"   • Game logs: {'✅' if player_logs > 0 and team_logs > 0 else '❌'}")
            print(f"   • Rosters: {'✅' if roster_players > 0 else '❌'}")
            print(f"   • Advanced stats: {'✅' if defense_stats > 0 or lineups > 0 else '❌'}")
            print(f"   • Ready for comprehensive WNBA analysis and reporting!")
        elif working_components >= 4:
            print(f"\n✅ GOOD! Core functionality is working")
            print(f"   • Most essential data collection is functional")
            print(f"   • Some advanced features may need attention")
        else:
            print(f"\n⚠️  LIMITED SUCCESS")
            print(f"   • Basic data collection working")
            print(f"   • Advanced features may need investigation")
        
        print(f"\n🔧 SYSTEM CAPABILITIES:")
        print(f"   • Player tracking and performance analysis")
        print(f"   • Team composition and coaching staff")
        print(f"   • Game-by-game detailed statistics")
        print(f"   • Defensive analytics and lineup optimization")
        print(f"   • Historical trend analysis")
        print(f"   • Export capabilities for external analysis")
        
    except Exception as e:
        print(f"❌ System test failed: {e}")
        import traceback
        traceback.print_exc()
    finally:
        collector.close()

if __name__ == "__main__":
    test_complete_wnba_system()
