#!/usr/bin/env python3
"""
Debug script to test individual WNBA endpoints
"""

from nba_api.stats.endpoints import *
import pandas as pd
import time

def test_endpoint(endpoint_func, endpoint_name, **kwargs):
    """Test a single endpoint"""
    print(f"\nTesting {endpoint_name}...")
    print(f"Parameters: {kwargs}")
    
    try:
        time.sleep(0.6)  # Rate limiting
        result = endpoint_func(**kwargs)
        
        if result:
            data_frames = result.get_data_frames()
            print(f"Success! Got {len(data_frames)} data frames")
            
            for i, df in enumerate(data_frames):
                print(f"  DataFrame {i}: {len(df)} rows, {len(df.columns)} columns")
                if len(df) > 0:
                    print(f"  Columns: {list(df.columns)[:10]}...")  # Show first 10 columns
                    print(f"  Sample data:")
                    print(df.head(2).to_string())
                else:
                    print(f"  DataFrame {i} is empty")
            
            return True
        else:
            print("Failed: No result returned")
            return False
            
    except Exception as e:
        print(f"Error: {e}")
        return False

def main():
    """Test various WNBA endpoints"""
    wnba_league_id = '10'
    season = '2024'
    
    print("Testing WNBA API Endpoints")
    print("=" * 50)
    
    # Test teams/standings
    print("\n" + "=" * 30)
    print("TESTING TEAMS/STANDINGS")
    print("=" * 30)
    
    test_endpoint(
        leaguestandings.LeagueStandings,
        "League Standings",
        league_id=wnba_league_id,
        season=season,
        season_type='Regular Season'
    )
    
    # Test players
    print("\n" + "=" * 30)
    print("TESTING PLAYERS")
    print("=" * 30)
    
    test_endpoint(
        commonallplayers.CommonAllPlayers,
        "Common All Players",
        league_id=wnba_league_id,
        season=season,
        is_only_current_season=0
    )
    
    # Test schedule
    print("\n" + "=" * 30)
    print("TESTING SCHEDULE")
    print("=" * 30)
    
    test_endpoint(
        scheduleleaguev2.ScheduleLeagueV2,
        "Schedule League V2",
        league_id=wnba_league_id,
        season=season
    )
    
    # Test team stats (without league_id)
    print("\n" + "=" * 30)
    print("TESTING TEAM STATS")
    print("=" * 30)

    test_endpoint(
        leaguedashteamstats.LeagueDashTeamStats,
        "League Dash Team Stats",
        season=season,
        season_type_all_star='Regular Season'
    )

    # Test hustle stats (without league_id)
    print("\n" + "=" * 30)
    print("TESTING HUSTLE STATS")
    print("=" * 30)

    test_endpoint(
        leaguehustlestatsplayer.LeagueHustleStatsPlayer,
        "League Hustle Stats Player",
        season=season,
        season_type_all_star='Regular Season'
    )

    # Test alternative schedule endpoint
    print("\n" + "=" * 30)
    print("TESTING ALTERNATIVE SCHEDULE")
    print("=" * 30)

    # Try scoreboardv2 for a specific date
    from datetime import datetime, timedelta
    test_date = datetime(2024, 5, 15)  # Mid-season date

    test_endpoint(
        scoreboardv2.ScoreboardV2,
        "Scoreboard V2",
        league_id=wnba_league_id,
        game_date=test_date.strftime('%m/%d/%Y')
    )

if __name__ == "__main__":
    main()
