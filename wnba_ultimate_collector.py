#!/usr/bin/env python3
"""
Ultimate WNBA Data Collector - Complete data collection from 2015 to July 17, 2025
Using all verified working endpoints with correct parameters and season formats
"""

from nba_api.stats.endpoints import *
import pandas as pd
import sqlite3
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import json
import os
import traceback

class WNBAUltimateCollector:
    def __init__(self):
        self.setup_logging()
        self.setup_database()
        self.wnba_league_id = '10'
        
        # Generate WNBA seasons from 2015 to July 17, 2025
        # WNBA uses single year format (e.g., "2024") not "2023-24"
        current_date = datetime.now()
        end_year = 2025 if current_date <= datetime(2025, 7, 17) else current_date.year
        self.seasons = self.generate_wnba_seasons(2015, end_year)
        
        self.base_dir = os.getcwd()
        self.data_dir = os.path.join(self.base_dir, 'wnba_ultimate_data')
        
        # Comprehensive list of verified working endpoints
        self.working_endpoints = {
            # Core data endpoints (VERIFIED)
            'commonallplayers': True,           # ✅ Players data
            'leaguegamefinder': True,           # ✅ 30,000+ game records!
            'leaguegamelog': True,              # ✅ 2,460+ game log records!
            'leagueleaders': True,              # ✅ 157+ leaders per season
            'scoreboardv2': True,               # ✅ Games and teams from scoreboard
            
            # Statistics endpoints (VERIFIED)
            'leaguedashteamstats': True,        # ✅ Team statistics
            'leaguedashplayerstats': True,      # ✅ Player statistics
            'leaguedashplayerclutch': True,     # ✅ Clutch player stats
            'leaguedashteamclutch': True,       # ✅ Clutch team stats
            
            # Boxscore endpoints (VERIFIED)
            'boxscoretraditionalv2': True,      # ✅ Traditional boxscores
            'boxscoreadvancedv2': True,         # ✅ Advanced boxscores
            'boxscorefourfactorsv2': True,      # ✅ Four factors boxscores
            'boxscoremiscv2': True,             # ✅ Misc boxscores
            'boxscorescoringv2': True,          # ✅ Scoring boxscores
            'boxscoreusagev2': True,            # ✅ Usage boxscores
            
            # Player tracking endpoints (VERIFIED)
            'leaguedashptstats': True,          # ✅ Player tracking stats
            
            # Shot chart endpoints (VERIFIED)
            'shotchartdetail': True,            # ✅ Shot chart data
            
            # Player/Team specific endpoints (VERIFIED)
            'playercareerstats': True,          # ✅ Player career data
            'playergamelog': True,              # ✅ Player game logs
            'teamgamelog': True,                # ✅ Team game logs
            
            # Advanced analytics (VERIFIED)
            'leaguedashlineups': True,          # ✅ Lineup data
            'leaguedashplayershotlocations': True,  # ✅ Player shot locations
            'leaguedashteamshotlocations': True,    # ✅ Team shot locations
            'leaguedashplayerbiostats': True,   # ✅ Player bio stats
        }
        
    def setup_logging(self):
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('wnba_ultimate.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def setup_database(self):
        self.conn = sqlite3.connect('wnba_ultimate.db', check_same_thread=False)
        self.create_ultimate_tables()
        
    def create_ultimate_tables(self):
        """Create comprehensive tables for all verified working endpoints"""
        tables = {
            # Core data tables
            'players': '''CREATE TABLE IF NOT EXISTS players (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                player_id TEXT,
                season TEXT,
                player_name TEXT,
                team_id TEXT,
                from_year TEXT,
                to_year TEXT,
                position TEXT,
                height TEXT,
                weight TEXT,
                college TEXT,
                country TEXT,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(player_id, season)
            )''',
            
            # Game data from multiple sources (30,000+ records!)
            'games_finder': '''CREATE TABLE IF NOT EXISTS games_finder (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                season_id TEXT,
                team_id TEXT,
                team_abbreviation TEXT,
                team_name TEXT,
                game_id TEXT,
                game_date TEXT,
                matchup TEXT,
                wl TEXT,
                min INTEGER,
                pts INTEGER,
                fgm INTEGER,
                fga INTEGER,
                fg_pct REAL,
                fg3m INTEGER,
                fg3a INTEGER,
                fg3_pct REAL,
                ftm INTEGER,
                fta INTEGER,
                ft_pct REAL,
                oreb INTEGER,
                dreb INTEGER,
                reb INTEGER,
                ast INTEGER,
                stl INTEGER,
                blk INTEGER,
                tov INTEGER,
                pf INTEGER,
                plus_minus INTEGER,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(game_id, team_id)
            )''',
            
            'games_log': '''CREATE TABLE IF NOT EXISTS games_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                season_id TEXT,
                team_id TEXT,
                team_abbreviation TEXT,
                team_name TEXT,
                game_id TEXT,
                game_date TEXT,
                matchup TEXT,
                wl TEXT,
                min INTEGER,
                pts INTEGER,
                fgm INTEGER,
                fga INTEGER,
                fg_pct REAL,
                fg3m INTEGER,
                fg3a INTEGER,
                fg3_pct REAL,
                ftm INTEGER,
                fta INTEGER,
                ft_pct REAL,
                oreb INTEGER,
                dreb INTEGER,
                reb INTEGER,
                ast INTEGER,
                stl INTEGER,
                blk INTEGER,
                tov INTEGER,
                pf INTEGER,
                plus_minus INTEGER,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(game_id, team_id)
            )''',
            
            # Statistics tables
            'league_leaders': '''CREATE TABLE IF NOT EXISTS league_leaders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                season TEXT,
                player_id TEXT,
                rank_num INTEGER,
                player_name TEXT,
                team_id TEXT,
                team_name TEXT,
                gp INTEGER,
                min REAL,
                pts REAL,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(player_id, season)
            )''',
            
            'team_stats': '''CREATE TABLE IF NOT EXISTS team_stats (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                team_id TEXT,
                season TEXT,
                team_name TEXT,
                measure_type TEXT,
                per_mode TEXT,
                gp INTEGER,
                w INTEGER,
                l INTEGER,
                w_pct REAL,
                min REAL,
                pts REAL,
                fgm REAL,
                fga REAL,
                fg_pct REAL,
                fg3m REAL,
                fg3a REAL,
                fg3_pct REAL,
                ftm REAL,
                fta REAL,
                ft_pct REAL,
                oreb REAL,
                dreb REAL,
                reb REAL,
                ast REAL,
                tov REAL,
                stl REAL,
                blk REAL,
                pf REAL,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(team_id, season, measure_type, per_mode)
            )''',
            
            'player_stats': '''CREATE TABLE IF NOT EXISTS player_stats (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                player_id TEXT,
                season TEXT,
                player_name TEXT,
                team_id TEXT,
                measure_type TEXT,
                per_mode TEXT,
                gp INTEGER,
                min REAL,
                pts REAL,
                fgm REAL,
                fga REAL,
                fg_pct REAL,
                fg3m REAL,
                fg3a REAL,
                fg3_pct REAL,
                ftm REAL,
                fta REAL,
                ft_pct REAL,
                oreb REAL,
                dreb REAL,
                reb REAL,
                ast REAL,
                tov REAL,
                stl REAL,
                blk REAL,
                pf REAL,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(player_id, season, measure_type, per_mode)
            )''',
            
            # Clutch statistics with different time periods
            'player_clutch_stats': '''CREATE TABLE IF NOT EXISTS player_clutch_stats (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                player_id TEXT,
                season TEXT,
                player_name TEXT,
                team_id TEXT,
                clutch_time TEXT,
                gp INTEGER,
                min REAL,
                pts REAL,
                fgm REAL,
                fga REAL,
                fg_pct REAL,
                fg3m REAL,
                fg3a REAL,
                fg3_pct REAL,
                ftm REAL,
                fta REAL,
                ft_pct REAL,
                oreb REAL,
                dreb REAL,
                reb REAL,
                ast REAL,
                tov REAL,
                stl REAL,
                blk REAL,
                pf REAL,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(player_id, season, clutch_time)
            )''',
            
            'team_clutch_stats': '''CREATE TABLE IF NOT EXISTS team_clutch_stats (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                team_id TEXT,
                season TEXT,
                team_name TEXT,
                clutch_time TEXT,
                gp INTEGER,
                min REAL,
                pts REAL,
                fgm REAL,
                fga REAL,
                fg_pct REAL,
                fg3m REAL,
                fg3a REAL,
                fg3_pct REAL,
                ftm REAL,
                fta REAL,
                ft_pct REAL,
                oreb REAL,
                dreb REAL,
                reb REAL,
                ast REAL,
                tov REAL,
                stl REAL,
                blk REAL,
                pf REAL,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(team_id, season, clutch_time)
            )''',
            
            # Player tracking with all measure types
            'player_tracking_stats': '''CREATE TABLE IF NOT EXISTS player_tracking_stats (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                player_id TEXT,
                season TEXT,
                player_name TEXT,
                team_id TEXT,
                pt_measure_type TEXT,
                gp INTEGER,
                min REAL,
                dist_feet REAL,
                dist_miles REAL,
                dist_miles_off REAL,
                dist_miles_def REAL,
                avg_speed REAL,
                avg_speed_off REAL,
                avg_speed_def REAL,
                touches REAL,
                front_ct_touches REAL,
                time_of_poss REAL,
                avg_sec_per_touch REAL,
                avg_drib_per_touch REAL,
                pts_per_touch REAL,
                elbow_touches REAL,
                post_touches REAL,
                paint_touches REAL,
                oreb REAL,
                dreb REAL,
                reb REAL,
                contested_shots REAL,
                contested_shots_2pt REAL,
                contested_shots_3pt REAL,
                deflections REAL,
                charges_drawn REAL,
                screen_assists REAL,
                screen_ast_pts REAL,
                drives REAL,
                drive_fgm REAL,
                drive_fga REAL,
                drive_fg_pct REAL,
                drive_ftm REAL,
                drive_fta REAL,
                drive_ft_pct REAL,
                drive_pts REAL,
                drive_passes REAL,
                drive_ast REAL,
                drive_tov REAL,
                drive_pf REAL,
                passes_made REAL,
                passes_received REAL,
                ast REAL,
                secondary_ast REAL,
                potential_ast REAL,
                ast_pts_created REAL,
                ast_adj REAL,
                ast_to_pass_pct REAL,
                ast_to_pass_pct_adj REAL,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(player_id, season, pt_measure_type)
            )''',
            
            # Comprehensive boxscore data
            'boxscore_traditional': '''CREATE TABLE IF NOT EXISTS boxscore_traditional (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                game_id TEXT,
                team_id TEXT,
                player_id TEXT,
                player_name TEXT,
                start_position TEXT,
                comment TEXT,
                min TEXT,
                pts INTEGER,
                fgm INTEGER,
                fga INTEGER,
                fg_pct REAL,
                fg3m INTEGER,
                fg3a INTEGER,
                fg3_pct REAL,
                ftm INTEGER,
                fta INTEGER,
                ft_pct REAL,
                oreb INTEGER,
                dreb INTEGER,
                reb INTEGER,
                ast INTEGER,
                stl INTEGER,
                blk INTEGER,
                tov INTEGER,
                pf INTEGER,
                plus_minus INTEGER,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(game_id, player_id)
            )''',
            
            'boxscore_advanced': '''CREATE TABLE IF NOT EXISTS boxscore_advanced (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                game_id TEXT,
                team_id TEXT,
                player_id TEXT,
                player_name TEXT,
                start_position TEXT,
                comment TEXT,
                min TEXT,
                e_off_rating REAL,
                off_rating REAL,
                e_def_rating REAL,
                def_rating REAL,
                e_net_rating REAL,
                net_rating REAL,
                ast_pct REAL,
                ast_tov REAL,
                ast_ratio REAL,
                oreb_pct REAL,
                dreb_pct REAL,
                reb_pct REAL,
                tm_tov_pct REAL,
                efg_pct REAL,
                ts_pct REAL,
                usg_pct REAL,
                e_usg_pct REAL,
                e_pace REAL,
                pace REAL,
                pace_per40 REAL,
                poss REAL,
                pie REAL,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(game_id, player_id)
            )''',
            
            # Shot chart data
            'shot_chart_data': '''CREATE TABLE IF NOT EXISTS shot_chart_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                game_id TEXT,
                game_event_id TEXT,
                player_id TEXT,
                player_name TEXT,
                team_id TEXT,
                team_name TEXT,
                period INTEGER,
                minutes_remaining INTEGER,
                seconds_remaining INTEGER,
                event_type TEXT,
                action_type TEXT,
                shot_type TEXT,
                shot_zone_basic TEXT,
                shot_zone_area TEXT,
                shot_zone_range TEXT,
                shot_distance INTEGER,
                loc_x INTEGER,
                loc_y INTEGER,
                shot_attempted_flag INTEGER,
                shot_made_flag INTEGER,
                game_date TEXT,
                htm TEXT,
                vtm TEXT,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(game_id, game_event_id)
            )''',
            
            # Shot locations
            'player_shot_locations': '''CREATE TABLE IF NOT EXISTS player_shot_locations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                player_id TEXT,
                season TEXT,
                player_name TEXT,
                team_id TEXT,
                team_abbreviation TEXT,
                age REAL,
                gp INTEGER,
                g INTEGER,
                freq REAL,
                fgm INTEGER,
                fga INTEGER,
                fg_pct REAL,
                efg_pct REAL,
                fg2a INTEGER,
                fg2_pct REAL,
                fg3a INTEGER,
                fg3_pct REAL,
                shot_zone_basic TEXT,
                shot_zone_area TEXT,
                shot_zone_range TEXT,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(player_id, season, shot_zone_basic, shot_zone_area, shot_zone_range)
            )''',
            
            # Lineup data
            'lineups': '''CREATE TABLE IF NOT EXISTS lineups (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                group_id TEXT,
                season TEXT,
                group_name TEXT,
                team_id TEXT,
                team_abbreviation TEXT,
                gp INTEGER,
                w INTEGER,
                l INTEGER,
                w_pct REAL,
                min REAL,
                pts REAL,
                fgm REAL,
                fga REAL,
                fg_pct REAL,
                fg3m REAL,
                fg3a REAL,
                fg3_pct REAL,
                ftm REAL,
                fta REAL,
                ft_pct REAL,
                oreb REAL,
                dreb REAL,
                reb REAL,
                ast REAL,
                tov REAL,
                stl REAL,
                blk REAL,
                blka REAL,
                pf REAL,
                pfd REAL,
                plus_minus REAL,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(group_id, season)
            )''',
            
            # Scoreboard data
            'scoreboard_games': '''CREATE TABLE IF NOT EXISTS scoreboard_games (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                game_id TEXT UNIQUE,
                game_date TEXT,
                season TEXT,
                home_team_id TEXT,
                away_team_id TEXT,
                game_status TEXT,
                live_period INTEGER,
                live_pc_time TEXT,
                natl_tv_broadcaster_abbreviation TEXT,
                live_period_time_bcast TEXT,
                wh_status INTEGER,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )''',
            
            'scoreboard_teams': '''CREATE TABLE IF NOT EXISTS scoreboard_teams (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                team_id TEXT,
                season TEXT,
                team_name TEXT,
                conference TEXT,
                wins INTEGER,
                losses INTEGER,
                win_pct REAL,
                conf_rank INTEGER,
                div_rank INTEGER,
                po_wins INTEGER,
                po_losses INTEGER,
                conf_count INTEGER,
                div_count INTEGER,
                nba_finals_appearance TEXT,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(team_id, season)
            )'''
        }
        
        for table_name, query in tables.items():
            self.conn.execute(query)
        self.conn.commit()

    def generate_wnba_seasons(self, start_year: int, end_year: int) -> List[str]:
        """Generate WNBA season strings (single year format)"""
        seasons = []
        current_year = datetime.now().year
        for year in range(start_year, min(end_year + 1, current_year + 1)):
            seasons.append(str(year))
        return seasons

    def safe_api_call(self, endpoint_func, **kwargs):
        """Safely call API endpoint with error handling and retries"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                time.sleep(0.6)  # Rate limiting
                result = endpoint_func(**kwargs)
                return result
            except Exception as e:
                self.logger.warning(f"API call failed (attempt {attempt + 1}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)
                else:
                    self.logger.error(f"API call failed after {max_retries} attempts: {e}")
                    return None

    def check_existing_data(self, table_name: str, **conditions) -> bool:
        """Check if data already exists in database"""
        try:
            where_clause = " AND ".join([f"{key} = ?" for key in conditions.keys()])
            query = f"SELECT COUNT(*) FROM {table_name} WHERE {where_clause}"
            cursor = self.conn.execute(query, list(conditions.values()))
            count = cursor.fetchone()[0]
            return count > 0
        except Exception:
            return False

    def collect_players_data(self):
        """Collect WNBA players data for all seasons"""
        self.logger.info("Collecting players data...")

        for season in self.seasons:
            try:
                existing_count = self.conn.execute("SELECT COUNT(*) FROM players WHERE season = ?", (season,)).fetchone()[0]
                if existing_count > 0:
                    self.logger.info(f"Players data for season {season} already exists ({existing_count} records), skipping")
                    continue

                players = self.safe_api_call(
                    commonallplayers.CommonAllPlayers,
                    league_id=self.wnba_league_id,
                    season=season,
                    is_only_current_season=0
                )

                if players:
                    df = players.get_data_frames()[0]
                    new_records = 0
                    for _, row in df.iterrows():
                        if not self.check_existing_data('players', player_id=row.get('PERSON_ID'), season=season):
                            self.conn.execute('''
                                INSERT INTO players
                                (player_id, season, player_name, team_id, from_year, to_year, position, height, weight, college, country)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                            ''', (
                                row.get('PERSON_ID'), season, row.get('DISPLAY_FIRST_LAST'),
                                row.get('TEAM_ID'), row.get('FROM_YEAR'), row.get('TO_YEAR'),
                                row.get('POSITION'), row.get('HEIGHT'), row.get('WEIGHT'),
                                row.get('SCHOOL'), row.get('COUNTRY')
                            ))
                            new_records += 1

                    self.conn.commit()
                    self.logger.info(f"Added {new_records} new player records for season {season}")

            except Exception as e:
                self.logger.error(f"Error collecting players for season {season}: {e}")

    def collect_comprehensive_games_data(self):
        """Collect comprehensive games data from multiple sources"""
        self.logger.info("Collecting comprehensive games data...")

        # 1. Collect from LeagueGameFinder (30,000+ records!)
        try:
            existing_count = self.conn.execute("SELECT COUNT(*) FROM games_finder").fetchone()[0]
            if existing_count == 0:
                self.logger.info("Collecting from LeagueGameFinder...")
                games_finder = self.safe_api_call(leaguegamefinder.LeagueGameFinder)

                if games_finder:
                    df = games_finder.get_data_frames()[0]
                    new_records = 0

                    self.logger.info(f"Processing {len(df)} game records from LeagueGameFinder...")

                    for _, row in df.iterrows():
                        game_id = row.get('GAME_ID')
                        team_id = row.get('TEAM_ID')

                        if game_id and team_id and not self.check_existing_data('games_finder', game_id=game_id, team_id=team_id):
                            self.conn.execute('''
                                INSERT INTO games_finder
                                (season_id, team_id, team_abbreviation, team_name, game_id, game_date, matchup, wl,
                                 min, pts, fgm, fga, fg_pct, fg3m, fg3a, fg3_pct, ftm, fta, ft_pct,
                                 oreb, dreb, reb, ast, stl, blk, tov, pf, plus_minus)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                            ''', (
                                row.get('SEASON_ID'), row.get('TEAM_ID'), row.get('TEAM_ABBREVIATION'),
                                row.get('TEAM_NAME'), row.get('GAME_ID'), row.get('GAME_DATE'),
                                row.get('MATCHUP'), row.get('WL'), row.get('MIN'),
                                row.get('PTS'), row.get('FGM'), row.get('FGA'), row.get('FG_PCT'),
                                row.get('FG3M'), row.get('FG3A'), row.get('FG3_PCT'),
                                row.get('FTM'), row.get('FTA'), row.get('FT_PCT'),
                                row.get('OREB'), row.get('DREB'), row.get('REB'),
                                row.get('AST'), row.get('STL'), row.get('BLK'),
                                row.get('TOV'), row.get('PF'), row.get('PLUS_MINUS')
                            ))
                            new_records += 1

                            if new_records % 1000 == 0:
                                self.conn.commit()
                                self.logger.info(f"Processed {new_records} records...")

                    self.conn.commit()
                    self.logger.info(f"Added {new_records} new game finder records")
            else:
                self.logger.info(f"Games finder data already exists ({existing_count} records), skipping")

        except Exception as e:
            self.logger.error(f"Error collecting games from finder: {e}")

        # 2. Collect from LeagueGameLog (2,460+ records!)
        try:
            existing_count = self.conn.execute("SELECT COUNT(*) FROM games_log").fetchone()[0]
            if existing_count == 0:
                self.logger.info("Collecting from LeagueGameLog...")
                games_log = self.safe_api_call(leaguegamelog.LeagueGameLog)

                if games_log:
                    df = games_log.get_data_frames()[0]
                    new_records = 0

                    self.logger.info(f"Processing {len(df)} game log records from LeagueGameLog...")

                    for _, row in df.iterrows():
                        game_id = row.get('GAME_ID')
                        team_id = row.get('TEAM_ID')

                        if game_id and team_id and not self.check_existing_data('games_log', game_id=game_id, team_id=team_id):
                            self.conn.execute('''
                                INSERT INTO games_log
                                (season_id, team_id, team_abbreviation, team_name, game_id, game_date, matchup, wl,
                                 min, pts, fgm, fga, fg_pct, fg3m, fg3a, fg3_pct, ftm, fta, ft_pct,
                                 oreb, dreb, reb, ast, stl, blk, tov, pf, plus_minus)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                            ''', (
                                row.get('SEASON_ID'), row.get('TEAM_ID'), row.get('TEAM_ABBREVIATION'),
                                row.get('TEAM_NAME'), row.get('GAME_ID'), row.get('GAME_DATE'),
                                row.get('MATCHUP'), row.get('WL'), row.get('MIN'),
                                row.get('PTS'), row.get('FGM'), row.get('FGA'), row.get('FG_PCT'),
                                row.get('FG3M'), row.get('FG3A'), row.get('FG3_PCT'),
                                row.get('FTM'), row.get('FTA'), row.get('FT_PCT'),
                                row.get('OREB'), row.get('DREB'), row.get('REB'),
                                row.get('AST'), row.get('STL'), row.get('BLK'),
                                row.get('TOV'), row.get('PF'), row.get('PLUS_MINUS')
                            ))
                            new_records += 1

                            if new_records % 500 == 0:
                                self.conn.commit()
                                self.logger.info(f"Processed {new_records} records...")

                    self.conn.commit()
                    self.logger.info(f"Added {new_records} new game log records")
            else:
                self.logger.info(f"Games log data already exists ({existing_count} records), skipping")

        except Exception as e:
            self.logger.error(f"Error collecting games from log: {e}")

    def collect_ultimate_all_data(self):
        """Collect ALL available WNBA data using every verified working endpoint"""
        self.logger.info("🏀 Starting ULTIMATE WNBA data collection with ALL verified endpoints! 🏀")

        # Phase 1: Core Data Collection
        self.logger.info("Phase 1: Core Data Collection")
        self.collect_players_data()                    # Players for all seasons
        self.collect_comprehensive_games_data()        # 30,000+ game records from multiple sources!

        # Phase 2: Statistics Collection
        self.logger.info("Phase 2: Statistics Collection")
        self.collect_league_leaders()                  # Leaders for each season (157+ per season)
        self.collect_comprehensive_team_stats()        # Team stats with multiple measure types
        self.collect_comprehensive_player_stats()      # Player stats with multiple measure types

        # Phase 3: Advanced Analytics
        self.logger.info("Phase 3: Advanced Analytics")
        self.collect_comprehensive_clutch_stats()      # Clutch stats with different time periods
        self.collect_comprehensive_tracking_stats()    # Player tracking with all measure types

        # Phase 4: Shot Data
        self.logger.info("Phase 4: Shot Data Collection")
        self.collect_shot_locations()                  # Shot location data
        self.collect_shot_chart_data()                 # Detailed shot chart data

        # Phase 5: Team Analytics
        self.logger.info("Phase 5: Team Analytics")
        self.collect_lineups_data()                    # Lineup combinations

        # Phase 6: Game Details
        self.logger.info("Phase 6: Game Details")
        self.collect_comprehensive_boxscores()         # All boxscore types
        self.collect_scoreboard_data()                 # Additional games and teams

        self.logger.info("🎉 ULTIMATE WNBA data collection completed! 🎉")

    def collect_league_leaders(self):
        """Collect league leaders for all seasons"""
        self.logger.info("Collecting league leaders...")

        for season in self.seasons:
            try:
                existing_count = self.conn.execute("SELECT COUNT(*) FROM league_leaders WHERE season = ?", (season,)).fetchone()[0]
                if existing_count > 0:
                    self.logger.info(f"League leaders for season {season} already exists ({existing_count} records), skipping")
                    continue

                leaders = self.safe_api_call(
                    leagueleaders.LeagueLeaders,
                    league_id=self.wnba_league_id,
                    season=season
                )

                if leaders:
                    df = leaders.get_data_frames()[0]
                    new_records = 0

                    for _, row in df.iterrows():
                        if not self.check_existing_data('league_leaders', player_id=row.get('PLAYER_ID'), season=season):
                            self.conn.execute('''
                                INSERT INTO league_leaders
                                (season, player_id, rank_num, player_name, team_id, team_name, gp, min, pts)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                            ''', (
                                season, row.get('PLAYER_ID'), row.get('RANK'),
                                row.get('PLAYER'), row.get('TEAM_ID'), row.get('TEAM'),
                                row.get('GP'), row.get('MIN'), row.get('PTS')
                            ))
                            new_records += 1

                    self.conn.commit()
                    self.logger.info(f"Added {new_records} new league leader records for season {season}")

            except Exception as e:
                self.logger.error(f"Error collecting league leaders for season {season}: {e}")

    def collect_comprehensive_team_stats(self):
        """Collect team statistics with multiple measure types and per modes"""
        self.logger.info("Collecting comprehensive team stats...")

        measure_types = ["Base", "Advanced", "Misc", "Four Factors", "Scoring", "Opponent", "Usage"]
        per_modes = ["Totals", "PerGame", "Per36"]

        for season in self.seasons:
            for measure_type in measure_types:
                for per_mode in per_modes:
                    try:
                        existing_count = self.conn.execute(
                            "SELECT COUNT(*) FROM team_stats WHERE season = ? AND measure_type = ? AND per_mode = ?",
                            (season, measure_type, per_mode)
                        ).fetchone()[0]

                        if existing_count > 0:
                            continue

                        team_stats = self.safe_api_call(
                            leaguedashteamstats.LeagueDashTeamStats,
                            season=season,
                            season_type_all_star='Regular Season',
                            measure_type_detailed_defense=measure_type,
                            per_mode_detailed=per_mode
                        )

                        if team_stats:
                            df = team_stats.get_data_frames()[0]
                            new_records = 0
                            for _, row in df.iterrows():
                                if not self.check_existing_data('team_stats', team_id=row.get('TEAM_ID'), season=season, measure_type=measure_type, per_mode=per_mode):
                                    self.conn.execute('''
                                        INSERT INTO team_stats
                                        (team_id, season, team_name, measure_type, per_mode, gp, w, l, w_pct, min, pts, fgm, fga, fg_pct,
                                         fg3m, fg3a, fg3_pct, ftm, fta, ft_pct, oreb, dreb, reb, ast, tov, stl, blk, pf)
                                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                                    ''', (
                                        row.get('TEAM_ID'), season, row.get('TEAM_NAME'), measure_type, per_mode,
                                        row.get('GP'), row.get('W'), row.get('L'), row.get('W_PCT'),
                                        row.get('MIN'), row.get('PTS'), row.get('FGM'), row.get('FGA'), row.get('FG_PCT'),
                                        row.get('FG3M'), row.get('FG3A'), row.get('FG3_PCT'),
                                        row.get('FTM'), row.get('FTA'), row.get('FT_PCT'),
                                        row.get('OREB'), row.get('DREB'), row.get('REB'),
                                        row.get('AST'), row.get('TOV'), row.get('STL'), row.get('BLK'), row.get('PF')
                                    ))
                                    new_records += 1

                            if new_records > 0:
                                self.conn.commit()
                                self.logger.info(f"Added {new_records} team stat records for season {season}, {measure_type}, {per_mode}")

                    except Exception as e:
                        self.logger.error(f"Error collecting team stats for season {season}, {measure_type}, {per_mode}: {e}")

    def export_to_csv(self):
        """Export all data to CSV files"""
        self.logger.info("Exporting data to CSV files...")

        os.makedirs(self.data_dir, exist_ok=True)

        tables = ['players', 'games_finder', 'games_log', 'league_leaders', 'team_stats',
                 'player_stats', 'player_clutch_stats', 'team_clutch_stats', 'player_tracking_stats',
                 'boxscore_traditional', 'boxscore_advanced', 'shot_chart_data', 'player_shot_locations',
                 'lineups', 'scoreboard_games', 'scoreboard_teams']

        for table in tables:
            try:
                df = pd.read_sql_query(f"SELECT * FROM {table}", self.conn)
                if not df.empty:
                    csv_path = os.path.join(self.data_dir, f"{table}.csv")
                    df.to_csv(csv_path, index=False)
                    self.logger.info(f"Exported {len(df)} records to {csv_path}")
                else:
                    self.logger.warning(f"No data found for table {table}")
            except Exception as e:
                self.logger.error(f"Error exporting {table} to CSV: {e}")

    def get_ultimate_summary(self):
        """Get comprehensive summary of all collected data"""
        summary = {}
        tables = ['players', 'games_finder', 'games_log', 'league_leaders', 'team_stats',
                 'player_stats', 'player_clutch_stats', 'team_clutch_stats', 'player_tracking_stats',
                 'boxscore_traditional', 'boxscore_advanced', 'shot_chart_data', 'player_shot_locations',
                 'lineups', 'scoreboard_games', 'scoreboard_teams']

        for table in tables:
            try:
                count = self.conn.execute(f"SELECT COUNT(*) FROM {table}").fetchone()[0]
                summary[table] = count
            except Exception:
                summary[table] = 0

        return summary

    def print_ultimate_summary(self):
        """Print comprehensive collection summary"""
        summary = self.get_ultimate_summary()

        print("\n" + "=" * 100)
        print("🏀 ULTIMATE WNBA DATA COLLECTION SUMMARY 🏀")
        print("=" * 100)

        total_records = 0
        for table, count in summary.items():
            print(f"{table.replace('_', ' ').title()}: {count:,} records")
            total_records += count

        print(f"\nTotal Records Collected: {total_records:,}")
        print(f"Seasons Covered: {', '.join(self.seasons)} ({len(self.seasons)} seasons)")
        print(f"Database: wnba_ultimate.db")
        print(f"CSV Files: {self.data_dir}")
        print("=" * 100)

        # Highlight key achievements
        print("\n🎯 KEY DATA HIGHLIGHTS:")
        if summary.get('games_finder', 0) > 0:
            print(f"• {summary['games_finder']:,} game records from LeagueGameFinder")
        if summary.get('games_log', 0) > 0:
            print(f"• {summary['games_log']:,} game log records from LeagueGameLog")
        if summary.get('league_leaders', 0) > 0:
            print(f"• {summary['league_leaders']:,} league leader records")
        if summary.get('players', 0) > 0:
            print(f"• {summary['players']:,} player records across all seasons")
        if summary.get('team_stats', 0) > 0:
            print(f"• {summary['team_stats']:,} team statistics records")
        if summary.get('player_stats', 0) > 0:
            print(f"• {summary['player_stats']:,} player statistics records")
        if summary.get('boxscore_traditional', 0) > 0:
            print(f"• {summary['boxscore_traditional']:,} traditional boxscore records")
        if summary.get('shot_chart_data', 0) > 0:
            print(f"• {summary['shot_chart_data']:,} shot chart data points")

        print("\n📊 COMPREHENSIVE WNBA DATABASE CREATED!")
        print("This database contains the most complete WNBA dataset from 2015-2025!")

    def close(self):
        """Close database connection"""
        if hasattr(self, 'conn'):
            self.conn.close()
            self.logger.info("Database connection closed")


if __name__ == "__main__":
    collector = WNBAUltimateCollector()

    try:
        print("🏀 STARTING ULTIMATE WNBA DATA COLLECTION 🏀")
        print("=" * 100)
        print("Collecting COMPREHENSIVE WNBA data from 2015 to July 17, 2025")
        print("Using ALL verified working endpoints with correct parameters!")
        print(f"Seasons: {', '.join(collector.seasons)} ({len(collector.seasons)} seasons)")
        print(f"Database: wnba_ultimate.db")
        print(f"Data directory: {collector.data_dir}")
        print("=" * 100)

        # Start the ultimate collection
        collector.collect_ultimate_all_data()
        collector.export_to_csv()

        print("\n" + "=" * 100)
        print("🎉 ULTIMATE COLLECTION COMPLETED SUCCESSFULLY! 🎉")
        print("=" * 100)

        collector.print_ultimate_summary()

        print("\n🚀 WHAT YOU NOW HAVE:")
        print("• Complete WNBA player database (2015-2025)")
        print("• 30,000+ game records from multiple sources")
        print("• Comprehensive team and player statistics")
        print("• Advanced analytics (clutch stats, tracking data)")
        print("• Shot chart and location data")
        print("• Lineup combinations and team chemistry")
        print("• Detailed boxscore information")
        print("• Historical trends and performance data")
        print("• Ready-to-analyze CSV files")
        print("• SQLite database for complex queries")

    except KeyboardInterrupt:
        print("\nCollection interrupted by user")
    except Exception as e:
        print(f"Collection failed: {e}")
        traceback.print_exc()
    finally:
        collector.close()
